import NextAuth from 'next-auth';
import Cred<PERSON><PERSON><PERSON>rovider from 'next-auth/providers/credentials';
import { USER_NOT_CONFIRMED_EXCEPTION } from '@/utils/constants';
import { setCookiesForUnverifiedUser } from '@/containers/auth/utility';
import { initiateSignIn, parseIdToken } from '@/containers/auth/cognito';

export const authOptions = {
  secret: process.env.NEXTAUTH_SECRET,
  providers: [
    CredentialsProvider({
      id: 'cognito',
      name: 'CognitoLogin',
      credentials: {
        email: { label: 'Email', type: 'text' },
        password: { label: 'Password', type: 'text' },
      },
      async authorize(credentials) {
        // console.log('AUTHORIZE CALL BACK', credentials);
        // This function is called
        const { email, password } = credentials ?? {};

        // Check to ensure all required credentials were passed in
        if (!password || !email) return null;

        const [signInResult, signInError] = await initiateSignIn({ username: email, password });
        if (signInError?.name === USER_NOT_CONFIRMED_EXCEPTION) {
          setCookiesForUnverifiedUser({ email });
          throw signInError.toNodeError();
        } else if (signInError) {
          throw signInError.toNodeError();
        }
        const userInfo = parseIdToken(signInResult.AuthenticationResult.IdToken);
        // console.log('result', signInResult);
        return userInfo;
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    // Seconds - How long until an idle session expires and is no longer valid.
    maxAge: 2 * 60 * 60, // 2 hours
  },
  debug: process.env.NODE_ENV !== 'production',
  callbacks: {
    async session({ session, user, token }) {
      // Add CAMBIAN ID, FIRST NAME, LAST NAME, and possibly profile link to the picture
      session.user = {
        email: token.email ?? null,
        firstName: token.given_name ?? null,
        lastName: token.family_name ?? null,
        cambianId: token.cambianId ?? null,
        patientId: token.patientId ?? null,
        idToken: token.idToken ?? null,
      };

      return session;
    },
    async jwt({ token, user, account, profile, isNewUser }) {
      if (user) {
        token.email = user.email;
        token.given_name = user.given_name;
        token.family_name = user.family_name;
        token.cambianId = user.cambianId;
        token.patientId = user.patientId;
        token.idToken = user.idToken;
      }

      return token;
    },
  },
  pages: {
    signIn: '/',
    // // signOut: '/signout',
    // error: '/error',
    // verifyRequest: '/verify-request',
    // newUser: '/signup'
  },
};

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };
