export const GET_CONSENT_AGREEMENT_REQUEST = 'GET_CONSENT_AGREEMENT_REQUEST';
export const GET_CONSENT_AGREEMENT_SUCCESS = 'GET_CONSENT_AGREEMENT_SUCCESS';
export const GET_CONSENT_AGREEMENT_ERROR = 'GET_CONSENT_AGREEMENT_ERROR';
export const GET_CONSENT_AGREEMENT_RESET = 'GET_CONSENT_AGREEMENT_RESET';

export function fetchConsentAgreement(data) {
  return {
    type: GET_CONSENT_AGREEMENT_REQUEST,
    payload: data,
  };
}

export function fetchConsentAgreementSuccess(data) {
  return {
    type: GET_CONSENT_AGREEMENT_SUCCESS,
    payload: data,
  };
}

export function fetchConsentAgreementError() {
  return {
    type: GET_CONSENT_AGREEMENT_ERROR,
  };
}

export function fetchConsentAgreementReset() {
  return {
    type: GET_CONSENT_AGREEMENT_RESET,
  };
}
