import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/getQuestionnaireDetails/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { fetchQuestionnaireQuestions } from './index';
/**
 * This function test test case for get search location details saga
 * Fires get search location success of api gives success
 * Fires get search location error of api fails
 */

describe('get questionnaire', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiFetchQuestionnaireQuestions = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchQuestionnaireQuestions(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchQuestionnaireQuestions, requestResult).done;

    let successResult = actions.fetchQuestionnaireQuestionsSuccess(DUMMY_ITEM.data);

    expect(api.makeApiFetchQuestionnaireQuestions.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchQuestionnaireQuestions = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchQuestionnaireQuestions(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchQuestionnaireQuestions, requestResult).done;

    expect(api.makeApiFetchQuestionnaireQuestions.mock.calls.length).toBe(1);

    let errorResult = actions.fetchQuestionnaireQuestionsError();
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchQuestionnaireQuestions = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchQuestionnaireQuestions(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchQuestionnaireQuestions, requestResult).done;

    expect(api.makeApiFetchQuestionnaireQuestions.mock.calls.length).toBe(1);

    let resetResult = actions.fetchQuestionnaireQuestionsReset();
    const expectedAction = {
      type: actions.GET_QUESTIONNAIRE_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
