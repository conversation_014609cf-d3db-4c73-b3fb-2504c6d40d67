/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_REQUEST,
  UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_SUCCESS,
  UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_ERROR,
} from '../../../redux/actions/updateSaveLiteQuestionnaireResponse';

import { makeApiFetchUpdateSaveLiteQuestionnaireResponse } from './api';

/**
 *
 * @param {*} action
 */

export function* fetchUpdateSaveLiteQuestionnaireResponse(action) {
  try {
    const response = yield call(makeApiFetchUpdateSaveLiteQuestionnaireResponse, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_SUCCESS, payload: data });
    } else {
      yield put({ type: UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchUpdateSaveLiteQuestionnaireResponse() {
  yield takeEvery(UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_REQUEST, fetchUpdateSaveLiteQuestionnaireResponse);
}
