'server only';

import { cookies } from 'next/headers';
import { getServerSession } from 'next-auth/next';
import { INDIVIDUAL, NETWORK, ORGANIZATION, MACHINE_ACCESS_TOKEN } from '../utils/constants';
import { authOptions } from '@/app/api/auth/(cognito)/[...nextauth]/route';

const getAccessTokenFromCookie = async (env) => {
  const cookieStore = cookies();
  const accessToken = cookieStore.get(`${MACHINE_ACCESS_TOKEN(env)}`)?.value;
  return accessToken;
};

export const getMachineAccessToken = async (targetAwsEnv = ORGANIZATION) => {
  try {
    if (targetAwsEnv !== NETWORK && targetAwsEnv !== ORGANIZATION && targetAwsEnv !== INDIVIDUAL) {
      throw new Error('env should be network, organization, or individual');
    }

    let accessToken = await getAccessTokenFromCookie(targetAwsEnv);

    if (!accessToken) {
      let baseUrl = process.env.NODE_ENV !== 'production' ? 'http://localhost:3050' : process.env.NEXT_PUBLIC_BASE_URL;
      const res = await fetch(`${baseUrl}/api/auth/machineToken/${targetAwsEnv}`, {
        cache: 'no-store',
      });
      console.log(res);
      if (!res.ok) {
        console.log('Fetching access token failed with status', res.status);
        throw res;
      }
      const data = await res.json();
      accessToken = data.accessToken;
    }

    return accessToken;
  } catch (e) {
    console.log('get machine access token failed', e, targetAwsEnv);
    throw e;
  }
};

export const getUserTokenAndUpdatedUrl = async (url, authOptions) => {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return [null, null];
    }

    console.log(session, 'session');
    const { idToken } = session?.user || {};

    if (!idToken) {
      throw new Error('user id token does not exist in session.');
    }

    return [url, idToken];
  } catch (error) {
    console.log('getUserTokenAndUpdatedUrl failed', error);
    throw error;
  }
};

/**
 * Makes a network call with the given configuration.
 *
 * @param {Object} config - The configuration object for the network call.
 * @param {string} config.url - The URL for the API call.
 * @param {string} config.method - The HTTP method (e.g., 'GET', 'POST').
 * @param {Object} [config.headers] - Optional headers for the request.
 * @param {*} [config.data] - The body payload for the request.
 * @param {boolean} [config.formData=false] - Whether the request body is formData.
 * @param {string} config.targetAwsEnv - The target AWS environment (e.g., 'INDIVIDUAL').
 */
export async function makeServerFetchNetworkCall(config) {
  let url = config.url;

  const { formData = false } = config;
  const headers = {
    ...config.headers,
  };

  try {
    const machineAccessToken = await getMachineAccessToken(config.targetAwsEnv);
    headers.Authorization = `Bearer ${machineAccessToken}`;

    if (config.targetAwsEnv === INDIVIDUAL) {
      const [newUrl, userIdToken] = await getUserTokenAndUpdatedUrl(url, authOptions);
      if (newUrl) {
        url = newUrl;
      }
      // Only adds the token if the individual human user is signed in.
      if (userIdToken) {
        headers['Cambian-User-Token'] = `Bearer ${userIdToken}`;
      }
    }
  } catch (err) {
    console.log(err);
    console.log('Adding machine token or user token failed');
    throw err;
  }

  const params = {
    method: config.method,
    headers: headers,
    body: formData ? config.data : JSON.stringify(config.data),
    timeoutInterval: 10000,
  };

  return fetch(url, params);
}
