import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/rescheduleAppointment/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { rescheduleAppointment } from './index';

/**
 * This function test test case for get rescheduleAppointment details saga
 * Fires get rescheduleAppointment success of api gives success
 * Fires get rescheduleAppointment error of api fails
 */

describe('rescheduleAppointment', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.rescheduleAppointmentApi = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.rescheduleAppointmentDetails(DUMMY_ITEM.data);
    await runSaga(fakeStore, rescheduleAppointment, requestResult).done;

    let successResult = actions.rescheduleAppointmentActionSuccess(DUMMY_ITEM.data);

    expect(api.rescheduleAppointmentApi.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.rescheduleAppointmentApi = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.rescheduleAppointmentDetails(DUMMY_ITEM.data);
    await runSaga(fakeStore, rescheduleAppointment, requestResult).done;

    expect(api.rescheduleAppointmentApi.mock.calls.length).toBe(1);

    let errorResult = actions.rescheduleAppointmentActionError();
    expect(dispatchedActions).toContainEqual(errorResult);
  });
});
