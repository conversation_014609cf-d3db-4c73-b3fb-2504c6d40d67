// import { adminDeleteUser, adminGetUser, getAttributeValue, handler, signUp } from '@/containers/auth';
// import { initiateSignInWithCredentials } from '@/containers/auth/cognito';
// import { setCookiesForUnverifiedUser } from '@/containers/auth/utility';
// import {
//   FIRST_NAME_COGNITO_ATTRIBUTE_NAME,
//   LAST_NAME_COGNITO_ATTRIBUTE_NAME,
//   UNVERIFIED_USER_DETAILS,
//   USER_NOT_CONFIRMED_EXCEPTION,
// } from '@/utils/constants';
// import { NextResponse } from 'next/server';

// **It's not being used right now, but it will probably be needed later. For that, we need to create an IAM user for Widget.**
// /**
//  *
//  * There were 2 options in implmenting this and decided on Option 2.
//  *
//  * Option 1:
//  * a. AdminInitiateAuth
//  * b. AdminUpdateAttribute
//  * c. VerifyUserAttribute
//  * d. AdminConfirmSignUp
//  * The frontend code will need to call b) and c) depending on if the email has changed in the Verification Page for resending code and verifiying the code.
//  * It will differentiate that with a cookie value like "isEmailChangePending"
//  *
//  * Option 2:
//  * a. AdminInitiateAuth
//  * b. AdminGetUser (See if an user with the newEmail exists)
//  * c. AdminDeleteUser
//  * d. AdminGetUser (Invoked only when the user tries to sign in with unconfirmed user credentials, they won't have firstName and lastName in their cookie header)
//  * e. SignUpUser
//  * Will need to save firstname nad lastname in the cookie during initial sign up to be used for signing up another user.
//  *
//  * @param {Request} req
//  * @returns {Promise<void>}
//  */
// const postEndpoint = async (req) => {
//   const { email, password, newEmail } = await req.json();

//   if (!newEmail || !email || !password) {
//     return NextResponse.json(
//       {
//         message: 'new email, email, and password must be provided',
//       },
//       {
//         status: 400,
//       },
//     );
//   }

//   if (newEmail === email) {
//     return NextResponse.json(
//       {
//         message: 'A different email must be provided than your current one.',
//       },
//       {
//         status: 400,
//       },
//     );
//   }

//   const [_signInResult, signInError] = await initiateSignInWithCredentials({ username: email, password });
//   if (signInError && signInError.name !== USER_NOT_CONFIRMED_EXCEPTION) {
//     return signInError.toNextResponse();
//   } else if (!signInError) {
//     return NextResponse.json({ message: 'user is already confirmed', errorName: '' }, { status: 400 });
//   }
//   // Checks if the new email is being used by someone else already.
//   // AccessToken is not available, thus, need to get a user using AdminGetUserCommand and not GetUserCommand.
//   const [_adminGetUserWithNewEmailResult, adminGetUserWithNewEmailError] = await adminGetUser({ username: newEmail });
//   if (adminGetUserWithNewEmailError?.name !== 'UserNotFoundException') {
//     return NextResponse.json({ message: 'The new email is already being used', errorName: '' }, { status: 400 });
//   }

//   const [adminGetUserWithCurrentEmailResult, adminGetUserWithCurrentEmailError] = await adminGetUser({
//     username: email,
//   });
//   if (adminGetUserWithCurrentEmailError) {
//     return NextResponse.json({ message: `Could not get the current user...${email}`, errorName: '' }, { status: 400 });
//   }

//   // Needs AdminDeleteUser and not DeletUser.
//   const [_adminDeleteUserResult, adminDeleteUserError] = await adminDeleteUser({ username: email });
//   if (adminDeleteUserError) {
//     return adminDeleteUserError.toNextResponse();
//   }

//   const userDetails = req.cookies.get(UNVERIFIED_USER_DETAILS)?.value;
//   let { firstName, lastName } = JSON.parse(userDetails ?? null) ?? {};

//   if (!firstName || !lastName) {
//     // set firstName and last name
//     const userAttributes = adminGetUserWithCurrentEmailResult.userAttributes;
//     firstName = getAttributeValue(userAttributes, FIRST_NAME_COGNITO_ATTRIBUTE_NAME);
//     lastName = getAttributeValue(userAttributes, LAST_NAME_COGNITO_ATTRIBUTE_NAME);
//   }
//   const [signUpResult, signUpError] = await signUp({ email: newEmail, password, firstName, lastName });
//   if (signUpError) {
//     return signUpError.toNextResponse();
//   }

//   setCookiesForUnverifiedUser({ email: newEmail, firstName, lastName });
//   return NextResponse.json({}, { status: signUpResult.status });
// };

// const middleware1 = (_req, next) => {
//   console.log('middleware 1');
//   next();
// };

// export const POST = handler(middleware1, postEndpoint);
