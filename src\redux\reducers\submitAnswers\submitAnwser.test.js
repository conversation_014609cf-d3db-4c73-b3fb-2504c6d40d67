import { POST_REGISTRATION_QUESTIONS_REQUEST, POST_REGISTRATION_QUESTIONS_SUCCESS } from '../../actions/submitAnswers';
import * as GLOBALS from '../globals';
import { submitAnswersReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isSubmitAnswersFetching: true,
  isSubmitAnswersSuccess: false,
  isSubmitAnswersError: false,
  submitAnswersErrorData: null,
};

const successState = {
  isSubmitAnswersFetching: false,
  isSubmitAnswersSuccess: true,
  isSubmitAnswersError: false,
  submitAnswersErrorData: null,
  submitAnswersSuccessData: data,
};

describe('Submit Answer Reducer', () => {
  it('should return the initial state', () => {
    expect(submitAnswersReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle POST_REGISTRATION_QUESTIONS_REQUEST', () => {
    expect(
      submitAnswersReducer(initialState, {
        type: POST_REGISTRATION_QUESTIONS_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle POST_REGISTRATION_QUESTIONS_SUCCESS', () => {
    expect(
      submitAnswersReducer(initialState, {
        type: POST_REGISTRATION_QUESTIONS_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });
});
