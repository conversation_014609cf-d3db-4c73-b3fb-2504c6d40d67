# base-template.yaml
AWSTemplateFormatVersion: '2010-09-09'
Description: Base template for Next.js application deployment with ECS Fargate using ECR + ECS

Parameters:
  TestPrefix:
    Type: String
    Description: TestPrefix for all resources and SSM parameters
    Default: ''
  Env:
    Type: String
    Default: 'dev'
    Description: A string value for the environment where the resource will be located.
  ApplicationName:
    Type: String
    Description: Specific application name for resource identification
    AllowedPattern: '[a-zA-Z0-9-]+'
    ConstraintDescription: Must contain only letters, numbers, and hyphens
  VisibilityEnv:
    Type: String
    Description: Visibility environment (Individual/Organization/Network)
    Default: Organization
    AllowedValues:
      - Individual
      - Organization
      - Network
  EcrImageUri:
    Type: String
    Description: URI of the ECR image to deploy
  DesiredCount:
    Type: Number
    Default: 2
    Description: Desired number of containers
    MinValue: 1
    MaxValue: 10
  ContainerPort:
    Type: Number
    Default: 3000
    Description: Container port for the application
  ContainerCpu:
    Type: Number
    Default: 512
    Description: CPU units for the container
  ContainerMemory:
    Type: Number
    Default: 1024
    Description: Memory for the container in MB
  HealthCheckPath:
    Type: String
    Default: /api/health
    Description: Health check path for the application
  VPCID:
    Type: AWS::SSM::Parameter::Value<String>
    Default: '/devCS/Organization/CF/VPCID'
  PublicSubnet1:
    Type: AWS::SSM::Parameter::Value<String>
    Default: '/devCS/Organization/CF/PublicSubnet1'
  PublicSubnet2:
    Type: AWS::SSM::Parameter::Value<String>
    Default: '/devCS/Organization/CF/PublicSubnet2'
  ACMCertificateArn:
    Type: String
    Default: '/devCS/Organization/CF/ACMCertificateARN'
  ExistingALBArn:
    Type: String
    Description: ARN of the existing Application Load Balancer
    Default: ''
  ExistingALBSecurityGroupId:
    Type: String
    Description: ID of the existing ALB Security Group
    Default: ''
  ExistingHTTPSListenerArn:
    Type: String
    Description: ARN of an existing HTTPS listener to use instead of creating a new one
    Default: ''
  AppEnvironmentFileS3Arn:
    Type: String
    Description: S3 ARN for the environment file
    Default: ''

Conditions:
  CreateNewALB: !Equals
    - !Ref ExistingALBArn
    - ''
  CreateNewALBSecurityGroup: !Equals
    - !Ref ExistingALBSecurityGroupId
    - ''
  CreateNewListener: !Equals
    - !Ref ExistingHTTPSListenerArn
    - ''
  UseExistingListener: !Not
    - !Equals
      - !Ref ExistingHTTPSListenerArn
      - ''

Resources:
  ALBSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Condition: CreateNewALBSecurityGroup
    Properties:
      GroupDescription: !Sub '${Env}${ApplicationName}-alb-sg'
      VpcId: !Ref VPCID
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0
      SecurityGroupEgress:
        - IpProtocol: -1
          FromPort: -1
          ToPort: -1
          CidrIp: 0.0.0.0/0
      Tags:
        - Key: Project
          Value: selfscheduler
        - Key: Component
          Value: app
        - Key: Name
          Value: !Sub '${TestPrefix}${Env}${VisibilityEnv}${ApplicationName}ALBSecurityGroup'

  ApplicationLoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Condition: CreateNewALB
    Properties:
      Name: !Sub '${Env}${ApplicationName}-alb'
      Scheme: internet-facing
      SecurityGroups:
        - !If
          - CreateNewALBSecurityGroup
          - !Ref ALBSecurityGroup
          - !Ref ExistingALBSecurityGroupId
      Subnets:
        - !Ref PublicSubnet1
        - !Ref PublicSubnet2
      Tags:
        - Key: Project
          Value: selfscheduler
        - Key: Component
          Value: app
        - Key: Name
          Value: !Sub '${TestPrefix}${Env}${VisibilityEnv}${ApplicationName}ALB'

  ECSSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: !Sub '${Env}${ApplicationName}-ecs-sg'
      VpcId: !Ref VPCID
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: !Ref ContainerPort
          ToPort: !Ref ContainerPort
          SourceSecurityGroupId: !If
            - CreateNewALBSecurityGroup
            - !Ref ALBSecurityGroup
            - !Ref ExistingALBSecurityGroupId
      SecurityGroupEgress:
        - IpProtocol: -1
          FromPort: -1
          ToPort: -1
          CidrIp: 0.0.0.0/0
      Tags:
        - Key: Project
          Value: selfscheduler
        - Key: Component
          Value: app
        - Key: Name
          Value: !Sub '${TestPrefix}${Env}${VisibilityEnv}${ApplicationName}ECSServiceGroup'

  # Core ECS Resources
  ECSCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Sub '${Env}${ApplicationName}-cluster'
      Tags:
        - Key: Project
          Value: selfscheduler
        - Key: Component
          Value: app
        - Key: Application
          Value: !Ref ApplicationName
        - Key: Name
          Value: !Sub '${TestPrefix}${Env}${VisibilityEnv}${ApplicationName}Cluster'

  LogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/ecs/${Env}${ApplicationName}-cluster'
      RetentionInDays: 30
      Tags:
        - Key: Project
          Value: selfscheduler
        - Key: Component
          Value: app
        - Key: Application
          Value: !Ref ApplicationName
        - Key: Name
          Value: !Sub '${TestPrefix}${Env}${VisibilityEnv}${ApplicationName}LogGroup'

  TaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub '${Env}${ApplicationName}-task'
      RequiresCompatibilities:
        - FARGATE
      NetworkMode: awsvpc
      Cpu: !Ref ContainerCpu
      Memory: !Ref ContainerMemory
      ExecutionRoleArn: !GetAtt ECSExecutionRole.Arn
      TaskRoleArn: !GetAtt ECSTaskRole.Arn
      RuntimePlatform:
        OperatingSystemFamily: LINUX
        CpuArchitecture: ARM64
      ContainerDefinitions:
        - Name: !Ref ApplicationName
          Image: !Ref EcrImageUri
          PortMappings:
            - ContainerPort: !Ref ContainerPort
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref LogGroup
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: !Ref ApplicationName
          EnvironmentFiles:
            - Type: s3
              Value: !Ref AppEnvironmentFileS3Arn
      Tags:
        - Key: Project
          Value: selfscheduler
        - Key: Component
          Value: app
        - Key: Application
          Value: !Ref ApplicationName
        - Key: Name
          Value: !Sub '${TestPrefix}${Env}${VisibilityEnv}${ApplicationName}TaskDefinition'

  # IAM Roles
  ECSExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub '${Env}${ApplicationName}-execution-role'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy
      Policies:
        - PolicyName: S3AccessPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                Resource:
                  - arn:aws:s3:::*/*
      Tags:
        - Key: Project
          Value: selfscheduler
        - Key: Component
          Value: app
        - Key: Application
          Value: !Ref ApplicationName
        - Key: Name
          Value: !Sub '${TestPrefix}${Env}${VisibilityEnv}${ApplicationName}ECSExecutionRole'

  ECSTaskRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub '${Env}${ApplicationName}-task-role'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: S3AccessPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                Resource:
                  - arn:aws:s3:::*/*
      Tags:
        - Key: Project
          Value: selfscheduler
        - Key: Component
          Value: app
        - Key: Application
          Value: !Ref ApplicationName
        - Key: Name
          Value: !Sub '${TestPrefix}${Env}${VisibilityEnv}${ApplicationName}ECSTaskRole'

  TargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Sub '${Env}${ApplicationName}-tg'
      Port: !Ref ContainerPort
      Protocol: HTTP
      TargetType: ip
      VpcId: !Ref VPCID
      HealthCheckPath: !Ref HealthCheckPath
      HealthCheckPort: !Ref ContainerPort
      HealthCheckIntervalSeconds: 30
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 2
      UnhealthyThresholdCount: 3
      Tags:
        - Key: Project
          Value: selfscheduler
        - Key: Component
          Value: app
        - Key: Name
          Value: !Sub '${TestPrefix}${Env}${VisibilityEnv}${ApplicationName}TargetGroup'

  HTTPSListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Condition: CreateNewListener
    Properties:
      LoadBalancerArn: !If
        - CreateNewALB
        - !Ref ApplicationLoadBalancer
        - !Ref ExistingALBArn
      Port: 443
      Protocol: HTTPS
      Certificates:
        - CertificateArn: !Ref ACMCertificateArn
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref TargetGroup

  ListenerRule:
    Type: AWS::ElasticLoadBalancingV2::ListenerRule
    Condition: UseExistingListener
    Properties:
      ListenerArn: !Ref ExistingHTTPSListenerArn
      Priority: 1
      Conditions:
        - Field: path-pattern
          Values:
            - '/*'
      Actions:
        - Type: forward
          TargetGroupArn: !Ref TargetGroup

  ECSService:
    Type: AWS::ECS::Service
    Properties:
      ServiceName: !Sub '${Env}${ApplicationName}-service'
      Cluster: !Ref ECSCluster
      TaskDefinition: !Ref TaskDefinition
      DesiredCount: !Ref DesiredCount
      LaunchType: FARGATE
      HealthCheckGracePeriodSeconds: 120
      NetworkConfiguration:
        AwsvpcConfiguration:
          AssignPublicIp: ENABLED
          SecurityGroups:
            - !Ref ECSSecurityGroup
          Subnets:
            - !Ref PublicSubnet1
            - !Ref PublicSubnet2
      LoadBalancers:
        - ContainerName: !Ref ApplicationName
          ContainerPort: !Ref ContainerPort
          TargetGroupArn: !Ref TargetGroup
      Tags:
        - Key: Project
          Value: selfscheduler
        - Key: Component
          Value: app
        - Key: Application
          Value: !Ref ApplicationName
        - Key: Name
          Value: !Sub '${TestPrefix}${Env}${VisibilityEnv}${ApplicationName}ECSService'
        - Key: ListenerArn
          Value: !If
            - CreateNewListener
            - !Ref HTTPSListener
            - !Ref ListenerRule

Outputs:
  ServiceURL:
    Description: URL of the service (only shown for new ALB)
    Value: !If
      - CreateNewALB
      - !Sub ${ApplicationLoadBalancer.DNSName}
      - !Sub Check the Load balancer ${ExistingALBArn} for the domain name

  LogGroupName:
    Description: Name of CloudWatch Logs group
    Value: !Ref LogGroup

  ClusterName:
    Description: Name of the ECS cluster
    Value: !Ref ECSCluster

  ServiceName:
    Description: Name of the ECS service
    Value: !Ref ECSService

  LoadBalancerArn:
    Description: ARN of the Application Load Balancer
    Value: !If
      - CreateNewALB
      - !Ref ApplicationLoadBalancer
      - !Ref ExistingALBArn
