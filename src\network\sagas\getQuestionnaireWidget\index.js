import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  GET_QUESTIONNAIRE_WIDGET_ERROR,
  GET_QUESTIONNAIRE_WIDGET_SUCCESS,
  GET_QUESTIONNAIRE_WIDGET_REQUEST,
} from '../../../redux/actions/getQuestionnaireWidget';

import { makeApiFetchQuestionnaireWidget } from './api';

export function* getQuestionnaireWidget(action) {
  try {
    const response = yield call(makeApiFetchQuestionnaireWidget, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: GET_QUESTIONNAIRE_WIDGET_SUCCESS, payload: data });
    } else {
      yield put({ type: GET_QUESTIONNAIRE_WIDGET_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: GET_QUESTIONNAIRE_WIDGET_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchFetchQuestionnaireWidget() {
  yield takeEvery(GET_QUESTIONNAIRE_WIDGET_REQUEST, getQuestionnaireWidget);
}
