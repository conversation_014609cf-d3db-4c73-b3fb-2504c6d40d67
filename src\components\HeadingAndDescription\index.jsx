import React from 'react';
import { Paper, Grid, Button, Stack, Typography, Box } from '@mui/material';
import { actions } from '@/containers/commonConstants';
import { useTranslation } from 'react-i18next';

export const HeadingAndDescription = (props) => {
  const { handleNavigationCallback, headingAndDescriptionData, showNextButton = true } = props;
  const { t } = useTranslation();

  return (
    <Box sx={{ px: '4%', pt: '8px', pb: '48px' }}>
      <Box sx={{ pt: 3, height: '100%' }}>
        <Grid container>
          <Grid item xs={12} sx={{ mb: 2 }}>
            <Typography variant="h5"> {headingAndDescriptionData?.heading} </Typography>
          </Grid>
          <Grid item xs={12} justifyContent="center" alignItems="center">
            <Grid sx={{ minHeight: '300px' }}>
              <Stack>
                <Typography
                  variant="body"
                  dangerouslySetInnerHTML={{ __html: headingAndDescriptionData?.description }}
                ></Typography>
              </Stack>
            </Grid>
          </Grid>
          {showNextButton && (
            <Grid item xs={12} sx={{ mt: 4 }}>
              <Stack direction="column" alignItems="center" spacing={2} sx={{ pt: 0, px: 2, pb: 2 }}>
                <Button variant="contained" onClick={() => handleNavigationCallback(actions.NEXT)}>
                  {headingAndDescriptionData?.buttonText || t('next')}
                </Button>
              </Stack>
            </Grid>
          )}
        </Grid>
      </Box>
    </Box>
  );
};
