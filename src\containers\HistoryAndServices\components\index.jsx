import { Box, Divider } from '@mui/material';
import { History } from './History';
import { Services } from './Services';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { getServicesFromSearchAndCreatePatient, getEligibilityMessage } from '../utility';
import { formatDate } from '../../../utils/helpers/date-time';

export const HistoryAndServicesContainer = (props) => {
  const { handleNavigationCallback } = props;
  const { t } = useTranslation();
  const searchAndCreatePatientSuccess = useSelector(
    (state) => state.searchAndCreatePatientReducer.searchAndCreatePatientSuccess,
  );
  const searchAndCreatePatientSuccessData = useSelector(
    (state) => state.searchAndCreatePatientReducer.searchAndCreatePatientSuccessData,
  );

  const patientData = searchAndCreatePatientSuccess ? searchAndCreatePatientSuccessData : [];
  const services = getServicesFromSearchAndCreatePatient(patientData);
  const eligibilityMessages = getEligibilityMessage(patientData);

  return (
    <Box>
      <History
        patientData={patientData}
        searchAndCreatePatientSuccess={searchAndCreatePatientSuccess}
        searchAndCreatePatientSuccessData={searchAndCreatePatientSuccessData}
        handleNavigationCallback={handleNavigationCallback}
      />
      {eligibilityMessages?.length ? (
        <Box sx={{ mt: 4 }}>
          <strong>{t('serviceSuggestionText')}</strong>
          {eligibilityMessages?.map((eligibility, index) => (
            <ul key={index}>
              <li>{eligibility.doseType}</li>
              <li>{eligibility.doseName}</li>
              {eligibility.eligibleDate && <li>After {formatDate(eligibility.eligibleDate)}</li>}
            </ul>
          ))}
          <Divider orientation="horizontal" flexItem />
        </Box>
      ) : (
        ''
      )}
      <Services
        services={services}
        searchAndCreatePatientSuccess={searchAndCreatePatientSuccess}
        searchAndCreatePatientSuccessData={searchAndCreatePatientSuccessData}
        handleNavigationCallback={handleNavigationCallback}
      />
    </Box>
  );
};
