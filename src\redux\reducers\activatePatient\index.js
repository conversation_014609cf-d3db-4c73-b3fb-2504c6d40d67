import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  ACTIVATE_PATIENT_REQUEST,
  ACTIVATE_PATIENT_SUCCESS,
  ACTIVATE_PATIENT_ERROR,
  ACTIVATE_PATIENT_RESET,
} from '../../actions/activatePatient';
export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};
export const activatePatientReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case ACTIVATE_PATIENT_REQUEST:
      return {
        ...state,
        isActivatePatientFetching: true,
        activatePatientSuccess: false,
        activatePatientError: false,
        activatePatientErrorData: null,
      };
    case ACTIVATE_PATIENT_SUCCESS: {
      return {
        ...state,
        isActivatePatientFetching: false,
        activatePatientSuccess: true,
        activatePatientError: false,
        activatePatientErrorData: null,
        activatePatientSuccessData: payload,
      };
    }
    case ACTIVATE_PATIENT_ERROR:
      return {
        ...state,
        isActivatePatientFetching: false,
        activatePatientSuccess: false,
        activatePatientError: true,
        activatePatientErrorData: payload,
      };
    case ACTIVATE_PATIENT_RESET:
      return {
        ...state,
        isActivatePatientFetching: false,
        activatePatientSuccess: false,
        activatePatientError: false,
        activatePatientErrorData: null,
      };
    default:
      return state;
  }
};
