/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import {
  RESCHEDULE_APPOINTMENT_ERROR,
  RESCHEDULE_APPOINTMENT_REQUEST,
  RESCHEDULE_APPOINTMENT_SUCCESS,
  RESCHEDULE_APPOINTMENT_RESET,
} from '../../actions/rescheduleAppointment';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const rescheduleAppointmentReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case RESCHEDULE_APPOINTMENT_REQUEST:
      return {
        ...state,
        rescheduleAppointmentFetching: true,
        rescheduleAppointmentSuccess: false,
        rescheduleAppointmentError: false,
        rescheduleAppointmentErrorData: null,
      };
    case RESCHEDULE_APPOINTMENT_SUCCESS: {
      return {
        ...state,
        rescheduleAppointmentFetching: false,
        rescheduleAppointmentSuccess: true,
        rescheduleAppointmentError: false,
        rescheduleAppointmentErrorData: null,
        rescheduleAppointmentSuccessData: payload,
      };
    }
    case RESCHEDULE_APPOINTMENT_ERROR:
      return {
        ...state,
        rescheduleAppointmentFetching: false,
        rescheduleAppointmentSuccess: false,
        rescheduleAppointmentError: true,
        rescheduleAppointmentErrorData: payload,
      };
    case RESCHEDULE_APPOINTMENT_RESET:
      return {
        ...state,
        rescheduleAppointmentFetching: false,
        rescheduleAppointmentSuccess: false,
        rescheduleAppointmentError: true,
        rescheduleAppointmentErrorData: null,
        rescheduleAppointmentSuccessData: null,
      };
    default:
      return state;
  }
};
