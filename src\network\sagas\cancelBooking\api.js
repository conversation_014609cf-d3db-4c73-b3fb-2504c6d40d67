import { CANCEL_APPOINTMENT } from '@/utils/constants/awsApiEndpoints';
import { makeNetworkCall } from '../..';
import { APPOINTMENT_ID, LOCATION_ID, ORGANIZATION_ID, REGISTRY } from '../../../utils/constants';

export function cancelBooking<PERSON>pi(action) {
  const { payload = {} } = action || {};
  const { headers = {}, appointmentId, cancelData = {}, locationId, organizationId } = payload || {};

  const URL = CANCEL_APPOINTMENT.replace(ORGANIZATION_ID, organizationId)
    //.replace(LOCATION_ID, locationId)
    .replace(APPOINTMENT_ID, appointmentId);

  const config = {
    method: 'DELETE',
    url: URL,
    headers: headers,
    data: cancelData,
  };

  return makeNetworkCall(config);
}
