import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/getLocations/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { fetchLocations } from './index';
/**
 * This function test test case for get search location details saga
 * Fires get search location success of api gives success
 * Fires get search location error of api fails
 */

describe('getLocations', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiFetchLocations = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchLocations(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchLocations, requestResult).done;

    let successResult = actions.fetchLocationsSuccess(DUMMY_ITEM.data);

    expect(api.makeApiFetchLocations.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchLocations = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchLocations(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchLocations, requestResult).done;

    expect(api.makeApiFetchLocations.mock.calls.length).toBe(1);

    let errorResult = actions.fetchLocationsError();
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchLocations = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchLocations(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchLocations, requestResult).done;

    expect(api.makeApiFetchLocations.mock.calls.length).toBe(1);

    let resetResult = actions.fetchLocationsReset();
    const expectedAction = {
      type: actions.GET_LOCATION_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
