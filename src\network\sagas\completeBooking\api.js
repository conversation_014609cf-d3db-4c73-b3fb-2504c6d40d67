/*
 *
 */

import { makeNetworkCall } from '../..';
import { LOCATION_ID, ORGANIZATION_ID } from '../../../utils/constants';
import { BOOK_APPOINTMENT } from '@/utils/constants/awsApiEndpoints';

export function completeBookingApi(action) {
  const { payload = {} } = action || {};
  const { headers = {}, bookingData = {}, organizationId, locationId } = payload || {};

  const config = {
    method: 'POST',
    url: BOOK_APPOINTMENT.replace(ORGANIZATION_ID, organizationId).replace(LOCATION_ID, locationId),
    headers: headers,
    data: bookingData,
  };

  return makeNetworkCall(config);
}
