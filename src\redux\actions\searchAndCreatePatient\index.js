/*
 *BOOKING_DETAILS action types
 */

export const SEARCH_AND_CREATE_PATIENT_REQUEST = 'SEARCH_AND_CREATE_PATIENT_REQUEST';
export const SEARCH_AND_CREATE_PATIENT_SUCCESS = 'SEARCH_AND_CREATE_PATIENT_SUCCESS';
export const SEARCH_AND_CREATE_PATIENT_ERROR = 'SEARCH_AND_CREATE_PATIENT_ERROR';
export const SEARCH_AND_CREATE_PATIENT_RESET = 'SEARCH_AND_CREATE_PATIENT_RESET';

/*
 * action creators
 */

export function searchAndCreatePatient(data) {
  return {
    type: SEARCH_AND_CREATE_PATIENT_REQUEST,
    payload: data,
  };
}

export function searchAndCreatePatientActionSuccess(data) {
  return {
    type: SEARCH_AND_CREATE_PATIENT_SUCCESS,
    payload: data,
  };
}

export function searchAndCreatePatientActionError() {
  return {
    type: SEARCH_AND_CREATE_PATIENT_ERROR,
  };
}

export const resetSearchAndCreatePatient = () => {
  return {
    type: SEARCH_AND_CREATE_PATIENT_RESET,
  };
};
