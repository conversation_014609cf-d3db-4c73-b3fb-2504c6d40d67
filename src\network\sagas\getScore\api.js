/*
 *
 */
import { makeNetworkCall } from '../..';
import { ORGANIZATION_ID, PATIENT_ID, QUESTIONNAIRE_RESPONSE_ID } from '../../../utils/constants';
import { GET_SCORE_URL, BASE_URL } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiGetScore(action) {
  const { payload = {} } = action || {};
  const { headers = {}, questionnaireResponseId, patientId, organizationId } = payload || {};
  const mainUrl =
    BASE_URL +
    GET_SCORE_URL.replace(QUESTIONNAIRE_RESPONSE_ID, questionnaireResponseId)
      .replace(PATIENT_ID, patientId)
      .replace(ORGANIZATION_ID, organizationId);
  const config = {
    method: 'GET',
    url: mainUrl,
    headers: headers,
  };
  return makeNetworkCall(config);
}
