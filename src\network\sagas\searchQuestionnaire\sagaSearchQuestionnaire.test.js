import { runSaga } from 'redux-saga';
import * as api from './api';
import { fetchSearchQuestionnaire } from './index';
import * as actions from '../../../redux/actions/SearchQuestionnaire/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

/**
 * This function test test case for get booking slots details saga
 * Fires get booking slots success of api gives success
 * Fires get booking slots error of api fails
 */

describe('Search Questionnaire Patient', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiFetchSearchQuestionnaire = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.searchQuestionnaireDetails(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchSearchQuestionnaire, requestResult).done;

    let successResult = actions.searchQuestionnaireActionSuccess(DUMMY_ITEM.data);

    expect(api.makeApiFetchSearchQuestionnaire.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchSearchQuestionnaire = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.searchQuestionnaireDetails(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchSearchQuestionnaire, requestResult).done;

    expect(api.makeApiFetchSearchQuestionnaire.mock.calls.length).toBe(1);

    let errorResult = actions.searchQuestionnaireActionError(DUMMY_ITEM.data);
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch reset action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchSearchQuestionnaire = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.searchQuestionnaireDetails(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchSearchQuestionnaire, requestResult).done;

    expect(api.makeApiFetchSearchQuestionnaire.mock.calls.length).toBe(1);

    let resetResult = actions.resetQuestionnaire(DUMMY_ITEM.data);
    const expectedAction = {
      type: actions.SEARCH_QUESTIONNAIRE_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
