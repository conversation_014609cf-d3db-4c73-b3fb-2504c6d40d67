export const pages = {
  INTRODUCTION: 'INTRODUCTION',
  SERVICE: 'SERVICE',
  HISTORY_AND_SERVICES: 'HISTORY_AND_SERVICES',
  DEMOGRAPHIC: 'DEMOGRAPHIC',
  LOCATION: 'LOCATION',
  SLOT_BOOKING: 'SLOT_BOOKING',
  BOOKING_SUMMARY: 'BOOKING_SUMMARY',
  REVIEW_BOOKING: 'REVIEW_BOOKING',
  CANCEL_APPOINTMENT: 'CANCEL_APPOINTMENT',
  CANCEL_APPOINTMENT_SUMMARY: 'CANCEL_APPOINTMENT_SUMMARY',
  RESCHEDULE_APPOINTMENT: 'RESCHEDULE_APPOINTMENT',
  ACTION: 'ACTION',
  LOGIN: 'LOGIN',
  PAGE_NOT_FOUND: 'PAGE_NOT_FOUND',
  APPOINTMENT_NOT_FOUND: 'APPOINTMENT_NOT_FOUND',
  REGISTER: 'REGISTER',
  HEADING_AND_DESCRIPTION: 'HEADING_AND_DESCRIPTION',
  EMAIL_VALIDATION: 'EMAIL_VALIDATION',
  CONFIRMATION: 'CONFIRMATION',
};

export const finalActions = {
  BOOKING: 'BOOKING',
  RESCHEDULE: 'RESCHEDULE',
  RESCHEDULE_BOOKING_SUMMARY: 'RESCHEDULE_BOOKING_SUMMARY',
  CANCEL: 'CANCEL',
  CONFIRM: 'CONFIRM',
  MODIFICATION: 'MODIFICATION',
};
//TODO: remove CANCEL and CONFIRM final action

export const apiErrors = {
  searchAndCreatePatient: {
    multiplePatient: 'Found multiple patients in Patient Service',
    invalidHealthCardId: 'client.healthcard.id.invalid',
    clientCreationDisallowed: 'client.external.create.disallowed',
    clientCreationDataNotExists: 'client.external.data.notexists',
  },
};

export const AppointmentConfirmationStatus = {
  ACCEPTED: 'accepted',
  TENTATIVE: 'tentative',
};
