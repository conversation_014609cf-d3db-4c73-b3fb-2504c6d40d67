export const SEAR<PERSON>_INDIVIDUAL_AT_CLIENT_INDEX_REQUEST = 'SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_REQUEST';
export const SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_SUCCESS = 'SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_SUCCESS';
export const SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_ERROR = 'SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_ERROR';
export const SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_RESET = 'SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_RESET';

export function searchIndividualAtClientIndex(data) {
  return {
    type: SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_REQUEST,
    payload: data,
  };
}

export function searchIndividualAtClientIndexSuccess(data) {
  return {
    type: SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_SUCCESS,
    payload: data,
  };
}

export function searchIndividualAtClientIndexError(data) {
  return {
    type: SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_ERROR,
    payload: data,
  };
}

export const resetSearchIndividualAtClientIndex = () => {
  return {
    type: SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_RESET,
  };
};
