import { NETWORK, ORGANIZATION, MACHINE_ACCESS_TOKEN, ORGANIZATION_ID, INDIVIDUAL } from '@/utils/constants';
import { ORGANIZATION_USER } from '@/containers/commonConstants';
import { getSession } from 'next-auth/react';

export const getUserTokenAndUpdatedUrl = async ({ url }) => {
  try {
    let newUrl;
    if (typeof window === 'undefined') {
      throw new Error('Use a fetch/server module instead.');
    }
    const session = await getSession();

    if (!session || !session.user) {
      // no user signed in
      return [null, null];
    }
    console.log(session, 'session');
    const { idToken } = session?.user || {};

    if (!idToken) {
      throw new Error('user id token does not exist in session.');
    }

    return [url, idToken];
  } catch (error) {
    console.log('addUserToken/client failed', error);
    throw error;
  }
};

const getAccessTokenFromCookie = async (env) => {
  const accessToken = document.cookie
    .split('; ')
    .find((row) => row.startsWith(`${MACHINE_ACCESS_TOKEN(env)}=`))
    ?.split('=')[1];

  return accessToken;
};

export const getMachineAccessToken = async (targetAwsEnv = ORGANIZATION) => {
  try {
    if (targetAwsEnv !== NETWORK && targetAwsEnv !== ORGANIZATION && targetAwsEnv !== INDIVIDUAL) {
      throw new Error('env should be network, organization, or individual');
    }
    if (typeof window === 'undefined') {
      throw new Error('Not running this code in browser env.');
    }
    let accessToken = await getAccessTokenFromCookie(targetAwsEnv);

    // Refetch access token if it does not exist. It is either the first time or if it expired
    if (!accessToken) {
      const res = await fetch(
        `/api/auth/machineToken/${targetAwsEnv}`,
        // `${process.env.NODE_ENV !== 'production' ? 'http' : 'https'}://${
        //   window.location.host
        // }/api/auth/machineToken/${targetAwsEnv}`,
        {
          cache: 'no-store',
        },
      );
      console.log(res);
      if (!res.ok) {
        console.log('Fetching access token failed with status', res.status);
        throw res;
      }
      const data = await res.json();
      accessToken = data.accessToken;
    }
    return accessToken;
  } catch (e) {
    console.log('get machine access token failed', e, targetAwsEnv);
    throw e;
  }
};
