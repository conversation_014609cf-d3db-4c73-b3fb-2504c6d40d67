/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { makeNetworkCall } from '../..';
import { TODO_ENDPOINT } from '../../../utils/constants/apiEndpoints';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { CARE_REQUEST, CARE_SUCCESS, CARE_ERROR } from '../../../redux/actions/care';

import { makeApiFetchTypCode } from './api';

/**
 *
 * @param {*} action
 */
export function makeUserFormData(action) {
  const { payload = {} } = action || {};
  const { data = {} } = payload || {};
  const { data: userDetails = {}, headers = {} } = data || {};

  let formBody = [];
  for (let userProperty in userDetails) {
    let encodedKey = encodeURIComponent(userProperty);
    let encodedValue = encodeURIComponent(userDetails[userProperty]);
    formBody.push(encodedKey + '=' + encodedValue);
  }
  formBody = formBody.join('&');

  const config = {
    method: 'POST',
    url: TODO_ENDPOINT,
    data: formBody,
    headers: headers,
    formData: true,
  };
  return makeNetworkCall(config);
}

/**
 *
 * @param {*} action
 */
export function* fetchTypcode(action) {
  try {
    const response = yield call(makeApiFetchTypCode, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: CARE_SUCCESS, payload: data });
    } else {
      yield put({ type: CARE_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: CARE_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchCare() {
  yield takeEvery(CARE_REQUEST, fetchTypcode);
}
