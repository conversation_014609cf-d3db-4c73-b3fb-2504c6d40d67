import { NextResponse } from 'next/server';
import { AwsError, generateSecretHash, handler } from '@/containers/auth';
import { UNVERIFIED_USER_DETAILS, UNVERIFIED_USER_EMAIL } from '@/utils/constants';
import { CognitoIdentityProviderClient, ConfirmSignUpCommand } from '@aws-sdk/client-cognito-identity-provider';

/**
 * @param {Request} req
 * @returns {Promise<void>}
 */
const postEndpoint = async (req) => {
  const { COGNITO_INDIVIDUAL_HUMAN_REGION, COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_ID } = process.env;
  const { verificationCode } = await req.json();
  const email = req.cookies.get(UNVERIFIED_USER_EMAIL)?.value;

  if (!email || !verificationCode) {
    return NextResponse.json(
      {
        message: 'email and verification code must be provided',
      },
      {
        status: 400,
      },
    );
  }

  const params = {
    ClientId: COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_ID,
    SecretHash: generateSecretHash(email),
    Username: email,
    ConfirmationCode: verificationCode,
  };

  const cognitoClient = new CognitoIdentityProviderClient({
    region: COGNITO_INDIVIDUAL_HUMAN_REGION,
  });

  const confirmSignUpCommand = new ConfirmSignUpCommand(params);

  try {
    const res = await cognitoClient.send(confirmSignUpCommand);
    const status = res['$metadata'].httpStatusCode;

    const response = NextResponse.json({}, { status });

    if (status === 200) {
      response.cookies.delete(UNVERIFIED_USER_EMAIL);
      response.cookies.delete(UNVERIFIED_USER_DETAILS);
    }

    return response;
  } catch (err) {
    const awsError = new AwsError(err);
    return awsError.toNextResponse();
  }
};

const middleware1 = (_req, next) => {
  console.log('middleware 1');
  next();
};

export const POST = handler(middleware1, postEndpoint);
