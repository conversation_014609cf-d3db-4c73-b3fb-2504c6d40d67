import { call, put, takeEvery } from 'redux-saga/effects';
import {
  CREATE_CONNECTION_AT_CLIENT_INDEX_REQUEST,
  CREATE_CONNECTION_AT_CLIENT_INDEX_SUCCESS,
  CREATE_CONNECTION_AT_CLIENT_INDEX_ERROR,
} from '../../../redux/actions/createConnectionAtClientIndex';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { createConnectionAtClientIndex } from './api';

export function* fetchCreateConnectionAtClientIndex(action) {
  try {
    const response = yield call(createConnectionAtClientIndex, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: CREATE_CONNECTION_AT_CLIENT_INDEX_SUCCESS, payload: data });
    } else {
      yield put({ type: CREATE_CONNECTION_AT_CLIENT_INDEX_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: CREATE_CONNECTION_AT_CLIENT_INDEX_ERROR });
  }
}

export function* watchCreateConnectionAtClientIndex() {
  yield takeEvery(CREATE_CONNECTION_AT_CLIENT_INDEX_REQUEST, fetchCreateConnectionAtClientIndex);
}
