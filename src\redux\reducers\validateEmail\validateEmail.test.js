import {
  VALIDATE_EMAIL_SUCCESS,
  VALIDATE_EMAIL_REQUEST,
  VALIDATE_EMAIL_ERROR,
  VALIDATE_EMAIL_RESET,
} from '../../actions/validateEmail';

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import { validateEmailReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isValidateEmailFetching: true,
  validateEmailSuccess: false,
  validateEmailError: false,
  validateEmailErrorData: null,
};

const successState = {
  isValidateEmailFetching: false,
  validateEmailSuccess: true,
  validateEmailError: false,
  validateEmailErrorData: null,
  validateEmailSuccessData: data,
};

const errorState = {
  isValidateEmailFetching: false,
  validateEmailSuccess: false,
  validateEmailError: true,
  validateEmailErrorData: data,
};

const resetState = {
  isValidateEmailFetching: false,
  validateEmailSuccess: false,
  validateEmailError: false,
  validateEmailErrorData: null,
};

describe('Reschedule Reducer', () => {
  it('should return the initial state', () => {
    expect(validateEmailReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle VALIDATE_EMAIL_REQUEST', () => {
    expect(
      validateEmailReducer(initialState, {
        type: VALIDATE_EMAIL_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle VALIDATE_EMAIL_SUCCESS', () => {
    expect(
      validateEmailReducer(initialState, {
        type: VALIDATE_EMAIL_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle VALIDATE_EMAIL_ERROR', () => {
    expect(
      validateEmailReducer(initialState, {
        type: VALIDATE_EMAIL_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle VALIDATE_EMAIL_RESET', () => {
    expect(
      validateEmailReducer(initialState, {
        type: VALIDATE_EMAIL_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
