import React, { useState, useEffect, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Alert, Grid, Paper, Stack, Typography } from '@mui/material';
import { fetchLocations } from '@/redux/actions/getLocations';
import { fetchNextAvailableSlot } from '@/redux/actions/getNextAvailableSlot';
import GoogleMap from './GoogleMap';
import { LocationList } from './LocationList';
import * as CommonUtility from '@/containers/commonUtility';
import { Loader } from '@/components';
import useNotification from '@/hooks/useNotification';
import { HeaderSummary } from '../../HeaderSummary';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';

const alertMessagesMap = {
  bothDateAvailable:
    '<full_name>, based on your immunization records in our system, your <service> must be booked between <earliest_date> and <latest_date>. If you have any questions, or are unable to find a suitable appointment time, please call the clinic directly.',
  earliestDateAvailable:
    '<full_name>, based on your immunization records in our system, your <service> must be booked after <earliest_date>. If you have any questions, or are unable to find a suitable appointment time, please call the clinic directly.',
  latestDateAvailable:
    '<full_name>, based on your immunization records in our system, your <service> must be booked before <latest_date>. If you have any questions, or are unable to find a suitable appointment time, please call the clinic directly.',
  notAllowedMessage: 'Please call your local health centre to book an appointment',
};

export const Location = (props) => {
  console.log('TRACE: Location');
  const {
    handleNavigationCallback,
    handleEditNavigationCallback,
    isRescheduleAppointment,
    selectedService,
    headerSummary,
    demographic,
    searchedPlace,
    setSearchedPlace,
    locationDistanceMap,
    setLocationDistanceMap,
    smartDates,
    smartDatesStatus,
  } = props;
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [organizationId] = CommonUtility.getOrganizationAndWidgetId();
  const [msg, sendNotification] = useNotification();

  const [locations, setLocations] = useState(null);
  const [radius, setRadius] = useState(20);
  const [alertMessage, setAlertMessage] = useState('');

  const { isLocationsFetching, isLocationsSuccess, isLocationsError, locationsSuccessData, locationsErrorData } =
    useSelector((state) => state.getLocationsReducer);

  const widgetDetailsSuccessData = useSelector((state) => state.widgetDetailsReducer.widgetDetailsSuccessData);
  const {
    locations: widgetLocations = [],
    enableMap,
    mapPlaceholderImage,
    locationRequired,
  } = widgetDetailsSuccessData || {};

  const [allowBooking, showAlertMessage] = useMemo(() => {
    const { message, earliestDate, latestDate } = smartDates;
    const { earliestDateStatus, latestDateStatus, bothDateValid } = smartDatesStatus;
    if (message) {
      return [false, true];
    } else {
      if (!latestDate && earliestDate && earliestDateStatus === 'PAST') {
        return [true, false];
      } else if (earliestDate && latestDate && earliestDateStatus === 'PAST' && latestDateStatus === 'PAST') {
        return [true, false];
      } else if (!earliestDate && latestDate && ['INVALID', 'PAST'].includes(latestDateStatus)) {
        return [false, true];
      }
      return [true, true];
    }
  }, [smartDates, smartDatesStatus]);

  const prepareMessage = (template) => {
    let preparedMessage = template;
    const { firstName, lastName } = demographic?.individuals?.[0] || {};

    const keyMap = {
      '<full_name>': `<b>${CommonUtility.getFullName(firstName, lastName)}</b>`,
      '<service>': selectedService?.name,
      '<earliest_date>': smartDates.earliestDate ? format(new Date(smartDates.earliestDate), 'yyyy-MM-dd') : '',
      '<latest_date>': smartDates.latestDate ? format(new Date(smartDates.latestDate), 'yyyy-MM-dd') : '',
    };

    Object.entries(keyMap).forEach(([key, value]) => (preparedMessage = preparedMessage.replace(key, value)));

    return preparedMessage;
  };

  useEffect(() => {
    if (demographic && smartDates && smartDatesStatus) {
      const { earliestDateStatus, latestDateStatus, bothDateValid } = smartDatesStatus;

      if (smartDates?.message) {
        setAlertMessage(smartDates?.message);
        return;
      }
      let preparedMessage = '';
      if (bothDateValid) {
        preparedMessage = prepareMessage(alertMessagesMap.bothDateAvailable);
      } else if (['PRESENT', 'FUTURE'].includes(earliestDateStatus)) {
        preparedMessage = prepareMessage(alertMessagesMap.earliestDateAvailable);
      } else if (['PRESENT', 'FUTURE'].includes(latestDateStatus)) {
        preparedMessage = prepareMessage(alertMessagesMap.latestDateAvailable);
      } else if (['INVALID', 'PAST'].includes(earliestDateStatus) && latestDateStatus === 'PAST') {
        preparedMessage = alertMessagesMap.notAllowedMessage;
      }

      setAlertMessage(preparedMessage);
    }
  }, [smartDatesStatus]);

  const getWidgetLocationsIds = () => {
    const widgetLocationsIds = [];
    widgetLocations.forEach((item) => {
      widgetLocationsIds.push(item.id);
    });
    return widgetLocationsIds;
  };

  const getAppointmentTypeAndId = () => {
    return {
      appointmentType: selectedService?.code,
      appointmentTypeId: Number(selectedService?.id),
    };
  };

  const getNextAvailableSlotData = (locationArray, earliestDate) => {
    let locationList = '';
    locationArray.map((location) => {
      locationList = locationList + location.resource.id + ',';
    });
    const { appointmentTypeId } = getAppointmentTypeAndId();
    let data = {
      appointmentTypeId,
      serviceDeliveryLocationIds: getWidgetLocationsIds(),
      startDate: earliestDate,
    };
    return data;
  };

  useEffect(() => {
    dispatch(
      fetchLocations({
        organizationId,
      }),
    );
  }, []);

  useEffect(() => {
    if (isLocationsSuccess && locationsSuccessData) {
      if (locationsSuccessData.entry && locationsSuccessData.entry.length) {
        const widgetLocationsIds = getWidgetLocationsIds();
        const locationArray = [];
        locationsSuccessData.entry.forEach((location) => {
          if (widgetLocationsIds.includes(location.resource.id)) {
            locationArray.push(location);
          }
        });
        if (locationArray && locationArray.length) {
          setLocations(locationArray);
          const earliestDate =
            smartDatesStatus?.earliestDateStatus === 'FUTURE' ? format(smartDates?.earliestDate, 'yyy-MM-dd') : null;

          const reqData = getNextAvailableSlotData(locationArray, earliestDate);

          dispatch(fetchNextAvailableSlot({ reqData, organizationId }));
        }
      }
    }
  }, [isLocationsSuccess, locationsSuccessData]);

  useEffect(() => {
    if (isLocationsError && locationsErrorData) {
      sendNotification({ msg: t('apiError'), variant: 'error' });
    }
  }, [isLocationsError, locationsErrorData]);

  const updateLocationDistance = (locationDistanceMap, searchedPlace) => {
    setLocationDistanceMap(locationDistanceMap);
    setSearchedPlace(searchedPlace);
  };

  return (
    <>
      {isLocationsFetching ? (
        <Loader active={true} />
      ) : (
        <Stack spacing={2} sx={{ mt: 2, mx: '2%', px: { xs: 2, md: 4 }, py: 2, mb: 2 }}>
          {headerSummary.size ? (
            <Paper sx={{ px: 2, mb: 3 }}>
              <HeaderSummary
                handleNavigationCallback={isRescheduleAppointment ? undefined : handleEditNavigationCallback}
                headerSummary={headerSummary}
                demographic={demographic}
              />
            </Paper>
          ) : (
            ''
          )}

          {showAlertMessage && alertMessage ? (
            <Alert severity="error">
              <Typography variant="body1" dangerouslySetInnerHTML={{ __html: alertMessage }} />
            </Alert>
          ) : (
            <></>
          )}

          {allowBooking ? (
            <Grid container>
              {enableMap && (
                <Grid item xs={12} sm={4}>
                  <Paper sx={{ py: 1, px: 2, mr: { sm: 1 }, mb: { xs: 2 }, height: { sm: '100%' } }}>
                    <GoogleMap
                      {...props}
                      radius={radius}
                      setRadius={setRadius}
                      locations={locations}
                      searchedPlace={searchedPlace}
                      locationDistanceMap={locationDistanceMap}
                      handleLocationDistanceUpdate={(locationDistanceMap, place) =>
                        updateLocationDistance(locationDistanceMap, place)
                      }
                    />
                  </Paper>
                </Grid>
              )}

              {!enableMap && mapPlaceholderImage?.imagePresignedUrl && (
                <Grid item xs={12} sm={4}>
                  <Paper sx={{ py: 1, px: 2, mr: { sm: 1 }, mb: { xs: 2 }, height: { sm: '100%' } }}>
                    <img
                      src={`${mapPlaceholderImage?.imagePresignedUrl}`}
                      alt=""
                      style={{ maxWidth: '100%', height: 'auto' }}
                    />
                  </Paper>
                </Grid>
              )}

              {(!locationRequired || locationDistanceMap?.size) && (
                <Grid item xs={12} sm={enableMap || mapPlaceholderImage?.imagePresignedUrl ? 8 : 12}>
                  <Paper sx={{ mt: { xs: 1, sm: 0 }, px: 2, py: 2, pb: 1.9 }}>
                    {locations && (
                      <LocationList
                        radius={radius}
                        locations={locations}
                        searchedPlace={searchedPlace}
                        locationDistanceMap={locationDistanceMap}
                        handleNavigationCallback={(action, location) => handleNavigationCallback(action, location)}
                      />
                    )}
                  </Paper>
                </Grid>
              )}
            </Grid>
          ) : (
            <></>
          )}
        </Stack>
      )}
    </>
  );
};
