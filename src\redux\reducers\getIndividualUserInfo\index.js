/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import {
  INDIVIDUAL_USER_INFO_RESET,
  INDIVIDUAL_USER_INFO_ERROR,
  INDIVIDUAL_USER_INFO_SUCCESS,
  INDIVIDUAL_USER_INFO_REQUEST,
  SAVE_INDIVIDUAL_USER_INFO,
} from '../../actions/getIndividualUserInfo';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const individualUserInfoReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case INDIVIDUAL_USER_INFO_REQUEST:
      return {
        ...state,
        isIndividualUserInfoFetching: true,
        individualUserInfoSuccess: false,
        individualUserInfoError: false,
        individualUserInfoErrorData: null,
      };
    case INDIVIDUAL_USER_INFO_SUCCESS: {
      return {
        ...state,
        isIndividualUserInfoFetching: false,
        individualUserInfoSuccess: true,
        individualUserInfoError: false,
        individualUserInfoSuccessData: payload,
        individualUserInfoErrorData: null,
      };
    }
    case SAVE_INDIVIDUAL_USER_INFO: {
      return {
        ...state,
        isIndividualUserInfoFetching: false,
        individualUserInfoSuccess: true,
        individualUserInfoError: false,
        individualUserInfoSuccessData: payload,
        individualUserInfoErrorData: null,
      };
    }
    case INDIVIDUAL_USER_INFO_ERROR:
      return {
        ...state,
        isIndividualUserInfoFetching: false,
        individualUserInfoSuccess: false,
        individualUserInfoError: true,
        individualUserInfoErrorData: payload,
      };
    case INDIVIDUAL_USER_INFO_RESET:
      return {
        ...state,
        isIndividualUserInfoFetching: false,
        individualUserInfoSuccess: false,
        individualUserInfoSuccessData: null,
        individualUserInfoError: false,
        individualUserInfoErrorData: null,
      };
    default:
      return state;
  }
};
