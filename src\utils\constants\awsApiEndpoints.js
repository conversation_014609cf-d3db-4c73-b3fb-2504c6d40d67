import {
  API_IDENTIFIER,
  REGION,
  API_ENVIRONMENT,
  ORGANI<PERSON>ATION_ID,
  QUESTIONNAIRE_ID,
  WIDGET_ID,
  INDIVIDUAL_ID,
  INCLUDE_SHARE_ATTRIBUTES,
  DOWNLOAD_REPORT_ID,
  APPOINTMENT_ID,
  LOCATION_ID,
  APPOINTMENT_TYPE,
  REQUEST_ID,
} from '.';

export const API_BASE_URL = `https://${API_IDENTIFIER}.execute-api.${REGION}.amazonaws.com/${API_ENVIRONMENT}`;

export const makeApiBaseUrl = (apiIdentifier, region, environment) => {
  return API_BASE_URL.replace(API_IDENTIFIER, apiIdentifier)
    .replace(REGION, region)
    .replace(API_ENVIRONMENT, environment);
};

const apiVariables = {
  CONNECTION_INDEX_IDENTIFIER: process.env.NEXT_PUBLIC_CONNECTION_INDEX_IDENTIFIER,
  CONNECTION_INDEX_STAGE: process.env.NEXT_PUBLIC_CONNECTION_INDEX_STAGE,

  CLIENT_INDEX_IDENTIFIER: process.env.NEXT_PUBLIC_CLIENT_INDEX_IDENTIFIER,
  CLIENT_INDEX_STAGE: process.env.NEXT_PUBLIC_CLIENT_INDEX_STAGE,

  PUBLIC_ARTIFACT_REPOSITORY_IDENTIFIER: process.env.NEXT_PUBLIC_ARTIFACT_REPOSITORY_IDENTIFIER_PUBLIC,
  PUBLIC_ARTIFACT_REPOSITORY_STAGE: process.env.NEXT_PUBLIC_ARTIFACT_REPOSITORY_PUBLIC_STAGE,

  PRIVATE_ARTIFACT_REPOSITORY_IDENTIFIER: process.env.NEXT_PUBLIC_ARTIFACT_REPOSITORY_IDENTIFIER_PRIVATE,
  PRIVATE_ARTIFACT_REPOSITORY_STAGE: process.env.NEXT_PUBLIC_ARTIFACT_REPOSITORY_PRIVATE_STAGE,

  APPSCOOP_WIDGET_SERVICES_IDENTIFIER: process.env.NEXT_PUBLIC_APPSCOOP_WIDGET_SERVICES_IDENTIFIER,
  APPSCOOP_WIDGET_SERVICES_REGION: process.env.NEXT_PUBLIC_APPSCOOP_WIDGET_SERVICES_REGION,
  APPSCOOP_WIDGET_SERVICES_STAGE: process.env.NEXT_PUBLIC_APPSCOOP_WIDGET_SERVICES_STAGE,

  CAMBIAN_WIDGET_SERVICES_IDENTIFIER: process.env.NEXT_PUBLIC_CAMBIAN_WIDGET_SERVICES_IDENTIFIER,
  CAMBIAN_WIDGET_SERVICES_STAGE: process.env.NEXT_PUBLIC_CAMBIAN_WIDGET_SERVICES_STAGE,
  CAMBIAN_SERVICES_REGION: process.env.NEXT_PUBLIC_CAMBIAN_SERVICES_REGION,

  CAMBIAN_INDIVIDUAL_CDR_IDENTIFIER: process.env.NEXT_PUBLIC_CAMBIAN_INDIVIDUAL_CDR,
  CAMBIAN_INDIVIDUAL_CDR_STAGE: process.env.NEXT_PUBLIC_CAMBIAN_INDIVIDUAL_CDR_STAGE,

  CAMBIAN_ORGANIZATION_CDR_IDENTIFIER: process.env.NEXT_PUBLIC_CAMBIAN_ORGANIZATION_CDR,
  CAMBIAN_ORGANIZATION_CDR_STAGE: process.env.NEXT_PUBLIC_CAMBIAN_ORGANIZATION_CDR_STAGE,

  PDF_GENERATION_SERVICE_IDENTIFIER: process.env.NEXT_PUBLIC_PDF_GENERATION_SERVICE_IDENTIFIER,
  PDF_GENERATION_SERVICES_STAGE: process.env.NEXT_PUBLIC_PDF_GENERATION_SERVICES_STAGE,
};

// const WIDGET_SERVICES_BASE_URL = makeApiBaseUrl(
//   apiVariables.APPSCOOP_WIDGET_SERVICES_IDENTIFIER,
//   apiVariables.APPSCOOP_WIDGET_SERVICES_REGION,
//   apiVariables.APPSCOOP_WIDGET_SERVICES_STAGE,
// );
const WIDGET_SERVICES_BASE_URL = process.env.NEXT_PUBLIC_WIDGET_SERVICES_BASE_URL;

const ORG_DATA_BASE_URL = process.env.NEXT_PUBLIC_ORGANIZATION_DATA_BASE_URL;
const ORG_REQUEST_BASE_URL = process.env.NEXT_PUBLIC_ORGANIZATION_REQUEST_BASE_URL;
const IND_REQUEST_BASE_URL = process.env.NEXT_PUBLIC_INDIVIDUAL_REQUEST_BASE_URL;

// const SCHEDULAR_BOOKING_SERVICES_BASE_URL = makeApiBaseUrl(
//   apiVariables.CAMBIAN_WIDGET_SERVICES_IDENTIFIER,
//   apiVariables.CAMBIAN_SERVICES_REGION,
//   apiVariables.CAMBIAN_WIDGET_SERVICES_STAGE,
// );
const SCHEDULAR_BOOKING_SERVICES_BASE_URL = process.env.NEXT_PUBLIC_BOOKING_SERVICES_BASE_URL;

// const PUBLIC_ARTIFACT_REPOSITORY_BASE_URL = makeApiBaseUrl(
//   apiVariables.PUBLIC_ARTIFACT_REPOSITORY_IDENTIFIER,
//   apiVariables.CAMBIAN_SERVICES_REGION,
//   apiVariables.PUBLIC_ARTIFACT_REPOSITORY_STAGE,
// );
const PUBLIC_ARTIFACT_REPOSITORY_BASE_URL = process.env.NEXT_PUBLIC_ARTIFACT_PUBLIC_REPOSITORY_BASE_URL;

// const PRIVATE_ARTIFACT_REPOSITORY_BASE_URL = makeApiBaseUrl(
//   apiVariables.PRIVATE_ARTIFACT_REPOSITORY_IDENTIFIER,
//   apiVariables.CAMBIAN_SERVICES_REGION,
//   apiVariables.PRIVATE_ARTIFACT_REPOSITORY_STAGE,
// );
const PRIVATE_ARTIFACT_REPOSITORY_BASE_URL = process.env.NEXT_PUBLIC_ARTIFACT_PRIVATE_REPOSITORY_BASE_URL;

// const CONNECTION_INDEX_BASE_URL = makeApiBaseUrl(
//   apiVariables.CONNECTION_INDEX_IDENTIFIER,
//   'ca-central-1',
//   apiVariables.CONNECTION_INDEX_STAGE,
// );
const CONNECTION_INDEX_BASE_URL = process.env.NEXT_PUBLIC_CONNECTION_INDEX_BASE_URL;

// const CLIENT_INDEX_BASE_URL = makeApiBaseUrl(
//   apiVariables.CLIENT_INDEX_IDENTIFIER,
//   apiVariables.CAMBIAN_SERVICES_REGION,
//   apiVariables.CLIENT_INDEX_STAGE,
// );
const CLIENT_INDEX_BASE_URL = process.env.NEXT_PUBLIC_CLIENT_INDEX_BASE_URL;
const ORGANIZATION_MESSAGING_SERVICE_BASE_URL = process.env.NEXT_PUBLIC_ORGANIZATION_MESSAGING_SERVICE_BASE_URL;
const ORGANIZATION_INDIVIDUAL_SERVICE_BASE_URL = process.env.NEXT_PUBLIC_ORGANIZATION_INDIVIDUAL_SERVICE_BASE_URL;

// const INDIVIDUAL_CDR_BASE_URL = makeApiBaseUrl(
//   apiVariables.CAMBIAN_INDIVIDUAL_CDR_IDENTIFIER,
//   apiVariables.CAMBIAN_SERVICES_REGION,
//   apiVariables.CAMBIAN_INDIVIDUAL_CDR_STAGE,
// );
const INDIVIDUAL_CDR_BASE_URL = process.env.NEXT_PUBLIC_INDIVIDUAL_CDR_BASE_URL;

// const ORGANIZATION_CDR_BASE_URL = makeApiBaseUrl(
//   apiVariables.CAMBIAN_ORGANIZATION_CDR_IDENTIFIER,
//   apiVariables.CAMBIAN_SERVICES_REGION,
//   apiVariables.CAMBIAN_ORGANIZATION_CDR_STAGE,
// );
const ORGANIZATION_CDR_BASE_URL = process.env.NEXT_PUBLIC_ORGANIZATION_CDR_BASE_URL;

// const GENERATE_PDF_REPORT_BASE_URL = makeApiBaseUrl(
//   apiVariables.PDF_GENERATION_SERVICE_IDENTIFIER,
//   apiVariables.CAMBIAN_SERVICES_REGION,
//   apiVariables.PDF_GENERATION_SERVICES_STAGE,
// );
const GENERATE_PDF_REPORT_BASE_URL = process.env.NEXT_PUBLIC_PDF_GENERATION_SERVICE_BASE_URL;

// * App-Scoop developed APIs
export const GET_BOOKING_WIDGET = `${WIDGET_SERVICES_BASE_URL}/${ORGANIZATION_ID}/bookingWidget/${WIDGET_ID}`;
export const GET_REPORT_DATA_FOR_PDF = `${WIDGET_SERVICES_BASE_URL}/questionnaire/downloadHtmlReport/${DOWNLOAD_REPORT_ID}`;
export const GET_REGISTRATION_WIDGET = `${WIDGET_SERVICES_BASE_URL}/${ORGANIZATION_ID}/registrationWidget/${WIDGET_ID}`;
export const GET_QUESTIONNAIRE_WIDGET = `${WIDGET_SERVICES_BASE_URL}/${ORGANIZATION_ID}/questionnaireWidget/${WIDGET_ID}`;
export const DOWNLOAD_REPORT_AS_PDF = `${WIDGET_SERVICES_BASE_URL}/questionnaire/downloadHtmlReport`;
export const DOWNLOAD_HTML_REPORT_AS_PDF = `${WIDGET_SERVICES_BASE_URL}/download/downloadHtmlAsPdf`;

// Common and Registration APIs
// export const GET_CONSENT_AGREEMENT = `${PRIVATE_ARTIFACT_REPOSITORY_BASE_URL}/organizations/${ORGANIZATION_ID}/consent-agreement?publishStatus=no`;
export const GET_CONSENT_AGREEMENT = `${ORG_DATA_BASE_URL}/organizations/${ORGANIZATION_ID}/consent-agreement`;
export const CHECK_EXISTING_CLIENT_INDEX = `${CLIENT_INDEX_BASE_URL}/${ORGANIZATION_ID}/clients/${INDIVIDUAL_ID}`;
export const CHECK_EXISTING_CONNECTION_INDEX = `${CONNECTION_INDEX_BASE_URL}/connection?connectionEntityId=${ORGANIZATION_ID}&individualId=${INDIVIDUAL_ID}&includeSharedAttributes=${INCLUDE_SHARE_ATTRIBUTES}`;
export const CREATE_CONNECTION_AT_CLIENT_INDEX = `${CLIENT_INDEX_BASE_URL}/${ORGANIZATION_ID}/clients`;
export const CREATE_CONNECTION_AT_CONNECTION_INDEX = `${CONNECTION_INDEX_BASE_URL}/connection`;
export const SEARCH_CREATE_INDIVIDUAL_AT_CLIENT_INDEX = `${CLIENT_INDEX_BASE_URL}/${ORGANIZATION_ID}/match-update`;

// Cambian Questionnaire APIs
export const GET_PUBLIC_QUESTIONNAIRE = `${PUBLIC_ARTIFACT_REPOSITORY_BASE_URL}/organizations/${ORGANIZATION_ID}/questionnaires/${QUESTIONNAIRE_ID}`; //`/artifact/questionnaire/${QUESTIONNAIRE_ID}`;
export const GET_PRIVATE_QUESTIONNAIRE = `${PRIVATE_ARTIFACT_REPOSITORY_BASE_URL}/organizations/${ORGANIZATION_ID}/questionnaires/${QUESTIONNAIRE_ID}`; //`/artifact/questionnaire/${QUESTIONNAIRE_ID}`;
export const SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL = `${INDIVIDUAL_CDR_BASE_URL}/questionnaire-responses`;
export const SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION = `${ORGANIZATION_CDR_BASE_URL}/organizations/${ORGANIZATION_ID}/fhir/QuestionnaireResponse`;
export const GENERATE_PDF_REPORT = `${GENERATE_PDF_REPORT_BASE_URL}/generate`;
export const GET_REPORT_SETTINGS = `${ORG_DATA_BASE_URL}/organizations/${ORGANIZATION_ID}/report-settings`;
export const GET_ORGANIZATION_DETAILS = `${ORG_DATA_BASE_URL}/organizations/${ORGANIZATION_ID}`;

// * Cambian Booking APIs
export const GET_SERVICES = `${SCHEDULAR_BOOKING_SERVICES_BASE_URL}/organizations/${ORGANIZATION_ID}/services`;
export const GET_LOCATIONS = `${SCHEDULAR_BOOKING_SERVICES_BASE_URL}/organizations/${ORGANIZATION_ID}/locations`;
export const GET_TIME_SLOTS = `${SCHEDULAR_BOOKING_SERVICES_BASE_URL}/organizations/${ORGANIZATION_ID}/locations/${LOCATION_ID}/time-slots`;
export const CREATE_PATIENTS = `${SCHEDULAR_BOOKING_SERVICES_BASE_URL}/organizations/${ORGANIZATION_ID}/patients`;
export const BOOK_APPOINTMENT = `${SCHEDULAR_BOOKING_SERVICES_BASE_URL}/organizations/${ORGANIZATION_ID}/locations/${LOCATION_ID}/appointments`;
export const CANCEL_APPOINTMENT = `${SCHEDULAR_BOOKING_SERVICES_BASE_URL}/organizations/${ORGANIZATION_ID}/appointments/${APPOINTMENT_ID}`;
export const GET_APPOINTMENT_DETAIL = `${SCHEDULAR_BOOKING_SERVICES_BASE_URL}/organizations/${ORGANIZATION_ID}/appointments/${APPOINTMENT_ID}`;
export const RESCHEDULE_APPOINTMENT = `${SCHEDULAR_BOOKING_SERVICES_BASE_URL}/organizations/${ORGANIZATION_ID}/locations/${LOCATION_ID}/appointments/${APPOINTMENT_ID}`;
export const CONFIRM_APPOINTMENT = `${SCHEDULAR_BOOKING_SERVICES_BASE_URL}/organizations/${ORGANIZATION_ID}/appointments/${APPOINTMENT_ID}/confirm`;
export const GET_LOCATIONS_BY_APPOINTMENT = `${SCHEDULAR_BOOKING_SERVICES_BASE_URL}/organizations/${ORGANIZATION_ID}/appointment-types/${APPOINTMENT_TYPE}/locations`;
export const GET_NEXT_AVAILABLE_TIME_SLOT = `${SCHEDULAR_BOOKING_SERVICES_BASE_URL}/organizations/${ORGANIZATION_ID}/next-available-time-slot`;
export const GET_ELIGIBILITY = `${SCHEDULAR_BOOKING_SERVICES_BASE_URL}/organizations/${ORGANIZATION_ID}/eligibility`;
export const GET_INDIVIDUAL_DATA = `${ORGANIZATION_INDIVIDUAL_SERVICE_BASE_URL}/individuals`;
export const SEND_OTP = `${ORGANIZATION_MESSAGING_SERVICE_BASE_URL}/${ORGANIZATION_ID}/otps`;
export const VERIFY_OTP = `${ORGANIZATION_MESSAGING_SERVICE_BASE_URL}/${ORGANIZATION_ID}/verified-contact-mechanisms`;
export const CREATE_INDIVIDUAL_DATA = '';
export const INDIVIDUAL_REQUESTS = `${IND_REQUEST_BASE_URL}/individuals/${INDIVIDUAL_ID}/requests`;
export const GET_ORGANIZATION_REQUEST = `${ORG_REQUEST_BASE_URL}/organizations/${ORGANIZATION_ID}/requests/${REQUEST_ID}`;
