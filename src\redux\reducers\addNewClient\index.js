import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  ADD_NEW_CLIENT_REQUEST,
  ADD_NEW_CLIENT_SUCCESS,
  ADD_NEW_CLIENT_ERROR,
  ADD_NEW_CLIENT_RESET,
} from '../../actions/addNewClient';
export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};
export const addNewPatientReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case ADD_NEW_CLIENT_REQUEST:
      return {
        ...state,
        addNewPatientFetching: true,
        addNewPatientSuccess: false,
        addNewPatientError: false,
        addNewPatientErrorData: null,
      };
    case ADD_NEW_CLIENT_SUCCESS: {
      return {
        ...state,
        addNewPatientFetching: false,
        addNewPatientSuccess: true,
        addNewPatientError: false,
        addNewPatientErrorData: null,
        addNewPatientSuccessData: payload,
      };
    }
    case ADD_NEW_CLIENT_ERROR:
      return {
        ...state,
        addNewPatientFetching: false,
        addNewPatientSuccess: false,
        addNewPatientError: true,
        addNewPatientErrorData: payload,
      };
    case ADD_NEW_CLIENT_RESET:
      return {
        ...state,
        addNewPatientFetching: false,
        addNewPatientSuccess: false,
        addNewPatientError: false,
        addNewPatientErrorData: null,
        addNewPatientSuccessData: null,
      };
    default:
      return state;
  }
};
