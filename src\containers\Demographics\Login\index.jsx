import { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { fetchToken } from '@/redux/actions/oAuth';
import { BASIC_TOKEN, CAMBIAN_LOGIN_URL, CURRENT_URL } from '@/utils/constants';
import { getRedirectURL } from '@/utils/helpers/stringFun';
import { formUrlEncodedAPIHeaderWithBasicToken } from '../../commonUtility';

export const Login = () => {
  const dispatch = useDispatch();
  const currentURL = window.location.href;

  // get Oauth
  const oauthSuccessData = useSelector((state) => state.oAuthReducer.oauthSuccessData);
  const access_token = (oauthSuccessData && oauthSuccessData.access_token) || '';

  const createURL = (url, CAMBIAN_LOGIN_URL, redirect_url) => {
    var pathname = new URL(url).pathname;
    let urlIndex = pathname.split('/');
    let redirect_uri = `/${urlIndex[1]}/${urlIndex[2]}/${urlIndex[3]}`;
    let returnUrl =
      CAMBIAN_LOGIN_URL +
      `?response_type=code&client_id=widget&redirect_uri=${redirect_url}${redirect_uri}&resource=cambianrsid`;
    return returnUrl;
  };

  useEffect(() => {
    if (!access_token) {
      sessionStorage.clear();
      // dispatch(storageAction.addData(CURRENT_URL, currentURL));
      let signURL = createURL(currentURL, CAMBIAN_LOGIN_URL, window.location.origin);
      window.location.href = signURL;
    }
  }, []);

  const getAccessToken = () => {
    const query = new URLSearchParams(window.location.search);
    const code = query.get('code');
    let redirect_url = getRedirectURL(currentURL);
    if (code) {
      const headers = formUrlEncodedAPIHeaderWithBasicToken(BASIC_TOKEN);
      const oAuthData = {
        grant_type: 'authorization_code',
        code: code,
        client_id: 'widget',
        redirect_uri: redirect_url,
      };
      dispatch(fetchToken({ headers, oAuthData }));
    }
  };

  useEffect(() => {
    if (!access_token) {
      getAccessToken();
    }
  }, [access_token]);

  return <></>;
};
