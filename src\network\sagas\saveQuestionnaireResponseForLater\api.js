/*
 *
 */
import { makeNetworkCall } from '../..';
import {
  EMA<PERSON>,
  FINALIZE,
  ORGANIZATION_ID,
  QUESTIONNAIRE_ID,
  QUESTIONNAIRE_RESPONSE_ID,
} from '../../../utils/constants';
import {
  SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER,
  BASE_URL,
  CAMBIAN_SERVICE_BASE_URL,
} from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiSaveQuestionnaireResponseForLater(action) {
  const { payload = {} } = action || {};
  const {
    headers = {},
    questionnaireResponseId,
    finalize,
    email,
    questionnaireId,
    organizationId,
    data,
  } = payload || {};

  const mainUrl =
    CAMBIAN_SERVICE_BASE_URL +
    SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER.replace(QUESTIONNAIRE_RESPONSE_ID, questionnaireResponseId)
      .replace(FINALIZE, finalize)
      .replace(EMAIL, email)
      .replace(ORGANIZATION_ID, organizationId)
      .replace(QUESTIONNAIRE_ID, questionnaireId);

  const config = {
    method: 'PUT',
    url: mainUrl,
    headers: headers,
    data: data,
  };
  return makeNetworkCall(config);
}
