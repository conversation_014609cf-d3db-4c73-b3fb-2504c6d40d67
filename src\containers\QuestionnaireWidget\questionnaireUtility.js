import dataValidation from '@/utils/dataValidation/dataValidation';
import { fetchQuestionnaireQuestionsReset } from '@/redux/actions/getQuestionnaireDetails';
import {
  extractAllMatchingExtension,
  extractExtension,
  getParamFromUrl,
  resetConnectionAndClientReducers,
} from '../commonUtility';
import { resetPdfInfo } from '@/redux/actions/getPdfReport';
import { resetGenerateHtmlReportAsPdf } from '@/redux/actions/generateHtmlReportAsPdf';
import { resetGetReportDataForPdf } from '@/redux/actions/getReportDataForPdf';
import {
  BETWEEN,
  EQUAL_TO,
  GREATER_THAN,
  GREATER_THAN_EQUAL,
  LESS_THAN,
  LESS_THAN_EQUAL,
} from './questionnaireConstants';
import { resetSaveQuestionnaireResponseIndividual } from '@/redux/actions/saveQuestionnaireResponseIndividual';
import { resetSaveQuestionnaireResponseOrganization } from '@/redux/actions/saveQuestionnaireResponseOrganization';

export function getQuestionnaireId(questionnaire) {
  let id = questionnaire?.cambianReferenceId || questionnaire?.artifactId || null;
  if (questionnaire?.cambianReferenceData?.id && !dataValidation.isDataEmpty(questionnaire.cambianReferenceData.id)) {
    id = questionnaire.cambianReferenceData.id;
  }
  return id;
}

export function getQuestionnaireType(extensionArray) {
  let questionnaireType = null;
  let extension = extractExtensionByType(extensionArray, 'questionnaire-type');
  if (extension) {
    questionnaireType = extension.valueCode;
  }
  return questionnaireType;
}

export function getResultData(extensionArray) {
  let resultData = null;
  let extension = extractExtensionByType(extensionArray, 'result-page');
  if (extension) {
    resultData = extension.valueString;
  }
  return resultData;
}

export function extractExtensionByType(extensionArray, extensionType) {
  let matchingExtension = null;
  if (extensionArray !== undefined) {
    extensionArray.forEach((item) => {
      let arr = item.url.split('/') || [];
      if (arr[arr.length - 1] === extensionType) {
        matchingExtension = item;
      }
    });
  }
  return matchingExtension;
}

export function resetAllAPIReducers(dispatch) {
  dispatch(fetchQuestionnaireQuestionsReset());
  dispatch(resetPdfInfo());
  dispatch(resetGenerateHtmlReportAsPdf());
  dispatch(resetGetReportDataForPdf());

  // new v2 APIs
  dispatch(fetchQuestionnaireQuestionsReset());
  dispatch(resetSaveQuestionnaireResponseIndividual());
  dispatch(resetSaveQuestionnaireResponseOrganization());
  resetConnectionAndClientReducers(dispatch);
}

export function resetAllAPIReducersIncludingQuestionnaire(dispatch) {
  resetAllAPIReducers(dispatch);
}

export function buildDemographicByParameter(demographicSection, demographic) {
  /*
  firstName,
  lastName,
  email,
  phn,
  gender,
  birthDate,
  phone,
  address,
  participantId,
  */
  let demographicArray = [];

  demographicSection?.fields?.forEach((field) => {
    let individuals = demographic?.individuals[0];
    if (field.name === 'firstName' && field.showInReport) {
      if (individuals?.firstName) {
        demographicArray.push({
          id: 'firstName',
          label: 'First Name: ',
          value: individuals?.firstName,
        });
      }
    }
    if (field.name === 'lastName' && field.showInReport) {
      if (individuals?.lastName) {
        demographicArray.push({
          id: 'lastName',
          label: 'Last Name: ',
          value: individuals?.lastName,
        });
      }
    }
    if (field.name === 'phn' && field.showInReport) {
      if (individuals?.personalHealthNumber) {
        demographicArray.push({
          id: 'phn',
          label: 'PHN: ',
          value: individuals?.personalHealthNumber,
        });
      }
    }
    if (field.name === 'dateOfBirth' && field.showInReport) {
      if (individuals?.dateOfBirth) {
        demographicArray.push({
          id: 'birthdate',
          label: 'Date of Birth: ',
          value: individuals?.dateOfBirth,
        });
      }
    }
    if (field.name === 'gender' && field.showInReport) {
      if (individuals?.gender) {
        demographicArray.push({
          id: 'gender',
          label: 'Gender: ',
          value: individuals?.gender,
        });
      }
    }
    if (field.name === 'email' && field.showInReport) {
      if (demographic?.contact?.email) {
        demographicArray.push({
          id: 'email',
          label: 'Email: ',
          value: demographic?.contact?.email,
        });
      }
    }
    if (field.name === 'participantId') {
      if (individuals?.participantId) {
        demographicArray.push({
          id: 'participantId',
          label: 'Participant Id: ',
          value: individuals?.participantId,
        });
      }
    }
  });

  return demographicArray;
}

export function extractResultPageVariableName(section, variableCode) {
  let variableName;
  section.variables.forEach((variable) => {
    if (variable.variable === variableCode && variable.showInReport === true) {
      variableName = variable.variableName;
    }
  });
  return variableName;
}

export function extractResultPageSection(questionnaireJSON, extensionUrl, sectionType) {
  let sectionBlock = null;
  if (questionnaireJSON && questionnaireJSON.extension !== undefined) {
    let extension = extractExtension(questionnaireJSON.extension, extensionUrl);
    if (
      extension !== undefined &&
      extension !== null &&
      extension.valueString !== undefined &&
      extension.valueString !== null
    ) {
      let headerDefinition = JSON.parse(extension.valueString);
      headerDefinition.sections.forEach((section) => {
        if (section.type === sectionType && section.showInReport === true) {
          sectionBlock = section;
        }
      });
    }
  }
  return sectionBlock;
}

export const createDemographicUrlParams = (demographic) => {
  let demographicUrlParams = '';
  if (demographic) {
    demographicUrlParams = '?';

    demographicUrlParams = demographicUrlParams + `id=${demographic.id}&`;
    demographicUrlParams = demographicUrlParams + `resourceType=${demographic.resourceType}&`;
    if (demographic.individuals && demographic.individuals.length) {
      Object.keys(demographic.individuals[0]).map((key) => {
        demographicUrlParams = demographicUrlParams + `${key}=${demographic.individuals[0][key]}&`;
      });
    }
    if (demographic.contact) {
      Object.keys(demographic.contact).map((key) => {
        demographicUrlParams = demographicUrlParams + `${key}=${demographic.contact[key]}&`;
      });
    }
  }
  return demographicUrlParams;
};

export const getHashCode = (str) => {
  let hashCode = 0;
  if (str !== undefined && str !== null) {
    for (var i = 0; i < str.length; i++) {
      hashCode = (Math.imul(31, hashCode) + str.charCodeAt(i)) | 0;
    }
  }
  return hashCode;
};

export const isPdfTemplateAvailable = (questionnaire) => {
  if (!questionnaire) return false;

  const PDF_TEMPLATE_ID_EXTENSION_URL = 'pdftemplate-id';
  const pdfTemplateIdExtension = extractExtension(questionnaire?.extension, PDF_TEMPLATE_ID_EXTENSION_URL);

  return Boolean(pdfTemplateIdExtension?.valueString);
};

export const extractGeneratedPdfReport = (questionnaireResponse) => {
  if (!questionnaireResponse) return '';

  const PDF_REPORT_EXTENSION_URL = 'generated-pdf-report';
  const generatedPdfExtension = extractExtension(questionnaireResponse?.extension, PDF_REPORT_EXTENSION_URL);
  const generatedPdf = generatedPdfExtension ? generatedPdfExtension.valueString : '';

  return generatedPdf;
};

export const extractInstrumentScores = (questionnaireResponse) => {
  if (!questionnaireResponse) return [];

  const INSTRUMENT_SCORES_EXTENSION_URL = 'calculated-scores';

  const calculatedScoresExtension = extractExtension(questionnaireResponse.extension, INSTRUMENT_SCORES_EXTENSION_URL);
  const calculatedScores = calculatedScoresExtension ? calculatedScoresExtension.extension : [];

  let scoresExtension;
  if (calculatedScores && calculatedScores.length) {
    scoresExtension = extractAllMatchingExtension(calculatedScores, 'score');
  }

  let instrumentScores = [];
  if (scoresExtension && scoresExtension.length) {
    scoresExtension.forEach((score) => {
      let variableScore = score.valueString;
      if (variableScore) {
        variableScore = variableScore.split(':');
        instrumentScores.push({
          scoreDefinitionName: variableScore[0],
          score: variableScore[1],
        });
      }
    });
  }

  return instrumentScores;
};

export const getMatchedActionCondition = (actions, instrumentScores) => {
  let matchedAction;
  if (!actions?.length || !instrumentScores?.length) return matchedAction;

  for (let i = 0; i < actions.length; i++) {
    for (let j = 0; j < instrumentScores.length; j++) {
      const calculatedScore = Number(instrumentScores[j]?.score);
      const configuredScore = Number(actions[i]?.scoreValue);

      if (instrumentScores[j]?.scoreDefinitionName === actions[i]?.scoreDefinitionName) {
        switch (actions[i]?.selectedScore) {
          case EQUAL_TO:
            if (calculatedScore == configuredScore) {
              matchedAction = actions[i];
              i = actions.length;
              j = instrumentScores.length;
            }
            break;
          case GREATER_THAN:
            //actual score > rule score
            if (calculatedScore > configuredScore) {
              matchedAction = actions[i];
              i = actions.length;
              j = instrumentScores.length;
            }
            break;
          case GREATER_THAN_EQUAL:
            if (calculatedScore >= configuredScore) {
              matchedAction = actions[i];
              i = actions.length;
              j = instrumentScores.length;
            }
            break;
          case LESS_THAN:
            if (calculatedScore < configuredScore) {
              matchedAction = actions[i];
              i = actions.length;
              j = instrumentScores.length;
            }
            break;
          case LESS_THAN_EQUAL:
            if (calculatedScore <= configuredScore) {
              matchedAction = actions[i];
              i = actions.length;
              j = instrumentScores.length;
            }
            break;

          case BETWEEN:
            if (calculatedScore > Number(actions[i]?.scoreFrom) && calculatedScore < Number(actions[i]?.scoreTo)) {
              matchedAction = actions[i];
              i = actions.length;
              j = instrumentScores.length;
            }
            break;
          default:
            break;
        }
      }
    }
  }

  return matchedAction;
};
