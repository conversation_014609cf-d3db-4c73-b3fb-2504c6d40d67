/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import { RESEND_OTP_ERROR, RESEND_OTP_REQUEST, RESEND_OTP_SUCCESS } from '../../../redux/actions/resendOtp';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiResendOtp } from './api';
/**
 *
 * @param {*} action
 */

export function* resendOtp(action) {
  try {
    const response = yield call(makeApiResendOtp, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: RESEND_OTP_SUCCESS, payload: data });
    } else {
      yield put({ type: RESEND_OTP_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: RESEND_OTP_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchResendOtp() {
  yield takeEvery(RESEND_OTP_REQUEST, resendOtp);
}
