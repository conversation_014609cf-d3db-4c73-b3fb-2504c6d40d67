import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_REQUEST,
  SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_SUCCESS,
  SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_ERROR,
  SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_RESET,
} from '../../actions/saveQuestionnaireResponseIndividual';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const saveQuestionnaireResponseIndividualReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;

  switch (type) {
    case SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_REQUEST:
      return {
        ...state,
        isSaveQuestionnaireResponseIndividualFetching: true,
        isSaveQuestionnaireResponseIndividualSuccess: false,
        saveQuestionnaireResponseIndividualSuccessData: false,
        isSaveQuestionnaireResponseIndividualError: null,
      };
    case SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_SUCCESS: {
      return {
        ...state,
        isSaveQuestionnaireResponseIndividualFetching: false,
        isSaveQuestionnaireResponseIndividualSuccess: true,
        saveQuestionnaireResponseIndividualSuccessData: payload,
        isSaveQuestionnaireResponseIndividualError: false,
        saveQuestionnaireResponseIndividualErrorData: null,
      };
    }
    case SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_ERROR:
      return {
        ...state,
        isSaveQuestionnaireResponseIndividualFetching: false,
        isSaveQuestionnaireResponseIndividualSuccess: false,
        isSaveQuestionnaireResponseIndividualError: true,
        saveQuestionnaireResponseIndividualErrorData: payload,
      };
    case SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_RESET:
      return {
        ...state,
        isSaveQuestionnaireResponseIndividualFetching: undefined,
        isSaveQuestionnaireResponseIndividualSuccess: undefined,
        saveQuestionnaireResponseIndividualSuccessData: undefined,
        isSaveQuestionnaireResponseIndividualError: undefined,
        saveQuestionnaireResponseIndividualErrorData: undefined,
      };
    default:
      return state;
  }
};
