/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import {
  GET_NEXT_AVAILABLE_SLOT_ERROR,
  GET_NEXT_AVAILABLE_SLOT_SUCCESS,
  GET_NEXT_AVAILABLE_SLOT_REQUEST,
} from '../../actions/getNextAvailableSlot';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const getNextAvailableSlotReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case GET_NEXT_AVAILABLE_SLOT_REQUEST:
      return {
        ...state,
        isNextAvailableSlotFetching: true,
        isNextAvailableSlotSuccess: false,
        isNextAvailableSlotError: false,
        nextAvailableSlotErrorData: null,
      };
    case GET_NEXT_AVAILABLE_SLOT_SUCCESS:
      return {
        ...state,
        isNextAvailableSlotFetching: false,
        isNextAvailableSlotSuccess: true,
        isNextAvailableSlotError: false,
        nextAvailableSlotErrorData: null,
        nextAvailableSlotSuccessData: payload,
      };
    case GET_NEXT_AVAILABLE_SLOT_ERROR:
      return {
        ...state,
        isNextAvailableSlotFetching: false,
        isNextAvailableSlotSuccess: false,
        isNextAvailableSlotError: true,
        nextAvailableSlotErrorData: payload,
      };
    default:
      return state;
  }
};
