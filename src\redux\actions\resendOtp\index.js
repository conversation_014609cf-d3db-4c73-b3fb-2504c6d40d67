/*
 * RESEND_OTP action types
 */

export const RESEND_OTP_REQUEST = 'RESEND_OTP_REQUEST';
export const RESEND_OTP_SUCCESS = 'RESEND_OTP_SUCCESS';
export const RESEND_OTP_ERROR = 'RESEND_OTP_ERROR';
export const RESEND_OTP_RESET = 'RESEND_OTP_RESET';

/*
 * action creators
 */
export function resendOtp(data) {
  return {
    type: RESEND_OTP_REQUEST,
    payload: data,
  };
}

export function resendRegistrationOtpSuccess(data) {
  return {
    type: RESEND_OTP_SUCCESS,
    payload: data,
  };
}

export function resendRegistrationOtpError() {
  return {
    type: RESEND_OTP_ERROR,
  };
}

export const resetResendOtp = () => {
  return {
    type: RESEND_OTP_RESET,
  };
};
