import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/cancelBooking/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { cancelBooking } from './index';
/**
 * This function test test case for get cancelBooking details saga
 * Fires get cancelBooking success of api gives success
 * Fires get cancelBooking error of api fails
 */

describe('cancelBooking', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.cancelBookingApi = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.cancelBooking(DUMMY_ITEM.data);
    await runSaga(fakeStore, cancelBooking, requestResult).done;

    let successResult = actions.cancelBookingSuccess(DUMMY_ITEM.data);

    expect(api.cancelBookingApi.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.cancelBookingApi = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.cancelBooking(DUMMY_ITEM.data);
    await runSaga(fakeStore, cancelBooking, requestResult).done;

    expect(api.cancelBookingApi.mock.calls.length).toBe(1);

    let errorResult = actions.cancelActionBookingError(DUMMY_ITEM.data);
    expect(dispatchedActions).toContainEqual(errorResult);
  });
});
