/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import {
  ASSIGN_QUESTIONNAIRE_REQUEST,
  ASSIGN_QUESTIONNAIRE_SUCCESS,
  ASSIGN_QUESTIONNAIRE_ERROR,
} from '../../../redux/actions/AssignQuestionnaire';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiFetchAssignQuestionnaire } from './api';
/**
 *
 * @param {*} action
 */
/**
 
/**
 *
 * @param {*} action
 */

export function* assignQuestionnaire(action) {
  try {
    const response = yield call(makeApiFetchAssignQuestionnaire, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: ASSIGN_QUESTIONNAIRE_SUCCESS, payload: data });
    } else {
      yield put({ type: ASSIGN_QUESTIONNAIRE_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: ASSIGN_QUESTIONNAIRE_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchAssignQuestionnaire() {
  yield takeEvery(ASSIGN_QUESTIONNAIRE_REQUEST, assignQuestionnaire);
}
