{"apiError": "Désolé, il y a eu un problème avec votre demande.", "otpSendError": "Désolé, il y a eu un problème lors de l'envoi du code OTP", "invalidOtp": "Code OTP invalide", "multiplePatientError": "Plusieurs patients trouvés avec les mêmes détails", "invalidHealthCardNumber": "Numéro de carte santé invalide", "clientCreationDisallowedError": "Échec de la création du dossier client. Création de client non autorisée.", "clientCreationDataNotExistsError": "Il semble y avoir un problème avec les données que vous avez saisies. Veuillez vous assurer que les informations correspondent exactement à celles figurant sur votre pièce d'identité gouvernementale.", "failedToDownloadCalenderFile": "Échec du téléchargement du fichier de calendrier", "someErrorOccurred": "Une erreur s'est produite", "errorGettingUserLocation": "Erreur dans l'obtention de l'emplacement de l'utilisateur", "pleaseAllowUsLocationPermission": "Veuillez nous accorder une autorisation de localisation", "loading": "Chargement", "none": "Aucun", "individual": "Individu", "bookNow": "Réserver maintenant", "bookAgain": "Réserver à nouveau", "searchALocation": "Entrer un emplacement", "selectALocation": "Sélectionner un emplacement", "bookingAppointment": "Réserver un rendez-vous", "bookNewAppointment": "Réserver un nouveau rendez-vous", "cancelAppointment": "<PERSON><PERSON><PERSON> le rendez-vous", "cancelMainHeading": "Voulez-vous annuler votre rendez-vous ?", "cancelSubHeading": "<PERSON> vous continuez, le rendez-vous suivant sera annulé.", "cancelSummaryHeading": "<PERSON><PERSON><PERSON>vous annulé", "cancelSummarySubHeading": "Le rendez-vous suivant a été annulé.", "backToAppointmentSummary": "Retour au résumé du rendez-vous", "rescheduleAppointment": "Reprogrammer le rendez-vous", "cancelledAppointmentMessage": "Ce rendez-vous a déjà été annulé, veuillez en réserver un nouveau.", "rescheduleNow": "Reprogrammer maintenant", "rescheduleSubHeading": "<PERSON> vous continuez, le rendez-vous suivant sera reprogrammé.", "rescheduleConfirmation": "Confirmation de reprogrammation", "rescheduleMessage": "Si vous avez ajouté le rendez-vous précédent à votre calendrier, vous devrez <strong>le supprimer ou le modifier</strong> car il n'est plus valide", "next": "Suivant", "noSlots": "Aucun rendez-vous disponible pour la date sélectionnée", "individuals": "<PERSON><PERSON><PERSON><PERSON>", "appointmentDetails": "<PERSON><PERSON><PERSON> du rendez-vous", "service": "Service", "appointment": "<PERSON><PERSON><PERSON>vous", "location": "Emplacement", "selectService": "Sélectionner un service", "selectAppointment": "Sélectionner un rendez-vous", "selectDateTime": "Sélectionner la date et l'heure", "dateAndTime": "Date et heure", "addToCalendar": "A<PERSON>ter au calendrier", "makeChanges": "Apporter des modifications", "otpSent": "Un code de vérification a été envoyé", "code": "Code", "emailAddress": "Adresse e-mail", "phoneNumber": "Numéro de téléphone", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "gmtFormat": "Format gmt", "reviewDetails": "Examiner les d<PERSON>", "appointmentCancelled": "<PERSON><PERSON><PERSON>vous annulé", "appointmentCancelledText": "Le rendez-vous suivant a été annulé.", "dateFormat": "2021-01-15T", "moreDetails": "Plus de détails", "lessDetails": "<PERSON><PERSON> de <PERSON>", "yourInformationVerifiedSuccessfully": "Vos informations ont été vérifiées avec succès", "failedToVerifyInformation": "Échec de la vérification des informations", "contactInformation": "Informations de contact", "back": "Retour", "addIndividual": "Ajouter un individu", "personalInformation": "Informations Personnelles", "organizationUserCambianUser": "Utilisateur de l'organisation : Utilisateur Cambian", "clientSearch": "Recherche de client", "otpError": "Nous avons des difficultés à vérifier ce code. Veuillez réessayer.", "existingAccountError": "Cette adresse e-mail est déjà utilisée pour un compte existant. Veuillez vous connecter ou réinitialiser le mot de passe", "otpValidationSubHeadingEmail": "Si vous ne le voyez pas, vérifiez votre dossier spam", "otpValidationSubHeadingPhone": "Entrez le code de vérification que nous avons envoyé à votre téléphone mobile.", "VerificationCodeSentTo": "Entrez le code de vérification envoyé à votre ", "didNotReceiveOTP": "Vous n'avez pas reçu le code de vérification ?", "resendEmail": "Renvoyer l'e-mail", "resendText": "Ren<PERSON><PERSON> le <PERSON>", "resendOtp": "Renvoyer le code OTP", "resendCode": "Renvoyer le code", "goBack": "Retour", "toEnterNewEmail": "pour entrer une nouvelle adresse e-mail", "toEnterNewPhone": "pour entrer un nouveau numéro de téléphone", "confirmationNeeded": "Confirmation nécessaire", "verificationCode": "Code de vérification", "changeYourEmail": "Changer votre adresse e-mail", "changeYourPhone": "Changer votre numéro de téléphone", "submit": "So<PERSON><PERSON><PERSON>", "requiredField": "Obligatoire", "emailDuplicate": "Email dé<PERSON>à existant", "phoneNumberDuplicate": "Téléphone déjà existant", "allowNotificationsValid": "Paramètre de notification invalide", "subscribeToNotificationsValid": "Sélectionnez <PERSON> ou <PERSON>", "dateValid": "Date invalide", "healthCardNumber": "Numéro de carte santé", "personalHealthNumber": "Numéro de santé personnel", "vaccinationHistory": "Historique de vaccination", "upcomingAppointments": "<PERSON>dez-vous à venir", "dose": "<PERSON><PERSON>", "serviceSuggestionText": "En fonction de votre âge et de votre historique de vaccination, vous êtes éligible pour recevoir l'une des options suivantes :", "reschedule": "Reprogrammer", "cancel": "Annuler", "thankYouDescription1": "Pour voir votre questionnaire complété, vous devez vous inscrire à un compte Cambian Navigator. Vous pouvez le faire en cliquant sur le lien dans l'e-mail que nous venons de vous envoyer et en entrant le code PIN suivant lorsque demandé.", "thankYouDescription2": "<PERSON>rci d'avoir rempli le questionnaire.", "pin": "Code PIN", "print": "<PERSON><PERSON><PERSON><PERSON>", "ok": "OK", "noServicesForYou": "Dés<PERSON><PERSON>, vous n'êtes pas éligible pour aucun service.", "printReport": "Imprimer le rapport", "downloadReport": "Télécharger le rapport", "agreeToConsent": "J'ai lu et j'accepte les", "consentAgreement": "Conditions d'utilisation", "outlook": "Outlook", "firstNameValid": "Lettres uniquement", "firstNameValidationMin": "Min 2 caractères", "middleNameValid": "Lettres uniquement", "middleNameValidationMin": "Min 2 caractères", "lastNameValid": "Lettres uniquement", "lastNameValidationMin": "Min 2 caractères", "emailValidationText": "<PERSON><PERSON> invalide", "phoneNumberValid": "Téléphone invalide", "phoneNumberLengthValidationText": "6-14 chiffres requis", "addressValidationMin": "Min 2 caractères", "countryValidationMin": "Min 2 caractères", "cityValidationMin": "Min 2 caractères", "provinceValidationMin": "Min 2 caractères", "postalCodeValidationMin": "Min 6 caractères", "bcPersonalHealthNumberMin": "10 chiffres requis", "bcPersonalHealthNumberValid": "Chiffres uniquement", "ytPersonalHealthNumberMin": "Format: 00XXXXXXX", "ytPersonalHealthNumberValid": "Chiffres uniquement", "signIn": "Se connecter", "login": "Connexion", "logout": "Déconnexion", "register": "S'inscrire", "registerAndConnect": "S'inscrire et se connecter", "registration": "Inscription", "haveCambianAccount": "<PERSON>z-vous déjà utilisé Cambian ?", "signInToYourAccount": "Connectez-vous à votre compte", "useYourCambianAccount": "Utilisez votre compte Cambian", "forgotPassword": "Mot de passe oublié ?", "dontHaveCambianAccount": "Vous n'avez pas de compte Cambian ?", "registerAccount": "Inscrivez-vous", "thankYou": "<PERSON><PERSON><PERSON>", "anEmailSent": "Un e-mail a été envoyé à", "registrationMessageSubText1": "Lors de votre inscription, nous envoyons un message de confirmation intitulé \"Confirmer la demande de connexion\" à l'adresse e-mail que vous avez fournie. Si vous cliquez sur le lien dans ce message, votre adresse e-mail sera vérifiée.", "registrationMessageSubText2": "L'e-mail de confirmation provie<PERSON> et vous serez invité à choisir un mot de passe pour utiliser le service.", "feelFreeToContact": "N'hésitez pas à nous contacter si vous avez des questions.", "saveLaterHeading": "Enregistrer pour plus tard", "saveLaterDescription": "Votre réponse a été enregistrée. Vous pouvez continuer votre réponse en vous connectant à votre compte Cambian Navigator.", "discardHeading": "<PERSON><PERSON><PERSON><PERSON>", "discardDescription": "La réponse a été supprimée.", "registrationMessageDescription": "Demande de connexion envoyée ! Vérifiez votre compte Navigator pour l'invitation à vous connecter avec nous.", "preferredContactMethod": "Méthode de contact préférée", "email": "E-mail", "phone": "Téléphone", "publicHealthJurisdiction": "Juridiction de santé publique", "firstNamePlaceholder": "Prénom", "lastNamePlaceholder": "Nom de famille", "middleNamePlaceholder": "Deuxième prénom", "emailPlaceholder": "E-mail", "phonePlaceholder": "Numéro de téléphone", "addressPlaceholder": "<PERSON><PERSON><PERSON>", "addressLine1Placeholder": "Ligne d'adresse 1", "addressLine2Placeholder": "Ligne d'adresse 2", "countryPlaceholder": "Pays", "cityPlaceholder": "Ville", "provincePlaceholder": "Province", "postalCodePlaceholder": "Code postal", "personalHealthNumberPlaceholder": "Numéro de santé personnel", "consentAgreementError": "Échec de la récupération des conditions d'utilisation. Veuillez réessayer plus tard", "individualNotFoundHeading": "Client non trouvé", "individualNotFoundDescription": "Veuillez nous contacter", "Book an Appointment at a location near you": "Prenez rendez-vous dans un emplacement près de chez vous", "Search by address, city or postal code": "Rechercher par adresse, ville ou code postal", "Use my location": "Utiliser ma position", "Radius": "Rayon", "additionalInformation": "Informations supplémentaires", "confirmation": "Confirmation", "confirmAppointment": "Confirmer le rendez-vous", "preConfirmationMessage": "Un rappel vous sera envoyé avant votre rendez-vous", "confirmationMessage": "Ce rendez-vous est confirmé", "cancelText": "Ce rendez-vous a déjà été annulé, veuillez en réserver un nouveau.", "nextStep": "Étape suiva<PERSON>", "Manage Notification Preferences": "<PERSON><PERSON><PERSON> les préférences de notification", "subscribeToNotifications": "S'abonner aux notifications", "Click here": "Cliquez ici", "Receive notifications about this appointment, including reminders.": "Recevez des notifications concernant ce rendez-vous, y compris des rappels.", "Submit": "So<PERSON><PERSON><PERSON>", "Manage Notification": "<PERSON><PERSON><PERSON> les notifications", "noClinicsFound": "Aucune clinique n'a été trouvée. Essayez d'élargir le rayon d'action ou de choisir un autre lieu.", "pleaseVerifyYourInformation": "Veuillez vérifier vos informations", "appointmentCancellationMessage": "Votre rendez-vous a été annulé", "appointmentCancelModalHeaderText": "<PERSON><PERSON><PERSON> le rendez-vous", "appointmentCancelModalMessage": "Veuillez confirmer votre préférence", "appointmentCancelModalCancelText": "Conserver le rendez-vous", "appointmentCancelModalActionText": "<PERSON><PERSON><PERSON> le rendez-vous", "dynamicIdError": "Erreur d'ID dynamique", "questionnaireIdIsInvalid": "L'ID du questionnaire est invalide", "requestNotFound": "Demande non trouvée", "Please get in touch with us": "Veuillez nous contacter", "confirmationPageHeading": "<PERSON><PERSON>-vous non trouvé", "appointmentNotFoundHeading": "<PERSON><PERSON>in nicht gefunden", "appointmentNotFoundDescription": "Prenez contact avec nous", "confirmationPageDescription": "Après vous être identifié avec vos informations personnelles, vous pourrez confirmer, reprogrammer ou annuler votre rendez-vous.", "Active connection already exists": "Une connexion active existe déjà", "Change language": "Changer de langue", "English": "English", "French": "Français", "Widget": "Widget", "previous": "Précédent", "done": "<PERSON><PERSON><PERSON><PERSON>", "yes": "O<PERSON>", "no": "Non", "gender": "Genre", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Fé<PERSON>n", "unknown": "Inconnu", "other": "<PERSON><PERSON>", "dateOfBirth": "Date de naissance", "address": "<PERSON><PERSON><PERSON>", "Add ID": "Ajouter une pièce d'identité", "Verify your email address": "Vérifier votre adresse e-mail", "Verify your phone number": "Vérifier votre numéro de téléphone", "Enter the verification code sent to": "Entrez le code de vérification envoyé à", "If you do not see it, check your spam folder.": "Si vous ne le voyez pas, vérifiez votre dossier spam.", "Enter your verification code": "Entrez votre code de vérification", "Verification code": "Code de vérification", "We are having trouble verifying that code. Please try again.": "Nous avons des difficultés à vérifier ce code. Veuillez réessayer.", "Cancel": "Annuler", "Next": "Suivant", "Something went wrong. Please try again.": "Une erreur s'est produite. Veuillez réessayer.", "Verified": "Vérifié", "Click to verify": "Cliquer pour vérifier", "Primary": "Principal", "Set as Primary": "Définir comme principal", "Delete email": "Supprimer l'e-mail", "Delete phone": "Supp<PERSON>er le téléphone", "Did not get the code?": "Vous n'avez pas reçu le code ?", "Resend the code": "Renvoyer le code", "Sign out": "Se déconnecter", "Address Line 1": "Ligne d'adresse 1", "Address Line 2": "Ligne d'adresse 2", "City": "Ville", "Province": "Province", "Country": "Pays", "Postal Code": "Code postal", "Type": "Type", "Issuer": "<PERSON><PERSON><PERSON>", "ID": "ID", "Delete Address": "Supp<PERSON>er l'adresse", "Add Address": "Ajouter une adresse", "addEmail": "Ajouter un email", "addPhone": "Ajouter un téléphone", "idMinLength": "Min 2 caractères", "Enter Verification Code": "Entrez le code de vérification", "A verification code has been sent to ": "Un code de vérification a été envoyé à ", ". Please enter it to verify your email address.": ". Veuillez l'entrer pour vérifier votre adresse e-mail.", ". Please enter it to verify your phone number.": ". Veuillez l'entrer pour vérifier votre numéro de téléphone.", "Verify": "Vérifier", "Resend Verification Code": "Renvoyer le code de vérification", "You must verify your email to continue. ": "V<PERSON> devez vérifier votre email pour continuer. ", "You must verify your phone to continue. ": "V<PERSON> devez vérifier votre téléphone pour continuer. "}