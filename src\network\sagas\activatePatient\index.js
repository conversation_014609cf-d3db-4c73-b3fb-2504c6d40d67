/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import {
  ACTIVATE_PATIENT_ERROR,
  ACTIVATE_PATIENT_REQUEST,
  ACTIVATE_PATIENT_SUCCESS,
} from '../../../redux/actions/activatePatient';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiActivatePatient } from './api';
/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function* activatePatient(action) {
  try {
    const response = yield call(makeApiActivatePatient, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: ACTIVATE_PATIENT_SUCCESS, payload: data });
    } else {
      yield put({ type: ACTIVATE_PATIENT_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: ACTIVATE_PATIENT_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchActivatePatient() {
  yield takeEvery(ACTIVATE_PATIENT_REQUEST, activatePatient);
}
