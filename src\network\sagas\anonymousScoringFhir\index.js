/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { makeApiFetchAnonymousScoringFhir } from './api';
import {
  ANONYMOUS_SCORING_FHIR_ERROR,
  ANONYMOUS_SCORING_FHIR_REQUEST,
  ANONYMOUS_SCORING_FHIR_SUCCESS,
} from '../../../redux/actions/anonymousScoringFhir';
/**
 *
 * @param {*} action
 */

export function* fetchAnonymousScoringFhir(action) {
  try {
    const response = yield call(makeApiFetchAnonymousScoringFhir, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: ANONYMOUS_SCORING_FHIR_SUCCESS, payload: data });
    } else {
      yield put({ type: ANONYMOUS_SCORING_FHIR_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: ANONYMOUS_SCORING_FHIR_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchAnonymousScoringFhir() {
  yield takeEvery(ANONYMOUS_SCORING_FHIR_REQUEST, fetchAnonymousScoringFhir);
}
