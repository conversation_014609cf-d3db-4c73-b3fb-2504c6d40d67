import { DOWNLOAD_REPORT_AS_PDF } from '@/utils/constants/awsApiEndpoints';
import { makeNetworkCall } from '../..';

export function makeApiGenerateHtmlReportAsPdf(action) {
  const { payload = {} } = action || {};
  const { headers = {}, data } = payload || {};

  const mainUrl = DOWNLOAD_REPORT_AS_PDF;

  const config = {
    method: 'POST',
    url: mainUrl,
    headers: headers,
    data: data,
  };

  return makeNetworkCall(config);
}
