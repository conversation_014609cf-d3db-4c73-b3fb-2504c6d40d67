/*
 *
 */
import { makeNetworkCall } from '../..';
import { GET_USER_INFO, BASE_URL, CAMBIAN_SERVICE_BASE_URL } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiFetchUserInfo(action) {
  const { payload = {} } = action || {};
  const { headers = {} } = payload || {};
  const config = {
    method: 'GET',
    url: CAMBIAN_SERVICE_BASE_URL + GET_USER_INFO,
    headers: headers,
    formData: false,
  };
  return makeNetworkCall(config);
}
