import { useEffect, useState } from 'react';
import { getQuestionnaireWidget } from '@/redux/actions/getQuestionnaireWidget';
import { useDispatch, useSelector } from 'react-redux';
import { getOrganizationAndWidgetId, getParamFromUrl } from '../commonUtility';
import useNotification from '@/hooks/useNotification';
import { useTranslation } from 'react-i18next';
import { HeadingAndDescription, Loader, PageNotFound } from '@/components';
import { QuestionnaireWidget } from './components';
import { resetAllAPIReducersIncludingQuestionnaire } from './questionnaireUtility';
import { demoQuestionnaireWidget } from '@/utils/data/demoQuestionnaire';
import { fetchQuestionnaireQuestions } from '@/redux/actions/getQuestionnaireDetails';

/*
 * questionnairePreview from QE
 * widgetId 1, qid from QE -> use hardcoded widget fetch questionnaire by using the Id from the URL, Repo from the URL.
 * widget -> dynamic, qid from QE -> pull widget config from BE & fetch questionnaire by using the Id from the URL, repo from the widget config.
 * widget -> normal widget, would work as normal widget, qid from the URL will be ignored.
 */

export const QuestionnaireWidgetContainer = () => {
  const dispatch = useDispatch();
  const [, sendToast] = useNotification();
  const { t } = useTranslation();
  const [organizationId, widgetId] = getOrganizationAndWidgetId();
  const [isResetDone, setIsResetDone] = useState(false);
  const isPreviewWidget = widgetId === '1';
  const dynamicQuestionnaireId = getParamFromUrl('qid');
  const repositoryQuery = getParamFromUrl('repository')?.toUpperCase() || '';

  const {
    isQuestionnaireWidgetFetching,
    isQuestionnaireWidgetSuccess,
    isQuestionnaireWidgetError,
    questionnaireWidgetSuccessData,
    questionnaireWidgetErrorData,
  } = useSelector((state) => state.questionnaireWidgetReducer) || {};

  const { isQuestionnaireQuestionsFetching, isQuestionnaireQuestionsError, questionnaireQuestionsSuccessData } =
    useSelector((state) => state.getQuestionnaireQuestionRedux);

  useEffect(() => {
    if (isResetDone === false) {
      setIsResetDone(true);
      resetAllAPIReducersIncludingQuestionnaire(dispatch);
    }
  }, []);

  useEffect(() => {
    if (isResetDone) {
      if (isPreviewWidget) {
        document.title = demoQuestionnaireWidget.widgetTitle;
        if (dynamicQuestionnaireId && !questionnaireQuestionsSuccessData) {
          dispatch(
            fetchQuestionnaireQuestions({
              questionnaireId: dynamicQuestionnaireId,
              repository: repositoryQuery,
              organizationId,
            }),
          );
        }
        return;
      } else {
        dispatch(getQuestionnaireWidget({ organizationId, widgetId }));
      }
    }
  }, [isResetDone]);

  useEffect(() => {
    if (isResetDone) {
      if (isQuestionnaireWidgetSuccess && questionnaireWidgetSuccessData) {
        document.title = questionnaireWidgetSuccessData?.widgetTitle || 'Widget';

        const questionnaireId = questionnaireWidgetSuccessData?.dynamicWidget
          ? dynamicQuestionnaireId
          : questionnaireWidgetSuccessData?.questionnaire?.artifactId;
        const repository = questionnaireWidgetSuccessData?.dynamicWidget
          ? repositoryQuery
          : questionnaireWidgetSuccessData?.repository;

        if (!questionnaireId) return;

        dispatch(
          fetchQuestionnaireQuestions({
            questionnaireId: questionnaireId,
            repository: repository,
            organizationId,
          }),
        );
      } else if (isQuestionnaireWidgetError && questionnaireWidgetErrorData) {
        sendToast({ msg: t('apiError'), variant: 'error' });
      }
    }
  }, [
    isResetDone,
    isQuestionnaireWidgetSuccess,
    isQuestionnaireWidgetError,
    questionnaireWidgetSuccessData,
    questionnaireWidgetErrorData,
  ]);

  if (isResetDone === false) {
    return <Loader active={true} />;
  }

  if (isPreviewWidget) {
    return <QuestionnaireWidget questionnaireWidgetSuccessData={demoQuestionnaireWidget} />;
  }

  if (isQuestionnaireWidgetError && questionnaireWidgetErrorData) {
    return (
      <>
        <PageNotFound />
      </>
    );
  }

  // Dynamic ID error page
  if (questionnaireWidgetSuccessData?.dynamicWidget && (!dynamicQuestionnaireId || isQuestionnaireQuestionsError)) {
    const dynamicIdErrorPage = questionnaireWidgetSuccessData?.dynamicIdErrorPage;
    const dynamicIdErrorPageDetails = {
      heading: dynamicIdErrorPage?.heading || t('questionnaireIdIsInvalid'),
      description: dynamicIdErrorPage?.description || t('Please get in touch with us'),
    };

    return <HeadingAndDescription headingAndDescriptionData={dynamicIdErrorPageDetails} showNextButton={false} />;
  }

  return (
    <>
      <Loader active={isQuestionnaireWidgetFetching || isQuestionnaireQuestionsFetching} />
      {questionnaireWidgetSuccessData && (
        <QuestionnaireWidget questionnaireWidgetSuccessData={questionnaireWidgetSuccessData} />
      )}
    </>
  );
};
