/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  POST_QUESTIONNAIRE_QUESTIONS_ERROR,
  POST_QUESTIONNAIRE_QUESTIONS_SUCCESS,
  POST_QUESTIONNAIRE_QUESTIONS_REQUEST,
} from '../../../redux/actions/SubmitQuestionnaire';

import { makeApiFetchQuestionnaireSubmitAnswers } from './api';

/**
 *
 * @param {*} action
 */

export function* fetchQuestionnaireSubmitAnswers(action) {
  try {
    const response = yield call(makeApiFetchQuestionnaireSubmitAnswers, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: POST_QUESTIONNAIRE_QUESTIONS_SUCCESS, payload: data });
    } else {
      yield put({ type: POST_QUESTIONNAIRE_QUESTIONS_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: POST_QUESTIONNAIRE_QUESTIONS_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchQuestionnaireSubmitAnswers() {
  yield takeEvery(POST_QUESTIONNAIRE_QUESTIONS_REQUEST, fetchQuestionnaireSubmitAnswers);
}
