import { Box, Grid, Button, Typography, Paper } from '@mui/material';
import { actions } from '../../commonConstants';
import { useTranslation } from 'react-i18next';

export const Services = (props) => {
  const { services, handleNavigationCallback } = props;
  const { t } = useTranslation();
  return (
    <Box>
      {services?.length ? (
        services.map((service, index) => (
          <Paper sx={{ bgcolor: 'background.secondary', p: 1, my: 2 }} key={index}>
            <Grid container sx={{ p: 1 }}>
              <Grid item xs={12} sm={6} md={6} lg={6} sx={{ pt: 0.6 }}>
                <Typography>
                  <>{service.display}</>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={6} lg={6} sx={{ textAlign: { sm: 'right', xs: 'center' } }}>
                <Button
                  variant="outlined"
                  size="large"
                  color="primary"
                  onClick={() => handleNavigationCallback(actions.NEXT, service)}
                >
                  {t('select')}
                </Button>
              </Grid>
            </Grid>
          </Paper>
        ))
      ) : (
        <Typography variant="h6">{t('noServicesForYou')}</Typography>
      )}
    </Box>
  );
};
