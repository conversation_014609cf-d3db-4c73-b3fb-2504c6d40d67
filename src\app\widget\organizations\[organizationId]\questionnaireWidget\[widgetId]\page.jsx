'use client';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { QuestionnaireWidgetContainer } from '@/containers';
import { resetAllAPIReducers } from '@/containers/QuestionnaireWidget/questionnaireUtility';

export default function QuestionnaireWidgetPage() {
  const dispatch = useDispatch();

  useEffect(() => {
    resetAllAPIReducers(dispatch);
  }, []);

  return <QuestionnaireWidgetContainer />;
}
