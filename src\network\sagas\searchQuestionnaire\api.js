/*
 *
 */
import { makeNetworkCall } from '../..';
import { BASE_URL, SEARCH_QUESTIONNAIRE_URL } from '../../../utils/constants/apiEndpoints';

import {
  SEARCH_PATIENTS,
  ORGANIZATION_ID,
  WIDGET_ID,
  BIRTH_DATE,
  FAMILY,
  GIVEN,
  EMAIL,
  MATCHANY,
  PHN,
  UNIQUEKEY,
  GENDER,
} from '../../../utils/constants';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiFetchSearchQuestionnaire(action) {
  const { payload = {} } = action || {};
  const {
    headers = {},
    organizationId,
    widgetId,
    searchPatients,
    birthDate,
    email,
    family = '',
    given,
    gender = '',
    phn,
    uniquekey,
    matchany,
  } = payload || {};

  const mainUrl =
    BASE_URL +
    SEARCH_QUESTIONNAIRE_URL.replace(ORGANIZATION_ID, organizationId)
      .replace(WIDGET_ID, widgetId)
      .replace(SEARCH_PATIENTS, searchPatients)
      .replace(BIRTH_DATE, birthDate)
      .replace(FAMILY, family)
      .replace(EMAIL, email)
      .replace(GIVEN, given)
      .replace(GENDER, gender)
      .replace(PHN, phn)
      .replace(UNIQUEKEY, uniquekey)
      .replace(MATCHANY, matchany);

  const config = {
    method: 'GET',
    url: mainUrl,
    headers: headers,
    formData: false,
  };
  return makeNetworkCall(config);
}
