import React, { Fragment } from 'react';
import { Box, Button, Grid, Typography, Paper } from '@mui/material';
import { useSelector } from 'react-redux';
import { actions } from '@/containers/commonConstants';
import { Loader } from '@/components';
import { useTranslation } from 'react-i18next';

function Service(props) {
  console.log('TRACE: Service');

  const { isWidgetDetailsFetching, widgetDetailsSuccessData } = useSelector((state) => state.widgetDetailsReducer);

  const { handleNavigationCallback } = props;
  const { t } = useTranslation();
  const { services = [] } = widgetDetailsSuccessData || {};

  const renderServicesList = (service) => {
    return (
      <Paper sx={{ bgcolor: '#f0f0f0', p: 1, my: 2 }}>
        <Grid container sx={{ p: 1 }}>
          <Grid item xs={12} sm={6} md={6} lg={6} sx={{ pt: 0.6 }}>
            <Typography>
              <strong>{service.display}</strong>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={6} lg={6} sx={{ textAlign: { sm: 'right', xs: 'center' } }}>
            <Button
              variant="outlined"
              size="large"
              color="primary"
              onClick={() => handleNavigationCallback(actions.NEXT, service)}
            >
              {t('select')}
            </Button>
          </Grid>
        </Grid>
      </Paper>
    );
  };
  return (
    <Box sx={{ py: 2 }}>
      {isWidgetDetailsFetching ? (
        <Loader active={isWidgetDetailsFetching} />
      ) : (
        services && (
          <Grid sx={{ mx: '2%', px: { xs: 2, md: 4 } }}>
            <Typography variant="h5">{t('selectAppointment')}</Typography>
            {services.length > 1 &&
              services.map((service) => {
                return <Fragment key={`str_${service.id}`}>{renderServicesList(service)}</Fragment>;
              })}
          </Grid>
        )
      )}
    </Box>
  );
}

export { Service };
