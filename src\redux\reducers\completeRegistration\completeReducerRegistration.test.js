import {
  COMPLETE_REGISTRATION_REQUEST,
  COMPLETE_REGISTRATION_SUCCESS,
  COMPLETE_REGISTRATION_ERROR,
  COMPLETE_REGISTRATION_RESET,
} from '../../actions/completeRegistration/index';
import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import { completeRegistrationReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  completeRegistrationFetching: true,
  completeRegistrationSuccess: false,
  completeRegistrationError: false,
  completeRegistrationErrorData: null,
};

const errorState = {
  completeRegistrationFetching: false,
  completeRegistrationSuccess: false,
  completeRegistrationError: true,
  completeRegistrationErrorData: data,
};

const successState = {
  completeRegistrationFetching: false,
  completeRegistrationSuccess: true,
  completeRegistrationError: false,
  completeRegistrationErrorData: null,
  completeRegistrationSuccessData: data,
};

const resetState = {
  completeRegistrationFetching: false,
  completeRegistrationSuccess: false,
  completeRegistrationError: false,
  completeRegistrationErrorData: null,
  completeRegistrationSuccessData: null,
};
describe('Complete Booking Details Reducer', () => {
  it('should return the initial state', () => {
    expect(completeRegistrationReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle COMPLETE_REGISTRATION_REQUEST', () => {
    expect(
      completeRegistrationReducer(initialState, {
        type: COMPLETE_REGISTRATION_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle COMPLETE_REGISTRATION_SUCCESS', () => {
    let result = completeRegistrationReducer(initialState, {
      type: COMPLETE_REGISTRATION_SUCCESS,
      payload: data,
    });
    expect(result).toEqual({ ...initialState, ...successState });
  });

  it('should handle COMPLETE_REGISTRATION_ERROR', () => {
    expect(
      completeRegistrationReducer(initialState, {
        type: COMPLETE_REGISTRATION_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle COMPLETE_REGISTRATION_RESET', () => {
    expect(
      completeRegistrationReducer(initialState, {
        type: COMPLETE_REGISTRATION_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
