/*
 *
 */
import { makeNetworkCall } from '../..';
import { BASE_URL, CAMBIAN_SERVICE_BASE_URL, LOGGED_OUT } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
export function userLogOutApi(action) {
  const { payload = {} } = action || {};
  const { headers = {} } = payload || {};
  const config = {
    method: 'POST',
    url: CAMBIAN_SERVICE_BASE_URL + LOGGED_OUT,
    formData: false,
    headers: headers,
  };
  return makeNetworkCall(config);
}
