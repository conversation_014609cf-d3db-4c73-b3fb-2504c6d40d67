version = 0.1

[default.deploy.parameters]
stack_name = "localwidget-FEStack"
resolve_s3 = true
s3_prefix = "localwidget-FEStack"
region = "ca-central-1"
capabilities = "CAPABILITY_IAM CAPABILITY_NAMED_IAM"
disable_rollback = false
parameter_overrides = [
  "ApplicationName=widget",
  "TestPrefix=''",
  "Env=dev",
  "EcrImageUri=4460016595608.dkr.ecr.ca-central-1.amazonaws.com/organization-images:widget-v1",
  "SSLCertificateArn=\"\"",
  "DNSHostedZoneId=\"\"",
  "DomainName=\"\"",
  "VisibilityEnv=Organization",
  "VPCID=\"/localCS/Organization/CF/VPCID\"",
  "PublicSubnet1=\"/localCS/Organization/CF/PublicSubnet1\"",
  "PublicSubnet2=\"/localCS/Organization/CF/PublicSubnet2\"",
]
image_repository = "460016595608.dkr.ecr.ca-central-1.amazonaws.com/organization-images"

[dev.deploy.parameters]
stack_name = "devwidgetV2Stack"
resolve_s3 = true
s3_prefix = "devwidgetV2Stack"
region = "ca-central-1"
capabilities = "CAPABILITY_IAM CAPABILITY_NAMED_IAM"
disable_rollback = false
parameter_overrides = [
  "ApplicationName=widget-v2",
  "TestPrefix=''",
  "Env=dev",
  "EcrImageUri=460016595608.dkr.ecr.ca-central-1.amazonaws.com/organization-images:widget-v1",
  "ACMCertificateARN=\"\"",
  "VisibilityEnv=Organization",
  "VPCID=\"/devCS/Organization/CF/VPCID\"",
  "PublicSubnet1=\"/devCS/Organization/CF/PublicSubnet1\"",
  "PublicSubnet2=\"/devCS/Organization/CF/PublicSubnet2\"",

  "ExistingALBArn=\"\"",
  "ExistingHTTPSListenerArn=\"\"",
  "ExistingALBSecurityGroupId=\"\"",

  "AppEnvironmentFileS3Arn=\"\"",
]
image_repository = "460016595608.dkr.ecr.ca-central-1.amazonaws.com/organization-images:widget-v1"
