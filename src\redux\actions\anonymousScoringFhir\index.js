/*
 *Scoring action types
 */

export const ANONYMOUS_SCORING_FHIR_REQUEST = 'ANONYMOUS_SCORING_FHIR_REQUEST';
export const ANONYMOUS_SCORING_FHIR_SUCCESS = 'ANONYMOUS_SCORING_FHIR_SUCCESS';
export const ANONYMOUS_SCORING_FHIR_ERROR = 'ANONYMOUS_SCORING_FHIR_ERROR';
export const ANONYMOUS_SCORING_FHIR_RESET = 'ANONYMOUS_SCORING_FHIR_RESET';

/*
 * action creators
 */

export function anonymousScoringFhir(data) {
  return {
    type: ANONYMOUS_SCORING_FHIR_REQUEST,
    payload: data,
  };
}

export function anonymousScoringFhirSuccess(data) {
  return {
    type: ANONYMOUS_SCORING_FHIR_SUCCESS,
    payload: data,
  };
}

export function anonymousScoringFhirError() {
  return {
    type: ANONYMOUS_SCORING_FHIR_ERROR,
  };
}

export const resetAnonymousScoring = () => {
  return {
    type: ANONYMOUS_SCORING_FHIR_RESET,
  };
};
