/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { makeApiFetchScoring } from './api';
import {
  SCORING_QUESTIONNAIRE_ERROR,
  SCORING_QUESTIONNAIRE_REQUEST,
  SCORING_QUESTIONNAIRE_SUCCESS,
} from '../../../redux/actions/scoring';
/**
 *
 * @param {*} action
 */

export function* fetchScoringQuestionnaire(action) {
  try {
    const response = yield call(makeApiFetchScoring, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: SCORING_QUESTIONNAIRE_SUCCESS, payload: data });
    } else {
      yield put({ type: SCORING_QUESTIONNAIRE_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: SCORING_QUESTIONNAIRE_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchScoringQuestionnaire() {
  yield takeEvery(SCORING_QUESTIONNAIRE_REQUEST, fetchScoringQuestionnaire);
}
