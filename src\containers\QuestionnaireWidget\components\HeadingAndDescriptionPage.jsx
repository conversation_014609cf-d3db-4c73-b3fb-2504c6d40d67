/* 
*
*
*
COMPONENT TO BE REMOVED, NOT BEING USED ANYMORE
*
*
*/
import React, { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { Paper, Button, Stack, Grid, Typography, Box } from '@mui/material';
import { actions } from '../../commonConstants';
import { useTranslation } from 'react-i18next';
import { v4 as uuidv4 } from 'uuid';
import { useAPIs } from '../useAPIs';
import { Loader, SignInSignUp, CambianTooltip } from '../../../components';
import {
  downloadPDF,
  getPrintDownloadIconsConfiguration,
  handleCrossOriginPrint,
  removeURLParams,
} from '../../commonUtility';
import { Report } from './Report';
import { useReactToPrint } from 'react-to-print';
import {
  createDemographicUrlParams,
  extractInstrumentScores,
  getMatchedActionCondition,
} from '../questionnaireUtility';

export const HeadingAndDescriptionPage = (props) => {
  const {
    questionnaireWidgetSuccessData,
    handleNavigationCallback,
    demographic,
    questionnaireStatus,
    isActionPerformed,
    setIsActionPerformed,
    questionnaireResponse,
  } = props;
  const { t } = useTranslation();
  const { fetchGenerateHtmlReportAsPdf } = useAPIs();
  const reportRef = useRef(null);
  const reportPageUrl = window.location.href + '/printReport' + createDemographicUrlParams(demographic);

  const [downloadPdfCalled, setDownloadPdfCalled] = useState(false);
  const [matchedActionCondition, setMatchedActionCondition] = useState();

  const { isSavedForLater, isCancelled, isQuestionnaireFinished } = questionnaireStatus || {};

  const questionnaireQuestionsSuccessData = useSelector(
    (state) => state.getQuestionnaireQuestionRedux.questionnaireQuestionsSuccessData,
  );
  const { generateHtmlReportAsPdfFetching, generateHtmlReportAsPdfSuccess, generateHtmlReportAsPdfSuccessData } =
    useSelector((state) => state.generateHtmlReportAsPdfReducer);

  const { questionnaire } = questionnaireQuestionsSuccessData || {};
  const { saveForLaterPage, headingAndDescriptionPage, discardPage, action } = questionnaireWidgetSuccessData || {};

  let heading = t('thankYou');
  let description = t('thankYouDescription2');

  if (isQuestionnaireFinished) {
    heading = headingAndDescriptionPage?.heading || t('thankYou');
    description = headingAndDescriptionPage?.description || t('thankYouDescription2');
  } else if (isSavedForLater) {
    heading = saveForLaterPage?.heading || t('saveLaterHeading');
    description = saveForLaterPage?.description || t('saveLaterDescription');
  } else if (isCancelled) {
    heading = discardPage?.heading || t('discardHeading');
    description = discardPage?.description || t('discardDescription');
  }

  const handleClickNext = () => {
    handleNavigationCallback(actions.NEXT);
  };

  const componentRef = useRef();

  const handleDownload = () => {
    questionnaire.extension.splice(
      questionnaire.extension.findIndex((ext) => ext.url === 'pdftemplate-base64'),
      1,
    );
    const data = {
      questionnaireResponseId: uuidv4(),
      url: removeURLParams(window.location.href) + `/downloadReport`,
      reportResponse: {
        questionnaire,
        questionnaireResponse,
        browserTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        demographic: {
          ...(demographic?.individuals?.[0] || {}),
          ...(demographic?.contact || {}),
        },
      },
    };
    setDownloadPdfCalled(true);

    fetchGenerateHtmlReportAsPdf(data);
  };

  const handlePrint = useReactToPrint({
    content: () => document.querySelector('#QuestionnaireReportComponent') || reportRef.current,
    documentTitle: questionnaire?.title?.trim()?.replace(/\s+/g, '_'),
  });

  const onClickOfPrint = () => {
    const title = questionnaire?.title?.trim()?.replace(/\s+/g, '_');
    try {
      // same-origin frame
      document.title = window.parent.document.title = title; // to check if same origin
      handlePrint();
    } catch (err) {
      handleCrossOriginPrint(reportPageUrl, title);
    }
  };

  useEffect(() => {
    if (generateHtmlReportAsPdfSuccess && generateHtmlReportAsPdfSuccessData && downloadPdfCalled) {
      const filename = questionnaire?.title?.trim().replaceAll(/\s+/g, '_');
      downloadPDF(generateHtmlReportAsPdfSuccessData?.base64Pdf, filename);
      setDownloadPdfCalled(false);
    }
  }, [generateHtmlReportAsPdfSuccess, generateHtmlReportAsPdfSuccessData]);

  useEffect(() => {
    if (questionnaireResponse && questionnaireResponse?.item) {
      const instrumentScores = extractInstrumentScores(questionnaireResponse);
      setMatchedActionCondition(getMatchedActionCondition(action?.metaData?.actionConditions, instrumentScores));
    }
  }, [questionnaireResponse]);

  const renderIcons = () => {
    const iconsAndConfiguration = getPrintDownloadIconsConfiguration();
    const { DownloadIcon, PrintIcon } = iconsAndConfiguration;

    return (
      <>
        {questionnaireResponse && headingAndDescriptionPage?.enabled && (
          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Stack
              direction={iconsAndConfiguration.order === 'print,download' ? 'row-reverse' : 'row'}
              spacing={3}
              sx={{ pt: 1, pr: 1 }}
            >
              {headingAndDescriptionPage?.showDownloadIcon && (
                <CambianTooltip title={iconsAndConfiguration.downloadTooltip}>
                  <DownloadIcon color="primary" sx={{ cursor: 'pointer' }} onClick={handleDownload} />
                </CambianTooltip>
              )}
              {headingAndDescriptionPage?.showPrintIcon !== false && (
                <CambianTooltip title={iconsAndConfiguration.printTooltip}>
                  <PrintIcon color="primary" sx={{ cursor: 'pointer' }} onClick={onClickOfPrint} />
                </CambianTooltip>
              )}
            </Stack>
          </Box>
        )}
      </>
    );
  };

  return (
    <>
      <Loader active={generateHtmlReportAsPdfFetching} />
      <Box sx={{ px: '4%', pt: '8px', pb: '48px' }}>
        <Grid container>
          <Grid
            item
            xs={12}
            justifyContent="center"
            alignItems="center"
            sx={{ minHeight: '200px', '@page': { margin: '1in !important' } }}
            ref={componentRef}
          >
            <Stack direction="row" justifyContent="space-between" sx={{ mt: 4 }}>
              <Typography variant="h5">{heading}</Typography>
              {renderIcons()}
            </Stack>
            <Grid
              sx={{
                minHeight: '300px',
                mt: 2,
                pb: 3,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Stack sx={{ width: '100%' }}>
                <Typography variant="body" dangerouslySetInnerHTML={{ __html: description }} />
              </Stack>
            </Grid>
          </Grid>
          <Grid item xs={12} sx={{ mt: 3 }}>
            <Stack direction="row" justifyContent="center" alignItems="center" spacing={2} sx={{ pt: 0, px: 2, pb: 2 }}>
              {questionnaireResponse && action?.enabled && matchedActionCondition && !isActionPerformed && (
                <Button variant="contained" onClick={handleClickNext}>
                  {t('next')}
                </Button>
              )}
            </Stack>
          </Grid>
        </Grid>
        <Box sx={{ visibility: 'hidden', width: '100%', height: 0, maxHeight: 0, overflow: 'hidden' }}>
          <Box ref={reportRef}>
            <Report
              questionnaireResponse={questionnaireResponse}
              demographic={demographic}
              reportPageUrl={reportPageUrl}
              printRequest={true}
              isActionPerformed={isActionPerformed}
              setIsActionPerformed={setIsActionPerformed}
            />
          </Box>
        </Box>
      </Box>
    </>
  );
};
