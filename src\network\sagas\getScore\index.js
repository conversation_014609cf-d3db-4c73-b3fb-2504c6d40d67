/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import { GET_SCORE_ERROR, GET_SCORE_REQUEST, GET_SCORE_SUCCESS } from '../../../redux/actions/getScore';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiGetScore } from './api';

/**
 *
 * @param {*} action
 */

export function* getScore(action) {
  try {
    const response = yield call(makeApiGetScore, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: GET_SCORE_SUCCESS, payload: data });
    } else {
      yield put({ type: GET_SCORE_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: GET_SCORE_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchGetScore() {
  yield takeEvery(GET_SCORE_REQUEST, getScore);
}
