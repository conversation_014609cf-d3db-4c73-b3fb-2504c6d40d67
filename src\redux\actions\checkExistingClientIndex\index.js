export const CHECK_EXISTING_CLIENT_INDEX_REQUEST = 'CHECK_EXISTING_CLIENT_INDEX_REQUEST';
export const CHECK_EXISTING_CLIENT_INDEX_SUCCESS = 'CHECK_EXISTING_CLIENT_INDEX_SUCCESS';
export const CHECK_EXISTING_CLIENT_INDEX_ERROR = 'CHECK_EXISTING_CLIENT_INDEX_ERROR';
export const CHECK_EXISTING_CLIENT_INDEX_RESET = 'CHECK_EXISTING_CLIENT_INDEX_RESET';

export function checkExistingClientIndex(data) {
  return {
    type: CHECK_EXISTING_CLIENT_INDEX_REQUEST,
    payload: data,
  };
}

export function checkExistingClientIndexSuccess(data) {
  return {
    type: CHECK_EXISTING_CLIENT_INDEX_SUCCESS,
    payload: data,
  };
}

export function checkExistingClientIndexError(data) {
  return {
    type: CHECK_EXISTING_CLIENT_INDEX_ERROR,
    payload: data,
  };
}

export const resetCheckExistingClientIndex = () => {
  return {
    type: CHECK_EXISTING_CLIENT_INDEX_RESET,
  };
};
