/*
 *
 */
import { makeNetworkCall } from '../..';
import { BASE_URL, GET_QUESTIONNAIRE_RESPONSE_FHIR } from '../../../utils/constants/apiEndpoints';
import { ORGANIZATION_ID, QUESTIONNAIRE_RESPONSE_ID } from '../../../utils/constants';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiFetchQuestionnaireResponseFhir(action) {
  const { payload = {} } = action || {};
  const { headers = {}, questionnaireResponseId = null, organizationId } = payload || {};
  const mainUrl =
    BASE_URL +
    GET_QUESTIONNAIRE_RESPONSE_FHIR.replace(QUESTIONNAIRE_RESPONSE_ID, questionnaireResponseId).replace(
      ORGANIZATION_ID,
      organizationId,
    );

  const config = {
    method: 'GET',
    url: mainUrl,
    headers: headers,
  };

  return makeNetworkCall(config);
}
