/*
 *
 */
import { makeNetworkCall } from '../..';
import { PATIENT_ID } from '../../../utils/constants';
import { CHANGE_STATUS_URL, BASE_URL, CAMBIAN_SERVICE_BASE_URL } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiChangeStatus(action) {
  const { payload = {} } = action || {};
  const { data = {} } = payload || {};
  const { headers = {}, patientId } = data || {};
  const mainUrl = CAMBIAN_SERVICE_BASE_URL + CHANGE_STATUS_URL.replace(PATIENT_ID, patientId);
  const config = {
    method: 'PUT',
    url: mainUrl,
    headers: headers,
    data: {},
  };
  return makeNetworkCall(config);
}
