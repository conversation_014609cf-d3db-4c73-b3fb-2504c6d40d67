import { useSnackbar } from 'notistack';
import IconButton from '@mui/material/IconButton';
import React, { Fragment, useEffect, useState } from 'react';
import { Clear } from '@mui/icons-material';
import { Typography } from '@mui/material';

const useNotification = () => {
  const [conf, setConf] = useState({});
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();

  const action = (key) => (
    <Fragment>
      <IconButton
        onClick={() => {
          closeSnackbar(key);
        }}
      >
        <Clear fontSize="small" sx={{ color: '#fff' }} />
      </IconButton>
    </Fragment>
  );

  useEffect(() => {
    if (conf?.msg) {
      let variant = 'info';
      if (conf.variant) {
        variant = conf.variant;
      }
      enqueueSnackbar(
        <Typography variant="body2" sx={{ pl: 1 }}>
          {conf.msg}
        </Typography>,
        {
          variant: variant,
          autoHideDuration: 3000,
          action,
        },
      );
    }
  }, [conf]);
  return [conf, setConf];
};

export default useNotification;
