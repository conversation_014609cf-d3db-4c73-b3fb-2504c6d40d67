/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import { GET_PDF_REQUEST, GET_PDF_SUCCESS, GET_PDF_ERROR } from '../../../redux/actions/getPdfReport';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiGetPdf } from './api';

/**
 *
 * @param {*} action
 */

export function* getPdf(action) {
  try {
    const response = yield call(makeApiGetPdf, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: GET_PDF_SUCCESS, payload: data });
    } else {
      yield put({ type: GET_PDF_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: GET_PDF_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchGetPdf() {
  yield takeEvery(GET_PDF_REQUEST, getPdf);
}
