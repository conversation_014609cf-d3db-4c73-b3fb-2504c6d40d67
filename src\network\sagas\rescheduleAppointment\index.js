/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';

import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  RESCHEDULE_APPOINTMENT_ERROR,
  RESCHEDULE_APPOINTMENT_SUCCESS,
  RESCHEDULE_APPOINTMENT_REQUEST,
} from '../../../redux/actions/rescheduleAppointment';

import { rescheduleAppointmentApi } from './api';

/**
 *
 * @param {*} action
 */
export function* rescheduleAppointment(action) {
  try {
    const response = yield call(rescheduleAppointmentApi, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: RESCHEDULE_APPOINTMENT_SUCCESS, payload: data });
    } else {
      yield put({ type: RESCHEDULE_APPOINTMENT_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: RESCHEDULE_APPOINTMENT_ERROR });
  }
}

// Our watcher Saga:
export function* watchRescheduleAppointment() {
  yield takeEvery(RESCHEDULE_APPOINTMENT_REQUEST, rescheduleAppointment);
}
