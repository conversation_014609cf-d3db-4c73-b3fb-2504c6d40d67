/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import {
  SEND_CONNECTION_INVITE_REQUEST,
  SEND_CONNECTION_INVITE_SUCCESS,
  SEND_CONNECTION_INVITE_ERROR,
  SEND_CONNECTION_INVITE_RESET,
} from '../../actions/sendConnectionInvite';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const sendConnectionInviteReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case SEND_CONNECTION_INVITE_REQUEST:
      return {
        ...state,
        sendConnectionInviteFetching: true,
        sendConnectionInviteSuccess: false,
        sendConnectionInviteError: false,
        sendConnectionInviteErrorData: null,
      };
    case SEND_CONNECTION_INVITE_SUCCESS: {
      return {
        ...state,
        sendConnectionInviteFetching: false,
        sendConnectionInviteSuccess: true,
        sendConnectionInviteError: false,
        sendConnectionInviteErrorData: null,
        sendConnectionInviteSuccessData: payload,
      };
    }
    case SEND_CONNECTION_INVITE_ERROR:
      return {
        ...state,
        sendConnectionInviteFetching: false,
        sendConnectionInviteSuccess: false,
        sendConnectionInviteError: true,
        sendConnectionInviteErrorData: payload,
      };
    case SEND_CONNECTION_INVITE_RESET:
      return {
        ...state,
        sendConnectionInviteFetching: false,
        sendConnectionInviteSuccess: false,
        sendConnectionInviteError: false,
        sendConnectionInviteErrorData: null,
      };
    default:
      return state;
  }
};
