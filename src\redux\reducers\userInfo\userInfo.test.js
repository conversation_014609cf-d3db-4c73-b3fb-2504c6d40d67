import { USER_INFO_RESET, USER_INFO_ERROR, USER_INFO_SUCCESS, USER_INFO_REQUEST } from '../../actions/userInfo';
import * as GLOBALS from '../globals';
import { userInfoReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isUserInfoFetching: true,
  fetchUserInfoSuccess: false,
  fetchUserInfoError: false,
  userInfoErrorData: null,
};

const successState = {
  isUserInfoFetching: false,
  fetchUserInfoSuccess: true,
  fetchUserInfoError: false,
  userInfoErrorData: null,
  userInfoSuccessData: data,
};

const errorState = {
  isUserInfoFetching: false,
  fetchUserInfoSuccess: false,
  fetchUserInfoError: true,
  userInfoErrorData: data,
};

const resetState = {
  isUserInfoFetching: false,
  fetchUserInfoSuccess: false,
  fetchUserInfoError: false,
  userInfoSuccessData: null,
};

describe('Complete User Info Reducer', () => {
  it('should return the initial state', () => {
    expect(userInfoReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle USER_INFO_REQUEST', () => {
    expect(
      userInfoReducer(initialState, {
        type: USER_INFO_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle USER_INFO_SUCCESS', () => {
    expect(
      userInfoReducer(initialState, {
        type: USER_INFO_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle USER_INFO_ERROR', () => {
    expect(
      userInfoReducer(initialState, {
        type: USER_INFO_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle USER_INFO_RESET', () => {
    expect(
      userInfoReducer(initialState, {
        type: USER_INFO_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
