/*
 *
 */

import { makeNetworkCall } from '../..';
import { ORGANIZATION_ID } from '../../../utils/constants';
import { BASE_URL, SCORING } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiFetchScoring(action) {
  const { payload = {} } = action || {};
  const { headers = {}, questionnaireData, organizationId } = payload || {};
  const mainUrl = BASE_URL + SCORING.replace(ORGANIZATION_ID, organizationId);

  const config = {
    method: 'POST',
    url: mainUrl,
    headers: headers,
    data: questionnaireData,
  };

  return makeNetworkCall(config);
}
