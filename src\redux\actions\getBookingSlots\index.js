/*
 * Get booking slots action types
 */

export const GET_BOOKING_SLOTS_REQUEST = 'GET_BOOKING_SLOTS_REQUEST';
export const GET_BOOKING_SLOTS_SUCCESS = 'GET_BOOKING_SLOTS_SUCCESS';
export const GET_BOOKING_SLOTS_ERROR = 'GET_BOOKING_SLOTS_ERROR';
export const GET_BOOKING_SLOTS_RESET = 'GET_BOOKING_SLOTS_RESET';

/*
 * action creators
 */

export function fetchBookingSlots(data) {
  return {
    type: GET_BOOKING_SLOTS_REQUEST,
    payload: data,
  };
}

export function fetchBookingSlotsSuccess(data) {
  return {
    type: GET_BOOKING_SLOTS_SUCCESS,
    payload: data,
  };
}

export function fetchBookingSlotsError() {
  return {
    type: GET_BOOKING_SLOTS_ERROR,
  };
}

export function fetchBookingSlotsReset() {
  return {
    type: GET_BOOKING_SLOTS_RESET,
  };
}
