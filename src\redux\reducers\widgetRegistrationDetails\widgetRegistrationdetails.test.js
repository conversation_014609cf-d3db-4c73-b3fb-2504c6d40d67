import {
  WIDGET_REGISTRATION_DETAILS_RESET,
  WIDGET_REGISTRATION_DETAILS_ERROR,
  WIDGET_REGISTRATION_DETAILS_SUCCESS,
  WIDGET_REGISTRATION_DETAILS_REQUEST,
} from '../../actions/widgetRegistrationDetails/index';
import * as GLOBALS from '../globals';
import { widgetRegistrationDetailsReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isWidgetRegistrationDetailsFetching: true,
  fetchWidgetRegistrationDetailsSuccess: false,
  fetchWidgetRegistrationDetailsError: false,
  widgetRegistrationDetailsErrorData: null,
};

const successState = {
  isWidgetRegistrationDetailsFetching: false,
  fetchWidgetRegistrationDetailsSuccess: true,
  fetchWidgetRegistrationDetailsError: false,
  widgetRegistrationDetailsErrorData: null,
  widgetRegistrationDetailsSuccessData: data,
};

const errorState = {
  isWidgetRegistrationDetailsFetching: false,
  fetchWidgetRegistrationDetailsSuccess: false,
  fetchWidgetRegistrationDetailsError: true,
  widgetRegistrationDetailsErrorData: data,
};

const resetState = {
  isWidgetRegistrationDetailsFetching: false,
  fetchWidgetRegistrationDetailsSuccess: false,
  fetchWidgetRegistrationDetailsError: false,
  widgetRegistrationDetailsErrorData: null,
};

describe('Widget details registration Details Reducer', () => {
  it('should return the initial state', () => {
    expect(widgetRegistrationDetailsReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle WIDGET_REGISTRATION_DETAILS_REQUEST', () => {
    expect(
      widgetRegistrationDetailsReducer(initialState, {
        type: WIDGET_REGISTRATION_DETAILS_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle WIDGET_REGISTRATION_DETAILS_SUCCESS', () => {
    expect(
      widgetRegistrationDetailsReducer(initialState, {
        type: WIDGET_REGISTRATION_DETAILS_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle WIDGET_REGISTRATION_DETAILS_ERROR', () => {
    expect(
      widgetRegistrationDetailsReducer(initialState, {
        type: WIDGET_REGISTRATION_DETAILS_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle WIDGET_REGISTRATION_DETAILS_RESET', () => {
    expect(
      widgetRegistrationDetailsReducer(initialState, {
        type: WIDGET_REGISTRATION_DETAILS_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
