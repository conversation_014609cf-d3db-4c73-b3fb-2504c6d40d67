/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import {
  SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_REQUEST,
  SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_SUCCESS,
  SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_ERROR,
} from '../../../redux/actions/saveQuestionnaireResponseForLater';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiSaveQuestionnaireResponseForLater } from './api';

/**
 *
 * @param {*} action
 */

export function* saveQuestionnaireResponseForLater(action) {
  try {
    const response = yield call(makeApiSaveQuestionnaireResponseForLater, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_SUCCESS, payload: data });
    } else {
      yield put({ type: SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchSaveQuestionnaireResponseForLater() {
  yield takeEvery(SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_REQUEST, saveQuestionnaireResponseForLater);
}
