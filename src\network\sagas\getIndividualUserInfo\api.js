/*
 *
 */
import { makeNetworkCall } from '../..';
import { GET_INDIVIDUAL_USER_INFO, BASE_URL, CAMBIAN_SERVICE_BASE_URL } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiFetchIndividualUserInfo(action) {
  const { payload = {} } = action || {};
  const { headers = {}, username } = payload || {};

  const config = {
    method: 'GET',
    url: BASE_URL + GET_INDIVIDUAL_USER_INFO.replace('EMAIL_ID', username),
    headers: headers,
    formData: false,
  };
  return makeNetworkCall(config);
}
