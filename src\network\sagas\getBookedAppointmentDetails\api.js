import { makeNetworkCall } from '../..';
import { APPOINTMENT_ID, LOCATION_ID, ORGANIZATION_ID, REGISTRY } from '../../../utils/constants';
import { GET_APPOINTMENT_DETAIL } from '@/utils/constants/awsApiEndpoints';

export function getBookedAppointmentApi(action) {
  const { payload = {} } = action || {};
  const { headers = {}, appointmentId, organizationId, locationId } = payload || {};

  const config = {
    method: 'GET',
    url: GET_APPOINTMENT_DETAIL.replace(ORGANIZATION_ID, organizationId)
      //.replace(LOCATION_ID, locationId)
      .replace(APPOINTMENT_ID, appointmentId),
    headers: headers,
    formData: false,
  };

  return makeNetworkCall(config);
}
