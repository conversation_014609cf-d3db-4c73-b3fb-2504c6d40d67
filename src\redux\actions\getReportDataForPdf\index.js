/*
 * POST Questions action types
 */

export const GET_REPORT_DATA_FOR_PDF_REQUEST = 'GET_REPORT_DATA_FOR_PDF_REQUEST';
export const GET_REPORT_DATA_FOR_PDF_SUCCESS = 'GET_REPORT_DATA_FOR_PDF_SUCCESS';
export const GET_REPORT_DATA_FOR_PDF_ERROR = 'GET_REPORT_DATA_FOR_PDF_ERROR';
export const GET_REPORT_DATA_FOR_PDF_RESET = 'GET_REPORT_DATA_FOR_PDF_RESET';
/*
 * action creators
 */

export function getReportDataForPdf(data) {
  return {
    type: GET_REPORT_DATA_FOR_PDF_REQUEST,
    payload: data,
  };
}

export function getReportDataForPdfSuccess(data) {
  return {
    type: GET_REPORT_DATA_FOR_PDF_SUCCESS,
    payload: data,
  };
}

export function getReportDataForPdfError() {
  return {
    type: GET_REPORT_DATA_FOR_PDF_ERROR,
  };
}

export function resetGetReportDataForPdf() {
  return {
    type: GET_REPORT_DATA_FOR_PDF_RESET,
  };
}
