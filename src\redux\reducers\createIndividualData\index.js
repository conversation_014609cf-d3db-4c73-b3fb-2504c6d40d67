import {
  CREATE_INDIVIDUAL_DATA_REQUEST,
  CREATE_INDIVIDUAL_DATA_SUCCESS,
  CREATE_INDIVIDUAL_DATA_ERROR,
  CREATE_INDIVIDUAL_DATA_RESET,
} from '@/redux/actions/createIndividualData';
import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

export const initialState = {
  ...GLOBALS,
  isCreateIndividualDataFetching: false,
  createIndividualDataSuccess: false,
  createIndividualDataError: null,
  createIndividualDataData: null,
};

export const createIndividualDataReducer = (state = initialState, action) => {
  const { type, payload } = action;

  switch (type) {
    case CREATE_INDIVIDUAL_DATA_REQUEST:
      return {
        ...state,
        isCreateIndividualDataFetching: true,
        createIndividualDataSuccess: false,
        createIndividualDataError: null,
      };

    case CREATE_INDIVIDUAL_DATA_SUCCESS:
      return {
        ...state,
        isCreateIndividualDataFetching: false,
        createIndividualDataSuccess: true,
        createIndividualDataData: payload,
        createIndividualDataError: null,
      };

    case CREATE_INDIVIDUAL_DATA_ERROR:
      return {
        ...state,
        isCreateIndividualDataFetching: false,
        createIndividualDataSuccess: false,
        createIndividualDataData: null,
        createIndividualDataError: payload,
      };

    case CREATE_INDIVIDUAL_DATA_RESET:
      return initialState;

    default:
      return state;
  }
};
