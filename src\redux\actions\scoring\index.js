/*
 *Scoring action types
 */

export const SCORING_QUESTIONNAIRE_REQUEST = 'SCORING_QUESTIONNAIRE_REQUEST';
export const SCORING_QUESTIONNAIRE_SUCCESS = 'SCORING_QUESTIONNAIRE_SUCCESS';
export const SCORING_QUESTIONNAIRE_ERROR = 'SCORING_QUESTIONNAIRE_ERROR';
export const SCORING_QUESTIONNAIRE_RESET = 'SCORING_QUESTIONNAIRE_RESET';

/*
 * action creators
 */

export function scoringQuestionnaire(data) {
  return {
    type: SCORING_QUESTIONNAIRE_REQUEST,
    payload: data,
  };
}

export function scoringQuestionnaireSuccess(data) {
  return {
    type: SCORING_QUESTIONNAIRE_SUCCESS,
    payload: data,
  };
}

export function scoringQuestionnaireError() {
  return {
    type: SCORING_QUESTIONNAIRE_ERROR,
  };
}

export const resetScoring = () => {
  return {
    type: SCORING_QUESTIONNAIRE_RESET,
  };
};
