import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  CHECK_EMAIL_REQUEST,
  CHECK_EMAIL_SUCCESS,
  CHECK_EMAIL_ERROR,
  CHECK_EMAIL_RESET,
} from '../../actions/checkEmailInBooking';
export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};
export const checkEmailReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case CHECK_EMAIL_REQUEST:
      return {
        ...state,
        isCheckEmailFetching: true,
        checkEmailSuccess: false,
        checkEmailError: false,
        checkEmailErrorData: null,
      };
    case CHECK_EMAIL_SUCCESS: {
      return {
        ...state,
        isCheckEmailFetching: false,
        checkEmailSuccess: true,
        checkEmailError: false,
        checkEmailErrorData: null,
        checkEmailSuccessData: payload,
      };
    }
    case CHECK_EMAIL_ERROR:
      return {
        ...state,
        isCheckEmailFetching: false,
        checkEmailSuccess: false,
        checkEmailError: true,
        checkEmailErrorData: payload,
      };
    case CHECK_EMAIL_RESET:
      return {
        ...state,
        isCheckEmailFetching: false,
        checkEmailSuccess: false,
        checkEmailError: false,
        checkEmailErrorData: null,
      };
    default:
      return state;
  }
};
