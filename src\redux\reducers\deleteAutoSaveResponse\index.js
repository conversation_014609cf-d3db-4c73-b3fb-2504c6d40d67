import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  DELETE_AUTO_SAVE_RESPONSE_ERROR,
  DELETE_AUTO_SAVE_RESPONSE_SUCCESS,
  DELETE_AUTO_SAVE_RESPONSE_REQUEST,
} from '../../actions/deleteAutoSaveResponse';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const deleteAutoSaveResponseReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case DELETE_AUTO_SAVE_RESPONSE_REQUEST:
      return {
        ...state,
        isDeleteAutoSaveResponseFetching: true,
        isDeleteAutoSaveResponseSuccess: false,
        isDeleteAutoSaveResponseError: false,
        deleteAutoSaveResponseErrorData: null,
      };
    case DELETE_AUTO_SAVE_RESPONSE_SUCCESS: {
      return {
        ...state,
        isDeleteAutoSaveResponseFetching: false,
        isDeleteAutoSaveResponseSuccess: true,
        isDeleteAutoSaveResponseError: false,
        deleteAutoSaveResponseErrorData: null,
        deleteAutoSaveResponseSuccessData: payload,
      };
    }
    case DELETE_AUTO_SAVE_RESPONSE_ERROR:
      return {
        ...state,
        isDeleteAutoSaveResponseFetching: false,
        isDeleteAutoSaveResponseSuccess: false,
        isDeleteAutoSaveResponseError: true,
        deleteAutoSaveResponseErrorData: payload,
      };
    default:
      return state;
  }
};
