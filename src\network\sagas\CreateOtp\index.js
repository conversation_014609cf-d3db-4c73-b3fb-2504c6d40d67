/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import { CREATE_OTP_ERROR, CREATE_OTP_REQUEST, CREATE_OTP_SUCCESS } from '../../../redux/actions/CreateOtp';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiCreateOtp } from './api';

/**
 *
 * @param {*} action
 */

export function* createOtp(action) {
  try {
    const response = yield call(makeApiCreateOtp, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: CREATE_OTP_SUCCESS, payload: data });
    } else {
      yield put({ type: CREATE_OTP_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: CREATE_OTP_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchCreateOtp() {
  yield takeEvery(CREATE_OTP_REQUEST, createOtp);
}
