/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';

import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  CONFIRM_APPOINTMENT_ERROR,
  CONFIRM_APPOINTMENT_SUCCESS,
  CONFIRM_APPOINTMENT_REQUEST,
} from '../../../redux/actions/confirmAppointment';

import { confirmAppointmentApi } from './api';

/**
 *
 * @param {*} action
 */
export function* confirmAppointment(action) {
  try {
    const response = yield call(confirmAppointmentApi, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: CONFIRM_APPOINTMENT_SUCCESS, payload: data });
    } else {
      yield put({ type: CONFIRM_APPOINTMENT_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: CONFIRM_APPOINTMENT_ERROR });
  }
}

// Our watcher Saga:
export function* watchConfirmAppointment() {
  yield takeEvery(CONFIRM_APPOINTMENT_REQUEST, confirmAppointment);
}
