import { runSaga } from 'redux-saga';
import * as api from './api';
import { addNewPatient } from './index';
import * as actions from '../../../redux/actions/addNewClient/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

/**
 * This function test test case for get booking slots details saga
 * Fires get booking slots success of api gives success
 * Fires get booking slots error of api fails
 */

describe('Add new client Patient', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiFetchAddNewPatient = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.addNewClientRequest(DUMMY_ITEM.data);
    await runSaga(fakeStore, addNewPatient, requestResult).done;

    let successResult = actions.addNewClientSuccess(DUMMY_ITEM.data);

    expect(api.makeApiFetchAddNewPatient.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchAddNewPatient = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.addNewClientRequest(DUMMY_ITEM.data);
    await runSaga(fakeStore, addNewPatient, requestResult).done;

    expect(api.makeApiFetchAddNewPatient.mock.calls.length).toBe(1);

    let errorResult = actions.addNewClientError(DUMMY_ITEM.data);
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch reset action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchAddNewPatient = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.addNewClientRequest(DUMMY_ITEM.data);
    await runSaga(fakeStore, addNewPatient, requestResult).done;

    expect(api.makeApiFetchAddNewPatient.mock.calls.length).toBe(1);

    let resetResult = actions.addNewClientReset(DUMMY_ITEM.data);
    const expectedAction = {
      type: actions.ADD_NEW_CLIENT_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
