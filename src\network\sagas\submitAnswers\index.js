/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  POST_REGISTRATION_QUESTIONS_ERROR,
  POST_REGISTRATION_QUESTIONS_SUCCESS,
  POST_REGISTRATION_QUESTIONS_REQUEST,
} from '../../../redux/actions/submitAnswers';

import { makeApiFetchSubmitAnswers } from './api';
/**
 *
 * @param {*} action
 */

export function* fetchSubmitAnswers(action) {
  try {
    const response = yield call(makeApiFetchSubmitAnswers, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: POST_REGISTRATION_QUESTIONS_SUCCESS, payload: data });
    } else {
      yield put({ type: POST_REGISTRATION_QUESTIONS_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: POST_REGISTRATION_QUESTIONS_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchSubmitAnswers() {
  yield takeEvery(POST_REGISTRATION_QUESTIONS_REQUEST, fetchSubmitAnswers);
}
