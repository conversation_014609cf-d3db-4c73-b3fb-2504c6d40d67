/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import { GET_LOCATION_ERROR, GET_LOCATION_SUCCESS, GET_LOCATION_REQUEST } from '../../actions/getLocations';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const getLocationsReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case GET_LOCATION_REQUEST:
      return {
        ...state,
        isLocationsFetching: true,
        isLocationsSuccess: false,
        isLocationsError: false,
        locationsErrorData: null,
      };
    case GET_LOCATION_SUCCESS:
      return {
        ...state,
        isLocationsFetching: false,
        isLocationsSuccess: true,
        isLocationsError: false,
        locationsErrorData: null,
        locationsSuccessData: payload,
      };
    case GET_LOCATION_ERROR:
      return {
        ...state,
        isLocationsFetching: false,
        isLocationsSuccess: false,
        isLocationsError: true,
        locationsErrorData: payload,
      };
    default:
      return state;
  }
};
