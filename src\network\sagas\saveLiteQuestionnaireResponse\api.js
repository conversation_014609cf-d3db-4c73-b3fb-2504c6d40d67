/*
 *
 */

import { makeNetworkCall } from '../..';
import { BASE_URL, SAVE_LITE_QUESTIONNAIRE_RESPONSE_URL } from '../../../utils/constants/apiEndpoints';

import { ORGANIZATION_ID } from '../../../utils/constants';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiFetchSaveLiteQuestionnaireResponse(action) {
  const { payload = {} } = action || {};
  const { headers = {}, data, organizationId } = payload || {};

  const mainUrl = BASE_URL + SAVE_LITE_QUESTIONNAIRE_RESPONSE_URL.replace(ORGANIZATION_ID, organizationId);
  const config = {
    method: 'POST',
    url: mainUrl,
    headers: headers,
    data,
  };
  return makeNetworkCall(config);
}
