/**
 *
 * @param {*} url
 */
const getUrlParameters = (url) => {
  const parameters = {};
  url.replace(/[?&]+([^=&]+)=([^&]*)/gi, (m, key, value) => {
    parameters[key] = value;
  });
  return parameters;
};

/**
 *
 * @param {*} text
 */
const isValidCharacter = (text) => {
  const regExp = /^[\u0600-\u065F\u066A-\u06EF\u06FA-\u06FFa-zA-Z ]*$/;
  return regExp.test(text);
};

/**
 *
 * @param {*} text
 */
const isValidEnglishNumber = (text) => {
  const regExp = /^[0-9\u0660-\u0669]+$/;
  return regExp.test(text);
};

const isValidPhoneNumber = (text) => {
  const regExp = /^(?=.*?[0-9])[0-9- .]+$/;
  return regExp.test(text);
};

const isLastCharDigit = (text) => {
  const regExp = /[0-9]$/;
  return regExp.test(text);
};

const isCharNumber = (c) => {
  return c >= '0' && c <= '9';
};

/**
 *
 * @param {*} email
 */
const isValidEmail = (email) => {
  const regExp =
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return regExp.test(email);
};

const isValidUserName = (userName) => {
  const regExp = /^[A-Za-z ]+$/;
  return regExp.test(userName);
};

const getRedirectURL = (url) => {
  var pathname = new URL(url).pathname;
  let urlIndex = pathname.split('/');
  let redirect_url = `${window.location.origin}/${urlIndex[1]}/${urlIndex[2]}/${urlIndex[3]}`;
  return redirect_url;
};

const createURL = (url, CAMBIAN_LOGIN_URL, redirect_url) => {
  var pathname = new URL(url).pathname;
  let urlIndex = pathname.split('/');
  let redirect_uri = `/${urlIndex[1]}/${urlIndex[2]}/${urlIndex[3]}`;
  let returnUrl =
    CAMBIAN_LOGIN_URL +
    `?response_type=code&client_id=widget&redirect_uri=${redirect_url}${redirect_uri}&resource=cambianrsid`;
  return returnUrl;
};

const emptyObject = () => {
  return {};
};

const emptyArray = () => {
  return [];
};

const getProgressValue = (value, totalValue) => {
  let valueOfProgress = parseInt((parseInt(value) * 100) / totalValue);
  return valueOfProgress;
};
/**
 *
 */
export {
  getUrlParameters,
  isValidCharacter,
  isValidEnglishNumber,
  isValidPhoneNumber,
  isCharNumber,
  isValidEmail,
  isLastCharDigit,
  isValidUserName,
  createURL,
  getRedirectURL,
  emptyObject,
  emptyArray,
  getProgressValue,
};
