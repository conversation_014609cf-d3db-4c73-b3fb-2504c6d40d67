import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  PIN_GENERATE_REQUEST,
  PIN_GENERATE_SUCCESS,
  PIN_GENERATE_ERROR,
  PIN_GENERATE_RESET,
} from '../../actions/pinGenerate';
export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};
export const pinGenerateReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case PIN_GENERATE_REQUEST:
      return {
        ...state,
        pinGenerateFetching: true,
        pinGenerateSuccess: false,
        pinGenerateError: false,
        pinGenerateErrorData: null,
      };
    case PIN_GENERATE_SUCCESS: {
      return {
        ...state,
        pinGenerateFetching: false,
        pinGenerateSuccess: true,
        pinGenerateError: false,
        pinGenerateErrorData: null,
        pinGenerateSuccessData: payload,
      };
    }
    case PIN_GENERATE_ERROR:
      return {
        ...state,
        pinGenerateFetching: false,
        pinGenerateSuccess: false,
        pinGenerateError: true,
        pinGenerateErrorData: payload,
      };
    case PIN_GENERATE_RESET:
      return {
        ...state,
        pinGenerateFetching: false,
        pinGenerateSuccess: false,
        pinGenerateError: false,
        pinGenerateErrorData: null,
        pinGenerateSuccessData: null,
      };
    default:
      return state;
  }
};
