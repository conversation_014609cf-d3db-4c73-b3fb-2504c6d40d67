/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import {
  START_QUESTIONNAIRE_REQUEST,
  START_QUESTIONNAIRE_SUCCESS,
  START_QUESTIONNAIRE_ERROR,
} from '../../../redux/actions/startQuestionnaire';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiStartQuestionnaire } from './api';

/**
 *
 * @param {*} action
 */

export function* startQuestionnaire(action) {
  try {
    const response = yield call(makeApiStartQuestionnaire, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: START_QUESTIONNAIRE_SUCCESS, payload: data });
    } else {
      yield put({ type: START_QUESTIONNAIRE_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: START_QUESTIONNAIRE_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchStartQuestionnaire() {
  yield takeEvery(START_QUESTIONNAIRE_REQUEST, startQuestionnaire);
}
