import { useState, useEffect } from 'react';
import { Typo<PERSON>, Grid, Button, Stack } from '@mui/material';
import { sortSlots } from '../../bookingUtility';
import { TIME_FORMAT } from '@/utils/constants/index';
import { convertTimeIntoTwelveHourFormat, formatDateName } from '@/utils/helpers/date-time';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import moment from 'moment-timezone';
import { extractExtension } from '@/containers/commonUtility';
import { CambianSelectionButton } from '@/components';
import { useTranslation } from 'react-i18next';

export const Slots = (props) => {
  console.log('TRACE: Slots');
  const { slotsData, isBookingSlotsFetching, selectedTime, handleSlotSelection, date, selectedLocation } = props;
  const { t } = useTranslation();

  const { entry = [] } = slotsData || [];
  const [startIndex, setStartIndex] = useState(0);
  const [slotLimit, setSlotLimit] = useState(15);

  const handleSlotSelectionCallback = (data) => {
    const slotStartTime = convertTimeIntoTwelveHourFormat(data?.resource?.start, TIME_FORMAT);
    const slotEndTime = convertTimeIntoTwelveHourFormat(data?.resource?.end, TIME_FORMAT);
    const slotDetails = { data, slotStartTime, slotEndTime };

    handleSlotSelection(slotDetails);
  };

  useEffect(() => {
    setStartIndex(0);
    setSlotLimit(15);
  }, [isBookingSlotsFetching]);

  const getTimeZone = () => {
    const { resource } = selectedLocation || {};
    const { valueString } = extractExtension(resource?.extension, 'http://cambian.com/Location/location-timezone');

    return valueString;
  };

  const previousSlots = () => {
    setSlotLimit(startIndex);
    setStartIndex(startIndex - 15);
  };

  const nextSlots = () => {
    setStartIndex(slotLimit);
    setSlotLimit(slotLimit + 15);
  };

  return (
    <Grid container sx={{ px: { xs: 1 } }}>
      <Grid item xs={12}>
        <Typography variant="body1">{formatDateName(date)}</Typography>
        {getTimeZone() && (
          <Typography variant="caption">
            {getTimeZone()} ({moment().tz(getTimeZone()).format(t('gmtFormat'))})
          </Typography>
        )}
      </Grid>

      {isBookingSlotsFetching === false ? (
        <>
          {entry?.length !== 0 ? (
            <Grid item xs={12}>
              <Grid container sx={{ pt: 2 }}>
                {entry?.sort(sortSlots).map(
                  (slot, index) =>
                    index >= startIndex &&
                    index < slotLimit && (
                      <Grid item xs={4} key={index}>
                        <CambianSelectionButton
                          variant="outlined"
                          isButtonSelected={
                            selectedTime === convertTimeIntoTwelveHourFormat(slot?.resource?.start, TIME_FORMAT)
                          }
                          buttonText={convertTimeIntoTwelveHourFormat(slot?.resource?.start, TIME_FORMAT)}
                          onClick={() => handleSlotSelectionCallback(slot)}
                        />
                      </Grid>
                    ),
                )}
              </Grid>
              <Stack direction="row" alignItems="center" justifyContent="center" spacing={2} sx={{ mt: 2, pb: 2 }}>
                <Button variant="outlined" onClick={previousSlots} disabled={!startIndex}>
                  <ArrowBackIosIcon />
                </Button>
                <Button variant="outlined" onClick={nextSlots} disabled={slotLimit > entry?.length - 1}>
                  <ArrowForwardIosIcon />
                </Button>
              </Stack>
            </Grid>
          ) : (
            <Typography variant="body1" sx={{ mt: 5 }}>
              {t('noSlots')}
            </Typography>
          )}
        </>
      ) : (
        <Typography variant="body1" sx={{ mt: 5 }}>
          {t('loading')}
        </Typography>
      )}
    </Grid>
  );
};
