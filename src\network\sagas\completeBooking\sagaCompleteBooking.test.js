import { runSaga } from 'redux-saga';
import * as api from './api';

import * as actions from '../../../redux/actions/completeBooking/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { completeBooking } from './index';

/**
 * This function test test case for get completeBooking details saga
 * Fires get completeBooking success of api gives success
 * Fires get completeBooking error of api fails
 */

describe('completeBooking', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.completeBookingApi = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.completeBooking(DUMMY_ITEM.data);
    await runSaga(fakeStore, completeBooking, requestResult).done;

    let successResult = actions.completeBookingActionSuccess(DUMMY_ITEM.data);

    expect(api.completeBookingApi.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.completeBookingApi = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.completeBooking(DUMMY_ITEM.data);
    await runSaga(fakeStore, completeBooking, requestResult).done;

    expect(api.completeBookingApi.mock.calls.length).toBe(1);

    let errorResult = actions.completeBookingActionError(DUMMY_ITEM.data);
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.completeBookingApi = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.completeBooking(DUMMY_ITEM.data);
    await runSaga(fakeStore, completeBooking, requestResult).done;

    expect(api.completeBookingApi.mock.calls.length).toBe(1);

    let resetResult = actions.resetBooking();
    const expectedAction = {
      type: actions.COMPLETE_BOOKING_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
