/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import {
  CONFIRM_APPOINTMENT_ERROR,
  CONFIRM_APPOINTMENT_REQUEST,
  CONFIRM_APPOINTMENT_SUCCESS,
  CONFIRM_APPOINTMENT_RESET,
} from '../../actions/confirmAppointment';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const confirmAppointmentReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case CONFIRM_APPOINTMENT_REQUEST:
      return {
        ...state,
        confirmAppointmentFetching: true,
        confirmAppointmentSuccess: false,
        confirmAppointmentError: false,
        confirmAppointmentErrorData: null,
      };
    case CONFIRM_APPOINTMENT_SUCCESS: {
      return {
        ...state,
        confirmAppointmentFetching: false,
        confirmAppointmentSuccess: true,
        confirmAppointmentError: false,
        confirmAppointmentErrorData: null,
        confirmAppointmentSuccessData: payload,
      };
    }
    case CONFIRM_APPOINTMENT_ERROR:
      return {
        ...state,
        confirmAppointmentFetching: false,
        confirmAppointmentSuccess: false,
        confirmAppointmentError: true,
        confirmAppointmentErrorData: payload,
      };
    case CONFIRM_APPOINTMENT_RESET:
      return {
        ...state,
        confirmAppointmentFetching: false,
        confirmAppointmentSuccess: false,
        confirmAppointmentError: true,
        confirmAppointmentErrorData: null,
        confirmAppointmentSuccessData: null,
      };
    default:
      return state;
  }
};
