/**
 * Service
 */

const handleAsyncApiForm = async (data, url) => {
  const formBody = Object.keys(data)
    .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(data[key]))
    .join('&');

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: formBody,
  });

  const responseJson = await response.json();
  return responseJson;
};

export { handleAsyncApiForm };
