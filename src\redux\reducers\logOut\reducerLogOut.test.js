import { LOG_OUT_REQUEST, LOG_OUT_SUCCESS, LOG_OUT_ERROR } from '../../actions/logOut';

import * as GLOBALS from '../globals';
import { userLogOutReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  userLogOutFetching: true,
  userLogOutSuccess: false,
  userLogOutError: false,
  userLogOutErrorData: null,
};

const successState = {
  userLogOutFetching: false,
  userLogOutSuccess: true,
  userLogOutError: false,
  userLogOutErrorData: null,
  userLogOutSuccessData: data,
};

const errorState = {
  userLogOutFetching: false,
  userLogOutSuccess: false,
  userLogOutError: true,
  userLogOutErrorData: data,
};

describe('Reschedule Reducer', () => {
  it('should return the initial state', () => {
    expect(userLogOutReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle LOG_OUT_REQUEST', () => {
    expect(
      userLogOutReducer(initialState, {
        type: LOG_OUT_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle LOG_OUT_SUCCESS', () => {
    expect(
      userLogOutReducer(initialState, {
        type: LOG_OUT_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle LOG_OUT_ERROR', () => {
    expect(
      userLogOutReducer(initialState, {
        type: LOG_OUT_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });
});
