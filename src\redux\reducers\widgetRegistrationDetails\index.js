/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import {
  WIDGET_REGISTRATION_DETAILS_REQUEST,
  WIDGET_REGISTRATION_DETAILS_SUCCESS,
  WIDGET_REGISTRATION_DETAILS_ERROR,
  WIDGET_REGISTRATION_DETAILS_RESET,
} from '../../actions/widgetRegistrationDetails';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const widgetRegistrationDetailsReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case WIDGET_REGISTRATION_DETAILS_REQUEST:
      return {
        ...state,
        isWidgetRegistrationDetailsFetching: true,
        fetchWidgetRegistrationDetailsSuccess: false,
        fetchWidgetRegistrationDetailsError: false,
        widgetRegistrationDetailsErrorData: null,
      };
    case WIDGET_REGISTRATION_DETAILS_SUCCESS: {
      return {
        ...state,
        isWidgetRegistrationDetailsFetching: false,
        fetchWidgetRegistrationDetailsSuccess: true,
        fetchWidgetRegistrationDetailsError: false,
        widgetRegistrationDetailsErrorData: null,
        widgetRegistrationDetailsSuccessData: payload,
      };
    }
    case WIDGET_REGISTRATION_DETAILS_ERROR:
      return {
        ...state,
        isWidgetRegistrationDetailsFetching: false,
        fetchWidgetRegistrationDetailsSuccess: false,
        fetchWidgetRegistrationDetailsError: true,
        widgetRegistrationDetailsErrorData: payload,
      };
    case WIDGET_REGISTRATION_DETAILS_RESET:
      return {
        ...state,
        isWidgetRegistrationDetailsFetching: false,
        fetchWidgetRegistrationDetailsSuccess: false,
        fetchWidgetRegistrationDetailsError: false,
        widgetRegistrationDetailsErrorData: null,
      };
    default:
      return state;
  }
};
