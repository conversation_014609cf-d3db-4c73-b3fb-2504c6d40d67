import React from 'react';
import { useSelector } from 'react-redux';
import { Button, Stack, Typography, Box, Paper, Grid } from '@mui/material';
import { HeaderSummary } from '../HeaderSummary';
import * as BookingUtility from '../bookingUtility';
import { useTranslation } from 'react-i18next';

export const RescheduleAppointment = (props) => {
  console.log('TRACE: RescheduleAppointment');
  const { t } = useTranslation();
  const { headerSummary, rescheduleAppointmentCallback, handleNavigationCallback, bookNewAppointmentCallback } = props;

  //get already booked appointment details
  const getBookedAppointmentSuccessData = useSelector(
    (state) => state.getBookedAppointmentReducer.getBookedAppointmentSuccessData,
  );

  const { entry: bookedAppointmentData } = getBookedAppointmentSuccessData || {};
  const appointmentStatus = BookingUtility.getAppointmentStatus(bookedAppointmentData);

  const getPage = () => {
    if (appointmentStatus === 'cancelled') {
      return cancelPage();
    } else {
      return reschedulePage();
    }
  };

  const cancelPage = () => {
    return (
      <Grid sx={{ mt: 5, mx: '2%', px: { xs: 2, md: 4 } }}>
        <Stack
          direction="column"
          alignItems="center"
          justifyContent="center"
          spacing={2}
          sx={{ pt: 0, px: 2, pb: 2, mt: 2 }}
        >
          <Typography variant={'h6'}>{t('cancelledAppointmentMessage')}</Typography>
          <Button variant="contained" onClick={bookNewAppointmentCallback}>
            {t('bookNewAppointment')}
          </Button>
        </Stack>
      </Grid>
    );
  };

  const reschedulePage = () => {
    return (
      <Grid sx={{ mt: 2, mx: '2%', px: { xs: 2, md: 4 } }}>
        <Box sx={{ my: 2 }}>
          <Typography variant={'h5'}>{t('rescheduleAppointment')}</Typography>
        </Box>
        <Typography variant={'body'}>{t('rescheduleSubHeading')}</Typography>

        <Paper sx={{ px: 2, my: 2 }}>
          <HeaderSummary headerSummary={headerSummary} />
        </Paper>

        <Stack
          direction="row"
          alignItems="center"
          justifyContent="center"
          spacing={2}
          sx={{ pt: 0, px: 2, pb: 2, mt: 3 }}
        >
          <Button variant="contained" onClick={rescheduleAppointmentCallback}>
            {t('rescheduleAppointment')}
          </Button>
          <Button variant="outlined" onClick={() => handleNavigationCallback()}>
            {t('backToAppointmentSummary')}
          </Button>
        </Stack>
      </Grid>
    );
  };

  return <>{getPage()}</>;
};
