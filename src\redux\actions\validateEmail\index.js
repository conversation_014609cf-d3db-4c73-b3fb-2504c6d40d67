/*
 * VALIDATE_EMAIL action types
 */

export const VALIDATE_EMAIL_REQUEST = 'VALIDATE_EMAIL_REQUEST';
export const VALIDATE_EMAIL_SUCCESS = 'VALIDATE_EMAIL_SUCCESS';
export const VALIDATE_EMAIL_ERROR = 'VALIDATE_EMAIL_ERROR';
export const VALIDATE_EMAIL_RESET = 'VALIDATE_EMAIL_RESET';

/*
 * action creators
 */
export function validateEmail(data) {
  return {
    type: VALIDATE_EMAIL_REQUEST,
    payload: data,
  };
}

export function validateRegistraionEmailSuccess(data) {
  return {
    type: VALIDATE_EMAIL_SUCCESS,
    payload: data,
  };
}

export function validateRegistraionEmailError() {
  return {
    type: VALIDATE_EMAIL_ERROR,
  };
}

export const resetValidateEmail = () => {
  return {
    type: VALIDATE_EMAIL_RESET,
  };
};
