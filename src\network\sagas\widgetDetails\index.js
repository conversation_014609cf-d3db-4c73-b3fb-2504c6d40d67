/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import {
  WIDGET_DETAILS_ERROR,
  WIDGET_DETAILS_REQUEST,
  WIDGET_DETAILS_SUCCESS,
} from '../../../redux/actions/widgetDetails';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiFetchWidgetDetails } from './api';
/**
 *
 * @param {*} action
 */

export function* fetchWidgetDetails(action) {
  try {
    const response = yield call(makeApiFetchWidgetDetails, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: WIDGET_DETAILS_SUCCESS, payload: data });
    } else {
      yield put({ type: WIDGET_DETAILS_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: WIDGET_DETAILS_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchWidgetDetails() {
  yield takeEvery(WIDGET_DETAILS_REQUEST, fetchWidgetDetails);
}
