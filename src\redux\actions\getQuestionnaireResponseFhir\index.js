export const GET_QUESTIONNAIRE_RESPONSE_FHIR_REQUEST = 'GET_QUESTIONNAIRE_RESPONSE_FHIR_REQUEST';
export const GET_QUESTIONNAIRE_RESPONSE_FHIR_SUCCESS = 'GET_QUESTIONNAIRE_RESPONSE_FHIR_SUCCESS';
export const GET_QUESTIONNAIRE_RESPONSE_FHIR_ERROR = 'GET_QUESTIONNAIRE_RESPONSE_FHIR_ERROR';
export const GET_QUESTIONNAIRE_RESPONSE_FHIR_RESET = 'GET_QUESTIONNAIRE_RESPONSE_FHIR_RESET';
export const SAVE_QUESTIONNAIRE_RESPONSE_FHIR = 'SAVE_QUESTIONNAIRE_RESPONSE_FHIR';

export function getQuestionnaireResponseFhir(data) {
  return {
    type: GET_QUESTIONNAIRE_RESPONSE_FHIR_REQUEST,
    payload: data,
  };
}

export function getQuestionnaireResponseFhirSuccess(data) {
  return {
    type: GET_QUESTIONNAIRE_RESPONSE_FHIR_SUCCESS,
    payload: data,
  };
}

export function getQuestionnaireResponseFhirError() {
  return {
    type: GET_QUESTIONNAIRE_RESPONSE_FHIR_ERROR,
  };
}

export function saveGeneratedFhirQuestionnaireResponse(payload) {
  return {
    type: SAVE_QUESTIONNAIRE_RESPONSE_FHIR,
    payload: payload,
  };
}

export function getQuestionnaireResponseFhirReset() {
  return {
    type: GET_QUESTIONNAIRE_RESPONSE_FHIR_RESET,
  };
}
