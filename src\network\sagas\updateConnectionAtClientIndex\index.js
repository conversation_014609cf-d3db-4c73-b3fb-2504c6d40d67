import { call, put, takeEvery } from 'redux-saga/effects';
import {
  UPDATE_CONNECTION_AT_CLIENT_INDEX_REQUEST,
  UPDATE_CONNECTION_AT_CLIENT_INDEX_SUCCESS,
  UPDATE_CONNECTION_AT_CLIENT_INDEX_ERROR,
} from '../../../redux/actions/updateConnectionAtClientIndex';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { updateConnectionAtClientIndex } from './api';

export function* fetchUpdateConnectionAtClientIndex(action) {
  try {
    const response = yield call(updateConnectionAtClientIndex, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: UPDATE_CONNECTION_AT_CLIENT_INDEX_SUCCESS, payload: data });
    } else {
      yield put({ type: UPDATE_CONNECTION_AT_CLIENT_INDEX_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: UPDATE_CONNECTION_AT_CLIENT_INDEX_ERROR });
  }
}

export function* watchUpdateConnectionAtClientIndex() {
  yield takeEvery(UPDATE_CONNECTION_AT_CLIENT_INDEX_REQUEST, fetchUpdateConnectionAtClientIndex);
}
