/*
 *CANCEL_BOOKING action types
 */

export const CANCEL_BOOKING_REQUEST = 'CANCEL_BOOKING_REQUEST';
export const CANCEL_BOOKING_SUCCESS = 'CANCEL_BOOKING_SUCCESS';
export const CANCEL_BOOKING_ERROR = 'CANCEL_BOOKING_ERROR';
export const CANCEL_BOOKING_RESET = 'CANCEL_BOOKING_RESET';

/*
 * action creators
 */

export function cancelBooking(data) {
  return {
    type: CANCEL_BOOKING_REQUEST,
    payload: data,
  };
}

export function cancelBookingSuccess(data) {
  return {
    type: CA<PERSON><PERSON>_BOOKING_SUCCESS,
    payload: data,
  };
}

export function cancelActionBookingError() {
  return {
    type: CANCEL_BOOKING_ERROR,
  };
}

export function resetCancel() {
  return {
    type: CANC<PERSON>_BOOKING_RESET,
  };
}
