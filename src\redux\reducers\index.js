import { combineReducers } from 'redux';
import storage from 'redux-persist/lib/storage';
// reducers
import { widgetDetailsReducer } from './widgetDetails';
import { getLocationsReducer } from './getLocations';
import { careReducer } from './care';
import { bookingSlotsReducer } from './getBookingSlots';
import { completeBookingReducer } from './completeBooking';
import { cancelBookingReducer } from './cancelBooking';
import { userLogOutReducer } from './logOut';
import { getBookedAppointmentReducer } from './getBookedAppointmentDetails';
import { rescheduleAppointmentReducer } from './rescheduleAppointment';
import { confirmAppointmentReducer } from './confirmAppointment';
import { oAuthReducer } from './oAuth';
import { userInfoReducer } from './userInfo';
import { widgetRegistrationDetailsReducer } from './widgetRegistrationDetails';
import { completeRegistrationReducer } from './completeRegistration';
import { validateEmailReducer } from './validateEmail';
import { CARE_RESET } from '../actions/care';
import { GET_QUESTIONNAIRE_WIDGET_RESET } from '../actions/getQuestionnaireWidget';
import { resendOtpReducer } from './resendOtp';
import { getQuestionnaireQuestionRedux } from './getQuestionnaireDetails';
import { checkEmailReducer } from './checkEmailInBooking';
import { validateEmailInBookingReducer } from './validateEmailInBooking';
import { questionnaireWidgetReducer } from './getQuestionnaireWidget';
import { createOtpReducer } from './CreateOtp';
import { addNewPatientReducer } from './addNewClient';
import { searchAndCreatePatientReducer } from './searchAndCreatePatient';
import { getNextAvailableSlotReducer } from './getNextAvailableSlot';
import { getPdfReducer } from './getPdfReport';
import { individualUserOAuthReducer } from './getIndividualUserOAuth';
import { individualUserInfoReducer } from './getIndividualUserInfo';
import { saveQuestionnaireResponseForLaterReducer } from './saveQuestionnaireResponseForLater';
import { generateHtmlReportAsPdfReducer } from './generateHtmlReportAsPdf';
import { getReportDataForPdfReducer } from './getReportDataForPdf';
import { sendConnectionInviteReducer } from './sendConnectionInvite';
import { widgetAuthReducer } from './widgetAuth';
import { saveQuestionnaireResponseIndividualReducer } from './saveQuestionnaireResponseIndividual';
import { saveQuestionnaireResponseOrganizationReducer } from './saveQuestionnaireResponseOrganization';
import { checkExistingClientIndexReducer } from './checkExistingClientIndex';
import { checkExistingConnectionIndexReducer } from './checkExistingConnectionIndex';
import { createConnectionAtClientIndexReducer } from './createConnectionAtClientIndex';
import { createConnectionAtConnectionIndexReducer } from './createConnectionAtConnectionIndex';
import { updateConnectionAtClientIndexReducer } from './updateConnectionAtClientIndex';
import { searchIndividualAtClientIndexReducer } from './searchIndividualAtClientIndex';
import { consentAgreementReducer } from './getConsentAgreement';
import { getIndividualDataReducer } from './getIndividualData';
import { createIndividualDataReducer } from './createIndividualData';

const appReducer = combineReducers({
  careReducer,
  widgetDetailsReducer,
  getLocationsReducer,
  bookingSlotsReducer,
  completeBookingReducer,
  cancelBookingReducer,
  getBookedAppointmentReducer,
  rescheduleAppointmentReducer,
  confirmAppointmentReducer,
  oAuthReducer,
  userInfoReducer,
  widgetRegistrationDetailsReducer,
  completeRegistrationReducer,
  validateEmailReducer,
  resendOtpReducer,
  userLogOutReducer,
  getQuestionnaireQuestionRedux,
  checkEmailReducer,
  validateEmailInBookingReducer,
  questionnaireWidgetReducer,
  createOtpReducer,
  addNewPatientReducer,
  searchAndCreatePatientReducer,
  getNextAvailableSlotReducer,
  getPdfReducer,
  saveQuestionnaireResponseForLaterReducer,
  individualUserOAuthReducer,
  individualUserInfoReducer,
  generateHtmlReportAsPdfReducer,
  getReportDataForPdfReducer,
  sendConnectionInviteReducer,
  widgetAuthReducer,
  saveQuestionnaireResponseIndividualReducer,
  saveQuestionnaireResponseOrganizationReducer,
  checkExistingClientIndexReducer,
  checkExistingConnectionIndexReducer,
  createConnectionAtClientIndexReducer,
  createConnectionAtConnectionIndexReducer,
  updateConnectionAtClientIndexReducer,
  searchIndividualAtClientIndexReducer,
  consentAgreementReducer,
  getIndividualDataReducer,
  createIndividualDataReducer,
});

export const rootReducer = (state, action) => {
  if (action.type === CARE_RESET) {
    // for all keys defined in your persistConfig(s)
    storage.removeItem('persist:root');
    storage.removeItem('token');
    state = undefined;
  }
  if (action.type === GET_QUESTIONNAIRE_WIDGET_RESET) {
    storage.removeItem('persist:root');
    state = undefined;
    //return appReducer(undefined, action);
  }

  return appReducer(state, action);
};
