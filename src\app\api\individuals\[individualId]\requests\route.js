import { makeFetchNetworkCall } from '@/network';
import { makeServerFetchNetworkCall } from '@/network/serverAPIUtility';
import { INDIVIDUAL } from '@/utils/constants';
import { NextResponse } from 'next/server';

const IND_REQUEST_BASE_URL = process.env.NEXT_PUBLIC_INDIVIDUAL_REQUEST_BASE_URL;

export async function POST(request, { params }) {
  try {
    const { individualId } = params;
    const body = await request.json();
    const { qrId, orgRequest, requestId } = body;

    if (!individualId || !requestId) {
      return NextResponse.json(
        { error: 'Either or all of required parameters are missing: individualId, requestId' },
        { status: 400 },
      );
    }

    const individualRequest = {
      ...orgRequest,
      requestDetails: {
        ...(orgRequest.requestDetails || {}),
        questionnaireResponseId: qrId,
      },
    };

    const url = `${IND_REQUEST_BASE_URL}/individuals/${individualId}/requests`;

    const response = await makeServerFetchNetworkCall({
      method: 'POST',
      url: url,
      data: individualRequest,
      targetAwsEnv: INDIVIDUAL,
    });

    if (!response.ok) {
      return NextResponse.json({ error: 'Failed to create individual request' }, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error creating individual request:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
