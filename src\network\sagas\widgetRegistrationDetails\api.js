import { GET_REGISTRATION_WIDGET } from '@/utils/constants/awsApiEndpoints';
import { makeNetworkCall } from '../..';
import { ORGANIZATION_ID, WIDGET_ID } from '../../../utils/constants';

export function makeApiFetchWidgetRegistrationDetails(action) {
  const { payload = {} } = action || {};
  const { headers, organizationId, widgetId, lang } = payload || {};

  let URL = GET_REGISTRATION_WIDGET.replace(ORGANIZATION_ID, organizationId).replace(WIDGET_ID, widgetId);

  // Add language parameter if specified
  if (lang) {
    URL = `${URL}?lang=${lang}`;
  }

  const config = {
    method: 'GET',
    url: URL,
    headers: headers,
    formData: false,
  };

  return makeNetworkCall(config);
}
