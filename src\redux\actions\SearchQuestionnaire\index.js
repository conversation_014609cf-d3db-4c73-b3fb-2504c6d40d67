/*
 *SEARCH_QUESTIONNAIRE action types
 */

export const <PERSON><PERSON><PERSON>_QUESTIONNAIRE_REQUEST = 'SEARCH_QUESTIONNAIRE_REQUEST';
export const SEARCH_QUESTIONNAIRE_SUCCESS = 'SEARCH_QUESTIONNAIRE_SUCCESS';
export const SEARCH_QUESTIONNAIRE_ERROR = 'SEARCH_QUESTIONNAIRE_ERROR';
export const SEARCH_QUESTIONNAIRE_RESET = 'SEARCH_QUESTIONNAIRE_RESET';

/*
 * action creators
 */

export function searchQuestionnaireDetails(data) {
  return {
    type: SEARCH_QUESTIONNAIRE_REQUEST,
    payload: data,
  };
}

export function searchQuestionnaireActionSuccess(data) {
  return {
    type: SEARCH_QUESTIONNAIRE_SUCCESS,
    payload: data,
  };
}

export function searchQuestionnaireActionError() {
  return {
    type: SEARCH_QUESTIONNAIRE_ERROR,
  };
}

export const resetQuestionnaire = () => {
  return {
    type: <PERSON><PERSON><PERSON>_QUESTIONNAIRE_RESET,
  };
};
