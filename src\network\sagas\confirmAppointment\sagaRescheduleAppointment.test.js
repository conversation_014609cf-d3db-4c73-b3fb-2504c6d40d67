import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/confirmAppointment/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { confirmAppointment } from './index';

/**
 * This function test test case for get confirmAppointment details saga
 * Fires get confirmAppointment success of api gives success
 * Fires get confirmAppointment error of api fails
 */

describe('confirmAppointment', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.confirmAppointmentApi = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.confirmAppointmentDetails(DUMMY_ITEM.data);
    await runSaga(fakeStore, confirmAppointment, requestResult).done;

    let successResult = actions.confirmAppointmentActionSuccess(DUMMY_ITEM.data);

    expect(api.confirmAppointmentApi.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.confirmAppointmentApi = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.confirmAppointmentDetails(DUMMY_ITEM.data);
    await runSaga(fakeStore, confirmAppointment, requestResult).done;

    expect(api.confirmAppointmentApi.mock.calls.length).toBe(1);

    let errorResult = actions.confirmAppointmentActionError();
    expect(dispatchedActions).toContainEqual(errorResult);
  });
});
