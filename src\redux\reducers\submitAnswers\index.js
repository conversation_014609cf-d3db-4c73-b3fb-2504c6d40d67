import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  POST_REGISTRATION_QUESTIONS_ERROR,
  POST_REGISTRATION_QUESTIONS_SUCCESS,
  POST_REGISTRATION_QUESTIONS_REQUEST,
} from '../../actions/submitAnswers';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const submitAnswersReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case POST_REGISTRATION_QUESTIONS_REQUEST:
      return {
        ...state,
        isSubmitAnswersFetching: true,
        isSubmitAnswersSuccess: false,
        isSubmitAnswersError: false,
        submitAnswersErrorData: null,
      };
    case POST_REGISTRATION_QUESTIONS_SUCCESS: {
      return {
        ...state,
        isSubmitAnswersFetching: false,
        isSubmitAnswersSuccess: true,
        isSubmitAnswersError: false,
        submitAnswersErrorData: null,
        submitAnswersSuccessData: payload,
      };
    }
    case POST_REGISTRATION_QUESTIONS_ERROR:
      return {
        ...state,
        isSubmitAnswersFetching: false,
        isSubmitAnswersSuccess: false,
        isSubmitAnswersError: true,
        submitAnswersErrorData: payload,
      };
    default:
      return state;
  }
};
