import { makeNetworkCall } from '../..';
import { ORGANIZATION_ID } from '../../../utils/constants';
import { CREATE_PATIENTS } from '@/utils/constants/awsApiEndpoints';

export function searchAndCreatePatientDataApi(action) {
  const { payload = {} } = action || {};
  const { headers = {}, searchAndCreatePatientData = {}, organizationId } = payload || {};

  const config = {
    method: 'POST',
    url: CREATE_PATIENTS.replace(ORGANIZATION_ID, organizationId),
    data: searchAndCreatePatientData,
  };

  return makeNetworkCall(config);
}
