import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import { CREATE_OTP_REQUEST, CREATE_OTP_SUCCESS, CREATE_OTP_ERROR, CREATE_OTP_RESET } from '../../actions/CreateOtp';
export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};
export const createOtpReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case CREATE_OTP_REQUEST:
      return {
        ...state,
        isCreateOtpFetching: true,
        createOtpSuccess: false,
        createOtpError: false,
        createOtpErrorData: null,
      };
    case CREATE_OTP_SUCCESS: {
      return {
        ...state,
        isCreateOtpFetching: false,
        createOtpSuccess: true,
        createOtpError: false,
        createOtpErrorData: null,
        createOtpSuccessData: payload,
      };
    }
    case CREATE_OTP_ERROR:
      return {
        ...state,
        isCreateOtpFetching: false,
        createOtpSuccess: false,
        createOtpError: true,
        createOtpErrorData: payload,
      };
    case CREATE_OTP_RESET:
      return {
        ...state,
        isCreateOtpFetching: false,
        createOtpSuccess: false,
        createOtpError: false,
        createOtpErrorData: null,
      };
    default:
      return state;
  }
};
