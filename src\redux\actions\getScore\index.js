/*
 * QUESTIONNAIRE_WIDGET_DETAILS action types
 */

export const GET_SCORE_REQUEST = 'GET_SCORE_REQUEST';
export const GET_SCORE_SUCCESS = 'GET_SCORE_SUCCESS';
export const GET_SCORE_ERROR = 'GET_SCORE_ERROR';
export const GET_SCORE_RESET = 'GET_SCORE_RESET';

/*
 * action creators
 */

export function getScoreInfo(data) {
  return {
    type: GET_SCORE_REQUEST,
    payload: data,
  };
}

export function getScoreSuccess(data) {
  return {
    type: GET_SCORE_SUCCESS,
    payload: data,
  };
}

export function getScoreError() {
  return {
    type: GET_SCORE_ERROR,
  };
}

export const resetGetScoreInfo = () => {
  return {
    type: GET_SCORE_RESET,
  };
};
