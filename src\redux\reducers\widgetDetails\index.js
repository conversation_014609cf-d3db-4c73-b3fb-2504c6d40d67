/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import {
  WIDGET_DETAILS_RESET,
  WIDGET_DETAILS_ERROR,
  WIDGET_DETAILS_SUCCESS,
  WIDGET_DETAILS_REQUEST,
} from '../../actions/widgetDetails';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const widgetDetailsReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;

  switch (type) {
    case WIDGET_DETAILS_REQUEST:
      return {
        ...state,
        isWidgetDetailsFetching: true,
        fetchWidgetDetailsSuccess: false,
        fetchWidgetDetailsError: false,
        widgetDetailsErrorData: null,
      };
    case WIDGET_DETAILS_SUCCESS: {
      return {
        ...state,
        isWidgetDetailsFetching: false,
        fetchWidgetDetailsSuccess: true,
        fetchWidgetDetailsError: false,
        widgetDetailsErrorData: null,
        widgetDetailsSuccessData: payload,
      };
    }
    case WIDGET_DETAILS_ERROR:
      return {
        ...state,
        isWidgetDetailsFetching: false,
        fetchWidgetDetailsSuccess: false,
        fetchWidgetDetailsError: true,
        widgetDetailsErrorData: payload,
      };
    case WIDGET_DETAILS_RESET:
      return {
        ...state,
        isWidgetDetailsFetching: false,
        fetchWidgetDetailsSuccess: false,
        fetchWidgetDetailsError: false,
        widgetDetailsSuccessData: null,
        widgetDetailsErrorData: null,
      };
    default:
      return state;
  }
};
