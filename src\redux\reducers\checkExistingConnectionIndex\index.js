import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  CHECK_EXISTING_CONNECTION_INDEX_REQUEST,
  CHECK_EXISTING_CONNECTION_INDEX_SUCCESS,
  CHECK_EXISTING_CONNECTION_INDEX_ERROR,
  CHECK_EXISTING_CONNECTION_INDEX_RESET,
} from '../../actions/checkExistingConnectionIndex';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const checkExistingConnectionIndexReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case CHECK_EXISTING_CONNECTION_INDEX_REQUEST:
      return {
        ...state,
        isCheckExistingConnectionIndexFetching: true,
        checkExistingConnectionIndexSuccess: false,
        checkExistingConnectionIndexError: false,
        checkExistingConnectionIndexErrorData: null,
      };
    case CHECK_EXISTING_CONNECTION_INDEX_SUCCESS: {
      return {
        ...state,
        isCheckExistingConnectionIndexFetching: false,
        checkExistingConnectionIndexSuccess: true,
        checkExistingConnectionIndexError: false,
        checkExistingConnectionIndexErrorData: null,
        checkExistingConnectionIndexSuccessData: payload,
      };
    }
    case CHECK_EXISTING_CONNECTION_INDEX_ERROR:
      return {
        ...state,
        isCheckExistingConnectionIndexFetching: false,
        checkExistingConnectionIndexSuccess: false,
        checkExistingConnectionIndexSuccessData: null,
        checkExistingConnectionIndexError: true,
        checkExistingConnectionIndexErrorData: payload,
      };
    case CHECK_EXISTING_CONNECTION_INDEX_RESET:
      return {
        ...state,
        isCheckExistingConnectionIndexFetching: undefined,
        checkExistingConnectionIndexSuccess: undefined,
        checkExistingConnectionIndexSuccessData: undefined,
        checkExistingConnectionIndexError: undefined,
        checkExistingConnectionIndexErrorData: undefined,
      };
    default:
      return state;
  }
};
