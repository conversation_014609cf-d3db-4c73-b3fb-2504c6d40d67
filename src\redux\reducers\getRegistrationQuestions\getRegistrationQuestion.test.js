import {
  GET_REGISTRATION_QUESTIONS_REQUEST,
  GET_REGISTRATION_QUESTIONS_SUCCESS,
  GET_REGISTRATION_QUESTIONS_ERROR,
} from '../../actions/getRegistrationQuestions';
import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import { getRegistrationQuestionsReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isRegistrationQuestionsFetching: true,
  isRegistrationQuestionsSuccess: false,
  isRegistrationQuestionsError: false,
  registrationQuestionsErrorData: null,
};

const successState = {
  isRegistrationQuestionsFetching: false,
  isRegistrationQuestionsSuccess: true,
  isRegistrationQuestionsError: false,
  registrationQuestionsErrorData: null,
  registrationQuestionsSuccessData: data,
};

const errorState = {
  isRegistrationQuestionsFetching: false,
  isRegistrationQuestionsSuccess: false,
  isRegistrationQuestionsError: true,
  registrationQuestionsErrorData: data,
};

describe('Complete Booking  slots Reducer', () => {
  it('should return the initial state', () => {
    expect(getRegistrationQuestionsReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle GET_REGISTRATION_QUESTIONS_REQUEST', () => {
    expect(
      getRegistrationQuestionsReducer(initialState, {
        type: GET_REGISTRATION_QUESTIONS_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle GET_REGISTRATION_QUESTIONS_SUCCESS', () => {
    expect(
      getRegistrationQuestionsReducer(initialState, {
        type: GET_REGISTRATION_QUESTIONS_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle GET_REGISTRATION_QUESTIONS_ERROR', () => {
    expect(
      getRegistrationQuestionsReducer(initialState, {
        type: GET_REGISTRATION_QUESTIONS_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });
});
