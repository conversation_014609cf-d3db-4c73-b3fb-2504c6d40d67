/* export const demoQuestionnaireData = {
  id: 1,
  name: "Test",
  flow: "INDIVIDUAL",
  clientGroup: {
    id: 2,
    cambianReferenceId: "851f8c8a-eedd-4bc2-b4af-e8f4c3920cd7",
    cambianReferenceData: {
      id: "851f8c8a-eedd-4bc2-b4af-e8f4c3920cd7",
      name: "Group 3",
      version: 0,
      type: "CLINICAL",
      ownerOrganizationId: "9b77482c-14d6-11eb-9af0-06f84590db43",
      assignmentMode: "QUESTIONNAIRE",
      viewableOnlyByOrgAdmin: "false",
      questionnaireUnits: [],
    },
    updatedAt: "2022-03-12T06:45:51.445Z",
    createdAt: "2022-03-12T06:45:51.445Z",
  },
  emailVerification: false,
  showProgressBar: true,
  multipleIndividualEnabled: false,
  showLogin: true,
  demographicFlag: false,
  appearance: {
    primarySelectionColor: "#47AAFF",
    mainTextColor: "#5880AA",
    secondaryTextColor: "#6B6B67",
    headingColor: "#000000",
    updatedAt: "2022-05-23T09:19:36.979Z",
    createdAt: "2022-05-23T09:19:36.979Z",
  },
  fields: [
    {
      id: 9,
      position: 1,
      isMandatory: true,
      display: "First Name",
      code: "FIRST_NAME",
      updatedAt: "2022-05-23T09:19:36.980Z",
      createdAt: "2022-05-23T09:19:36.980Z",
    },
    {
      id: 6,
      position: 2,
      isMandatory: true,
      display: "Last Name",
      code: "LAST_NAME",
      updatedAt: "2022-05-23T09:19:36.981Z",
      createdAt: "2022-05-23T09:19:36.981Z",
    },
    {
      id: 4,
      position: 5,
      isMandatory: true,
      display: "Email",
      code: "EMAIL",
      updatedAt: "2022-05-23T09:19:36.980Z",
      createdAt: "2022-05-23T09:19:36.980Z",
    },
  ],
  questionnaire: {
    id: 109,
    cambianReferenceId: "bc16293b-2eb9-4048-9b3e-51889c945675",
    name: "Test questionnaire for display logic",
    cambianReferenceData: {
      id: "bc16293b-2eb9-4048-9b3e-51889c945675",
      identifier: [
        {
          system: "urn:uuid",
          value: "bc16293b-2eb9-4048-9b3e-51889c945675",
        },
      ],
      name: "Test questionnaire for display logic",
      title: "Test questionnaire for display logic result",
      status: "active",
      description: "cannot locate string",
      purpose: "Instrument",
      resourceType: "Questionnaire",
    },
    updatedAt: "2022-05-03T11:58:23.302Z",
    createdAt: "2022-05-03T11:58:23.302Z",
  },
  introduction: {
    enabled: false,
  },
  report: {
    enable: true,
    showDownloadIcon: true,
    showPrintIcon: true,
  },
  result: {
    enabled: false,
    resultResponse: {
      actionFieldsData: [
        {
          displayScore: "Display if score is ",
          selectScore: [],
          scoreDefinitionName: "",
          scoreDropDown: [
            {
              id: 0,
              label: "Equal to",
              value: "=",
            },
            {
              id: 1,
              label: "Greater than",
              value: ">",
            },
            {
              id: 2,
              label: "Greater than or equal",
              value: ">=",
            },
            {
              id: 3,
              label: "Less than",
              value: "<",
            },
            {
              id: 4,
              label: "Less than or equal",
              value: "<=",
            },
            {
              id: 5,
              label: "Between",
              value: "between",
            },
          ],
          selectedDropdownScore: "Less than",
          selectedScore: "",
          action: "Action",
          actionDropDown: [
            {
              id: 0,
              label: "Heading and description page",
              value: "Heading and description page",
            },
            {
              id: 1,
              label: "Redirect to URL",
              value: "Redirect to URL",
            },
            {
              id: 2,
              label: "Redirect to Widget",
              value: "Redirect to Widget",
            },
            {
              id: 3,
              label: "Call a service in background",
              value: "Call a service in background",
            },
          ],
          selectedAction: "Redirect to URL",
          targetDropDown: [
            {
              id: 0,
              label: "open in new tab",
              value: "open in new tab",
            },
            {
              id: 1,
              label: "open in the same tab",
              value: "open in the same tab",
            },
          ],
          selectedTarget: "open in new tab",
          widgetTypes: [
            {
              id: 0,
              label: "Booking Widget",
              value: "Booking Widget",
            },
            {
              id: 1,
              label: "Questionnaire Widget",
              value: "Questionnaire Widget",
            },
            {
              id: 2,
              label: "Registration Widget",
              value: "Registration Widget",
            },
          ],
          selectedWidgetType: "",
          selectedWidget: {
            name: "",
          },
          serviceDropdown: [],
          selectedService: "",
          scoreValue: "100",
          actionUrl: "",
          demographicFieldsAdd: false,
        },
      ],
    },
  },
  headingAndDescription: {
    enable: false,
    showDownloadIcon: true,
    showPrintIcon: true,
    heading: "Thank you",
    description: "<p>Thank you for filling out the Questionnaire</p>\n",
    discardHeading: "Questionnaire has been discarded",
    discardDescription: "<p>Questionnaire has been discarded and your response has been not saved</p>",
    saveLaterHeading: "Saved for later",
    saveLaterHDescription:
      "<p>Your questionnaire has been saved. You can continue your questionnaire by signing into your Cambian Navigator account.</p>",
    downloadButtonText: "",
  },
  createdAt: "2022-03-12T07:03:09.369Z",
  updatedAt: "2022-05-23T09:19:36.982Z",
}; */

export const demoQuestionnaireWidget = {
  PK: 'ORGANIZATION#978456c9-7ab5-4a64-80cc-aa11f8a86cbc',
  SK: 'QUESTIONNAIRE_WIDGET#a104576b-4e8e-4c85-9ecc-2dc107e0c72b',
  name: 'Preview questionnaire widget',
  widgetType: 'QUESTIONNAIRE',
  widgetTitle: 'Preview',
  showSignIn: false,
  identification: 'UNIDENTIFIED',
  questionnaire: {
    artifactId: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
    shortName: 'CV_risk_2023',
  },
  flow: 'INDIVIDUAL',
  showProgressBar: false,
  showProgressPercentage: false,
  spinnerText: '',
  nextButtonText: '',
  previousButtonText: '',
  doneButtonText: '',
  showClientInformation: false,
  otpVerificationEnabled: false,
  provinces: [],
  introduction: {
    enabled: false,
    heading: '',
    description: '',
  },
  report: {
    enabled: true,
    showDownloadIcon: true,
    showPrintIcon: true,
  },
  headingAndDescriptionPage: {
    enabled: false,
    heading: '',
    description: '',
    showDownloadIcon: true,
    showPrintIcon: false,
  },
  registerPage: {
    heading: '',
    buttonText: '',
  },
  saveForLaterPage: {
    heading: '',
    description: '',
  },
  discardPage: {
    heading: '',
    description: '',
  },
  action: {
    enabled: false,
    metaData: {
      actionConditions: [
        {
          displayScore: 'Display if score is ',
          selectScore: [],
          scoreDropDown: [
            {
              id: 0,
              label: 'Equal to',
              value: '=',
            },
            {
              id: 1,
              label: 'Greater than',
              value: '>',
            },
            {
              id: 2,
              label: 'Greater than or equal',
              value: '>=',
            },
            {
              id: 3,
              label: 'Less than',
              value: '<',
            },
            {
              id: 4,
              label: 'Less than or equal',
              value: '<=',
            },
            {
              id: 5,
              label: 'Between',
              value: 'between',
            },
          ],
          actionDropDown: [
            {
              id: 0,
              label: 'Heading and Description Page',
              value: 'Heading and Description Page',
            },
            {
              id: 1,
              label: 'Redirect to URL',
              value: 'Redirect to URL',
            },
            {
              id: 2,
              label: 'Redirect to Widget',
              value: 'Redirect to Widget',
            },
            {
              id: 3,
              label: 'Call Background Service',
              value: 'Call Background Service',
            },
          ],
          targetDropDown: [
            {
              id: 0,
              label: 'Open in new Tab',
              value: 'Open in new Tab',
            },
            {
              id: 1,
              label: 'Open in same Tab',
              value: 'Open in same Tab',
            },
            {
              id: 2,
              label: 'Open in same IFrame',
              value: 'Open in same IFrame',
            },
          ],
          widgetTypes: [
            {
              id: 0,
              label: 'Booking Widget',
              value: 'Booking Widget',
            },
            {
              id: 1,
              label: 'Questionnaire Widget',
              value: 'Questionnaire Widget',
            },
            {
              id: 2,
              label: 'Registration Widget',
              value: 'Registration Widget',
            },
          ],
          scoreDefinitionName: '',
          selectedDropdownScore: '',
          selectedScore: '',
          selectedAction: '',
          selectedTarget: '',
          selectedWidgetType: '',
          selectedWidget: {
            name: '',
            id: null,
          },
          serviceDropdown: [
            {
              id: 0,
              label: 'Call Background URL',
              value: 'Call Background URL',
            },
          ],
          selectedService: '',
          backgroundServiceEndpoint: '',
          appendIncomingParameters: false,
          demographicPageShow: false,
          demographicFieldsAdd: false,
        },
      ],
    },
  },
  createdAt: '2024-02-25T14:10:54.982Z',
  updatedAt: '2024-02-25T14:10:54.982Z',
};
