import { resetWidgetDetails } from '@/redux/actions/widgetDetails';
import { resetConfirmAppointment } from '@/redux/actions/confirmAppointment';
import { fetchLocationsReset } from '@/redux/actions/getLocations';
import { getBookedAppointmentReset } from '@/redux/actions/getBookedAppointmentDetails';
import { fetchNextAvailableSlotReset } from '@/redux/actions/getNextAvailableSlot';
import { fetchBookingSlotsReset } from '@/redux/actions/getBookingSlots';
import { resetBooking } from '@/redux/actions/completeBooking';
import { resetRescheduleAppointment } from '@/redux/actions/rescheduleAppointment';
import { resetCancel } from '@/redux/actions/cancelBooking';
import dataValidation, { isDataValid } from '@/utils/dataValidation/dataValidation';
import { convertTimeIntoTwelveHourFormatIgnoreZone } from '@/utils/helpers/date-time';
import { TIME_FORMAT, DATE_FORMAT } from '@/utils/constants';
import { resetCheckEmail } from '@/redux/actions/checkEmailInBooking';
import { resetGetIndividualData } from '@/redux/actions/getIndividualData';
import { v4 as uuidv4 } from 'uuid';
import * as CommonUtility from '../commonUtility';
import { getPatientObj, getAppointmentObj, getAppointmentObjOfReschedule } from '@/utils/data';
import moment from 'moment';
import { pages } from './bookingConstants';
import i18next from '@/i18n';
import { addDays, addMonths, addWeeks, isAfter, isBefore, isToday, isValid } from 'date-fns';

export const getPageByBookingWidgetFlowType = () => {
  let page;
  page = pages.SERVICE;

  return page;
};

export const getFirstBookingScreen = (introduction) => {
  let page;
  if (CommonUtility.isParamInUrl('appointmentId')) {
    page = pages.DEMOGRAPHIC;
  } else {
    if (introduction?.enabled) {
      page = pages.INTRODUCTION;
    } else {
      page = pages.SERVICE;
    }
  }
  return page;
};

export function resetAllAPIReducers(dispatch) {
  console.log('booking reset');
  dispatch(fetchLocationsReset());
  dispatch(fetchNextAvailableSlotReset());
  dispatch(getBookedAppointmentReset());
  dispatch(fetchBookingSlotsReset());
  dispatch(resetBooking());
  dispatch(resetRescheduleAppointment());
  dispatch(resetConfirmAppointment());
  dispatch(resetCancel());
  dispatch(resetCheckEmail());
  dispatch(resetGetIndividualData());
  CommonUtility.resetConnectionAndClientReducers(dispatch);
}

export function resetAllAPIReducersIncludingBooking(dispatch) {
  console.log('booking reset with questionnaire apis');
  resetAllAPIReducers(dispatch);
  dispatch(resetWidgetDetails());
}

export function getAddress(location) {
  var line1 = '';
  var address = '';

  if (!dataValidation.isDataEmpty(location) && isDataValid(location.address)) {
    location.address.line.map((value) => (line1 = line1 + value + ', '));
    address = line1;
    if (isDataValid(location.address.city)) address = address + location.address.city;
    if (isDataValid(location.address.state)) address = address + ', ' + location.address.state;
    if (isDataValid(location.address.country)) address = address + ', ' + location.address.country;
    if (isDataValid(location.address.postalCode)) address = address + ', ' + location.address.postalCode;
  }
  return address;
}

// sort Locations data
export function sortLocation(a, b) {
  if (a.resource.distance && b.resource.distance) {
    return a.resource.distance - b.resource.distance;
  } else {
    if (a.resource.name < b.resource.name) {
      return -1;
    }
    if (a.resource.name > b.resource.name) {
      return 1;
    }
  }
  return 0;
}

export const sortSlots = (a, b) => {
  if (a.resource.start < b.resource.start) {
    return -1;
  }
  if (a.resource.start > b.resource.start) {
    return 1;
  }
  return 0;
};

export function getLocationDistance(locationDistanceMap, location) {
  return location && locationDistanceMap && locationDistanceMap.size
    ? locationDistanceMap.get(location.resource.id)
    : null;
}

export const getLocationPhoneNumber = (locationDetail) => {
  if (!locationDetail?.resource?.telecom?.length) return null;

  const telecom = locationDetail.resource?.telecom;

  return telecom?.find((telecomType) => telecomType?.system === 'phone')?.value || null;
};

export const getBookedAppointmentData = (data) => {
  let bookedAppointmentData = {
    individuals: null,
    location: null,
    appointmentDetails: null,
    links: null,
  };
  for (let i = 0; i < data?.entry?.length; i++) {
    if (data?.entry[i]?.resource?.resourceType === i18next.t('location')) {
      bookedAppointmentData.location = data?.entry[i]?.resource;
    } else if (data?.entry[i]?.resource?.resourceType === 'Bundle') {
      if (data?.entry[i]?.resource?.id === 'Appointments') {
        bookedAppointmentData.appointmentDetails = data?.entry[i]?.resource;
      } else if (data?.entry[i]?.resource?.id === 'Patients') {
        bookedAppointmentData.individuals = data?.entry[i]?.resource;
      }
    }

    // Get calendar links
    if (data?.link) {
      bookedAppointmentData.links = data?.link;
    }
  }
  return bookedAppointmentData;
};

export const getBookedSlotDetails = (data) => {
  const { entry } = data || [];
  let slotDetails = {};

  for (let i = 0; i < entry.length; i++) {
    if (entry[i]) {
      slotDetails = {
        date: getScheduleDate(entry[i].resource?.start, DATE_FORMAT) || '',
        start: convertTimeIntoTwelveHourFormatIgnoreZone(entry[i].resource?.start, TIME_FORMAT),
        end: convertTimeIntoTwelveHourFormatIgnoreZone(entry[i].resource?.end, TIME_FORMAT),
        numberOfAppointments: entry?.length,
      };
    }
  }
  return slotDetails;
};

export const getBookedLocation = (bookedAppointmentData) => {
  return getResourceTypeData(bookedAppointmentData, 'Location');
};

export const getAppointmentStatus = (bookedAppointmentData) => {
  let appointmentStatus = null;
  let appointmentData = getResourceTypeById(bookedAppointmentData, 'Appointments');
  if (appointmentData?.resource?.entry && appointmentData?.resource?.entry.length) {
    let entry = appointmentData.resource.entry[0];
    if (entry?.resource?.status) {
      appointmentStatus = entry?.resource?.status;
    }
  }
  return appointmentStatus;
};

export const getAppointmentConfirmationStatus = (bookedAppointmentData) => {
  let appointmentStatus = null;
  if (bookedAppointmentData?.entry && bookedAppointmentData?.entry.length) {
    let appointmentData = getResourceTypeById(bookedAppointmentData.entry, 'Appointments');
    if (appointmentData?.resource?.entry && appointmentData?.resource?.entry.length) {
      let entry = appointmentData.resource.entry[0];
      if (entry?.resource?.participant && entry?.resource?.participant.length) {
        appointmentStatus = getParticipantActorStatusByReference(entry?.resource?.participant, 'Patient');
      }
    }
  }
  return appointmentStatus;
};

export const getAppointmentIds = (bookedAppointmentData) => {
  let appointmentIds = [];
  let appointmentData = getResourceTypeById(bookedAppointmentData, 'Appointments');
  if (appointmentData?.resource?.entry && appointmentData?.resource?.entry.length) {
    appointmentData.resource.entry.map((data) => {
      let id = data.resource?.id ? data.resource?.id : null;
      appointmentIds.push(id);
    });
  }
  return appointmentIds;
};

export const getAppointmentId = (bookedAppointmentData) => {
  let appointmentId = null;
  let appointmentData = getResourceTypeById(bookedAppointmentData, 'Appointments');
  if (appointmentData?.resource?.entry && appointmentData?.resource?.entry.length) {
    appointmentId = appointmentData.resource.entry[0].resource?.id
      ? appointmentData.resource.entry[0].resource?.id
      : null;
  }

  return appointmentId;
};

export const getBookedService = (bookedAppointmentData) => {
  let service = null;
  let selectedService = null;
  let serviceData = getResourceTypeById(bookedAppointmentData, 'Appointments');
  if (serviceData?.resource?.entry && serviceData?.resource?.entry.length) {
    let entry = serviceData.resource.entry[0];
    if (entry?.resource?.appointmentType?.coding && entry?.resource?.appointmentType?.coding.length) {
      service = entry?.resource?.appointmentType?.coding[0];
    }
  }
  if (service) {
    selectedService = {
      id: service.id,
      cambianReferenceId: service.code,
      cambianReferenceData: {
        code: service.code,
        display: service.display,
      },
      name: service.display,
    };
  }
  return selectedService;
};

export const getSlotData = (bookedAppointmentData) => {
  let slotData = null;
  let selectedService = null;
  let serviceData = getResourceTypeById(bookedAppointmentData, 'Appointments');
  if (serviceData?.resource?.entry && serviceData?.resource?.entry.length) {
    slotData = serviceData.resource.entry[0];
  }
  return slotData;
};

export const getScheduleDate = (startDate, format) => {
  return moment(startDate).utcOffset(startDate).format(format);
};

const getValue = (values, key) => {
  let value = '';
  if (values && values.length) {
    values.map((data) => {
      if (data.system === key) {
        value = data.value;
      }
    });
  }
  return value;
};

const getResourceTypeById = (entry, id) => {
  let resourceTypeData = null;
  if (entry && entry.length) {
    entry.map((data) => {
      if (data?.resource.id == id) {
        resourceTypeData = data;
      }
    });
  }
  return resourceTypeData;
};

const getParticipantActorStatusByReference = (participant, reference) => {
  let status = null;
  const pattern = new RegExp(`^${reference}/.*$`);
  if (participant && participant.length) {
    participant.map((data) => {
      if (pattern.test(data?.actor.reference)) {
        status = data.status;
      }
    });
  }
  return status;
};

const getResourceTypeData = (entry, resourceType) => {
  let resourceTypeData = null;
  entry.map((data) => {
    if (data?.resource?.resourceType === resourceType) {
      resourceTypeData = data;
    }
  });
  return resourceTypeData;
};

export const getActionObject = (condition) => {
  let action = {};
  if (condition && Object.keys(condition).length) {
    action.selectedAction = condition.selectedAction;
    action.redirectUrl = condition.redirectUrl;
    action.selectedTarget = condition.selectedTarget;
    action.buttonName = condition.buttonName;
    action.widgetType = condition.selectedWidgetType;
    action.selectedWidget = condition.selectedWidget;
    action.addDemographic = condition.addDemographic;
    action.showDemographic = condition.showDemographic;
  }

  return action;
};

export const checkActionCondition = (result, selectedService) => {
  let actionCondition = {
    isActionConditionMatched: false,
    action: {},
  };

  const selectedServiceCode = selectedService?.code;

  if (result?.resultResponse) {
    for (let condition of result.resultResponse) {
      if (condition?.selectedServices?.length) {
        for (let service of condition.selectedServices) {
          if (service.code === selectedServiceCode) {
            actionCondition.isActionConditionMatched = true;
            actionCondition.action = getActionObject(condition);
            break;
          }
        }
      }
    }
  }

  return actionCondition;
};

export const getNextAvailableSlot = (nextAvailableSlots, location) => {
  let nextAvlSlot = null;
  if (nextAvailableSlots && nextAvailableSlots.length > 0) {
    nextAvailableSlots.forEach((nextAvailableSlot) => {
      let locationObj = nextAvailableSlot.resource.extension.filter(
        (obj) => obj.url == 'http://cambian.com/Location/location-id',
      );
      if (locationObj.length && locationObj[0].valueString == location.resource.id) {
        if (nextAvailableSlot.resource && nextAvailableSlot.resource.start) {
          nextAvlSlot = nextAvailableSlot.resource.start;
        }
      }
    });
  }
  return nextAvlSlot;
};

export const getMaxAllowedDateForService = (selectedService, services) => {
  if (!selectedService) return null;

  let extensions;
  if (selectedService?.extension) {
    extensions = selectedService?.extension;
  } else if (services?.length) {
    extensions = services.find((service) => service.code === selectedService.code)?.extension || [];
  }

  if (Array.isArray(extensions) && extensions.length) {
    const windowValueExt =
      extensions.find((extension) => extension.url === 'http://cambian.com/AppointmentType/windowValue')?.valueString ||
      null;

    if (!windowValueExt) return null;

    const [windowValue, windowType] = windowValueExt.toLowerCase()?.split(' ') || ['', ''];

    if (windowType?.includes('day')) {
      return addDays(new Date(), Number(windowValue));
    } else if (windowType?.includes('week')) {
      return addWeeks(new Date(), Number(windowValue));
    } else if (windowType?.includes('month')) {
      return addMonths(new Date(), Number(windowValue));
    }
  }

  return null;
};

export const getAppointmentAdditionalInfo = (bookedAppointmentData) => {
  if (!bookedAppointmentData) return { additionalText: '', additionalLink: '' };

  const appointmentExtension = bookedAppointmentData?.entry?.[0]?.resource?.extension || [];
  const additionalText =
    CommonUtility.extractExtension(appointmentExtension, 'http://cambian.com/Appointment/additional-text')
      ?.valueString || '';
  const additionalLink =
    CommonUtility.extractExtension(appointmentExtension, 'http://cambian.com/Appointment/additional-url')
      ?.valueString || '';

  return { additionalText, additionalLink };
};

export const getSmartDates = (eligibilityData) => {
  const smartDates = {
    earliestDate: null,
    latestDate: null,
    message: null,
  };

  if (!eligibilityData || !eligibilityData?.entry?.length) return smartDates;

  const messageDefinitionBundle = eligibilityData?.entry?.find(
    (entry) => entry?.resource?.resourceType === 'MessageDefinition',
  )?.resource;

  const earliestDate =
    CommonUtility.extractExtension(messageDefinitionBundle?.extension, 'earliestDate')?.valueString || null;
  const latestDate =
    CommonUtility.extractExtension(messageDefinitionBundle?.extension, 'latestDate')?.valueString || null;
  const message = CommonUtility.extractExtension(messageDefinitionBundle?.extension, 'message')?.valueString || null;

  smartDates.earliestDate = earliestDate ? CommonUtility.convertStringToDate(earliestDate) : null;
  smartDates.latestDate = latestDate ? CommonUtility.convertStringToDate(latestDate) : null;
  smartDates.message = message || null;

  return smartDates;
};

export const validateSmartDates = (date) => {
  let dateStatus = '';
  if (!isValid(date)) return 'INVALID';

  if (isBefore(date, new Date())) {
    dateStatus = 'PAST';
  } else if (isAfter(date, new Date())) {
    dateStatus = 'FUTURE';
  } else if (isToday(date)) {
    dateStatus = 'PRESENT';
  }
  return dateStatus;
};
