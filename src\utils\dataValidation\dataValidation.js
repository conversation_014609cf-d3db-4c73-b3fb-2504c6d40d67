// ---------- Data validation starts-------------

const isNull = (item) => {
  return item === undefined || item === null;
};

const isStringEmpty = (str) => {
  return str.constructor === String && str.trim().length === 0;
};

const isDataEmpty = (data) => {
  if (isNull(data)) return true;
  if (isStringEmpty(data)) return true;
  if (
    (data.constructor === Object && Object.keys(data).length === 0) ||
    (data.constructor === Array && data.length === 0)
  ) {
    return true;
  }
  return false;
};

const formValid = (state, arrayOfkey) => {
  let valid = true;
  // validate the form was filled out
  for (let i = 0; i < arrayOfkey.length; i++) {
    if (isDataEmpty(state[arrayOfkey[i]])) {
      return false;
    }
  }
  return valid;
};

const isValidData = (state) => {
  let valid = false;
  Object.keys(state).forEach((key) => {
    if (!isDataEmpty(state[key])) {
      valid = true;
    }
  });
  return valid;
};

const isArrayOfObjectValid = (state) => {
  let valid = false;
  for (let i = 0; i < state.length; i++) {
    valid = isValidData(state[i]);
  }
  return valid;
};
export const isValidationDataValid = (data) => {
  const isEmptyValue = (value) => {
    if (Array.isArray(value)) {
      return value.every((item) => item === '');
    }
    return value === '';
  };
  for (const key in data) {
    if (typeof data[key] === 'object' && data[key] !== null) {
      if (!isValidationDataValid(data[key])) {
        return false;
      }
    } else if (!isEmptyValue(data[key])) {
      return false;
    }
  }
  return true;
};

const getColorOfAppearance = (colorObj) => {
  let objectOfColor = {};
  objectOfColor['backgroundColor'] = colorObj.backgroundColor ? colorObj.backgroundColor : '';
  objectOfColor['headingColor'] = colorObj.headingColor ? colorObj.headingColor : '';
  objectOfColor['mainTextColor'] = colorObj.mainTextColor ? colorObj.mainTextColor : '';
  objectOfColor['primarySelectionColor'] = colorObj.primarySelectionColor ? colorObj.primarySelectionColor : '';
  objectOfColor['secondaryTextColor'] = colorObj.secondaryTextColor ? colorObj.secondaryTextColor : '';

  return objectOfColor;
};

const getDataFromObject = (object, path) => {
  if (path.length === 1) return object[path[0]];
  else if (path.length === 0) return;
  else {
    if (object[path[0]]) return getDataFromObject(object[path[0]], path.slice(1));
    else {
      object[path[0]] = {};
      return getDataFromObject(object[path[0]], path.slice(1));
    }
  }
};

export const isDataValid = (data) => {
  let isValid = false;
  if (data !== null && data !== undefined) isValid = true;
  return isValid;
};

export function isObject(data) {
  let isValid = false;
  if (isDataValid(data)) {
    if (typeof data === 'object') {
      isValid = true;
    }
  }
  return isValid;
}
// ---------- Data validation ends-------------

export default {
  isNull,
  isStringEmpty,
  isDataEmpty,
  formValid,
  getColorOfAppearance,
  getDataFromObject,
  isValidData,
  isArrayOfObjectValid,
};
