import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  GENERATE_HTML_REPORT_AS_PDF_REQUEST,
  GENERATE_HTML_REPORT_AS_PDF_SUCCESS,
  GENERATE_HTML_REPORT_AS_PDF_ERROR,
  GENERATE_HTML_REPORT_AS_PDF_RESET,
} from '../../actions/generateHtmlReportAsPdf';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const generateHtmlReportAsPdfReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case GENERATE_HTML_REPORT_AS_PDF_REQUEST:
      return {
        ...state,
        generateHtmlReportAsPdfFetching: true,
        generateHtmlReportAsPdfSuccess: false,
        generateHtmlReportAsPdfError: false,
        generateHtmlReportAsPdfErrorData: null,
      };
    case GENERATE_HTML_REPORT_AS_PDF_SUCCESS: {
      return {
        ...state,
        generateHtmlReportAsPdfFetching: false,
        generateHtmlReportAsPdfSuccess: true,
        generateHtmlReportAsPdfError: false,
        generateHtmlReportAsPdfErrorData: null,
        generateHtmlReportAsPdfSuccessData: payload,
      };
    }
    case GENERATE_HTML_REPORT_AS_PDF_ERROR:
      return {
        ...state,
        generateHtmlReportAsPdfFetching: false,
        generateHtmlReportAsPdfSuccess: false,
        generateHtmlReportAsPdfError: true,
        generateHtmlReportAsPdfErrorData: payload,
      };
    case GENERATE_HTML_REPORT_AS_PDF_RESET:
      return {
        ...state,
        generateHtmlReportAsPdfFetching: false,
        generateHtmlReportAsPdfSuccess: false,
        generateHtmlReportAsPdfError: false,
        generateHtmlReportAsPdfErrorData: null,
        generateHtmlReportAsPdfSuccessData: null,
      };
    default:
      return state;
  }
};
