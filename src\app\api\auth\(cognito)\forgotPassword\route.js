import { AwsError, generateSecretHash, handler } from '@/containers/auth';
import { CognitoIdentityProviderClient, ForgotPasswordCommand } from '@aws-sdk/client-cognito-identity-provider';
import { NextResponse } from 'next/server';

/**
 * @param {Request} req
 * @returns {Promise<void>}
 */
const postEndpoint = async (req) => {
  const { COGNITO_INDIVIDUAL_HUMAN_REGION, COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_ID } = process.env;
  const { email } = await req.json();

  if (!email) {
    return NextResponse.json(
      {
        message: 'email must be provided',
      },
      {
        status: 400,
      },
    );
  }

  const params = {
    ClientId: COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_ID,
    SecretHash: generateSecretHash(email),
    Username: email,
  };

  const cognitoClient = new CognitoIdentityProviderClient({
    region: COGNITO_INDIVIDUAL_HUMAN_REGION,
  });

  const cognitoCommand = new ForgotPasswordCommand(params);

  try {
    const res = await cognitoClient.send(cognitoCommand);
    const status = res['$metadata'].httpStatusCode;

    const response = NextResponse.json({}, { status });

    return response;
  } catch (err) {
    const awsError = new AwsError(err);
    return awsError.toNextResponse();
  }
};

const middleware1 = (_req, next) => {
  console.log('middleware 1');
  next();
};

export const POST = handler(middleware1, postEndpoint);
