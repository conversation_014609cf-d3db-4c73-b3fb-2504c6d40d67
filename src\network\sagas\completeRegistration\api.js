/*
 *
 */
import { makeNetworkCall } from '../..';
import { PATIENT_ID, EMAIL_VERIFICATION_REQUIRED } from '../../../utils/constants';
import { BASE_URL, COMPLETE_REGISTRATION, COMPLETE_REGISTRATION_WITH_ID } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeCompleteRegistration(action) {
  const { payload = {} } = action || {};
  const { headers = {}, registrationData = {}, method = '', emailVerificationRequired } = payload || {};
  const END_POINT =
    method === 'POST'
      ? COMPLETE_REGISTRATION.replace(EMAIL_VERIFICATION_REQUIRED, emailVerificationRequired)
      : COMPLETE_REGISTRATION_WITH_ID.replace(PATIENT_ID, registrationData && registrationData.id).replace(
          EMAIL_VERIFICATION_REQUIRED,
          emailVerificationRequired,
        );
  const config = {
    method: method,
    url: BASE_URL + END_POINT,
    headers: headers,
    data: registrationData,
  };
  return makeNetworkCall(config);
}
