/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import {
  CHECK_EMAIL_REQUEST,
  CHECK_EMAIL_SUCCESS,
  CHECK_EMAIL_ERROR,
} from '../../../redux/actions/checkEmailInBooking';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { verifyEmailInBooking } from './api';
/**
 *
 * @param {*} action
 */

export function* checkEmail(action) {
  try {
    const response = yield call(verifyEmailInBooking, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: CHECK_EMAIL_SUCCESS, payload: data });
    } else {
      yield put({ type: CHECK_EMAIL_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: CHECK_EMAIL_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchCheckEmail() {
  yield takeEvery(CHECK_EMAIL_REQUEST, checkEmail);
}
