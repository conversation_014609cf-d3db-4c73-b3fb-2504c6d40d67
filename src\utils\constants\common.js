import {
  API_HEADER_AUTHORIZATION_KEY,
  API_HEADER_CAMIBIAN_AUTHORIZATION_KEY,
  API_HEADER_AUTHORIZATION_VALUE,
  API_HEADER_AUTHORIZATION_BASIC,
  API_HEADER_AUTHORIZATION_ACCESS_TOKEN_KEY,
  API_HEADER_CONTENT_TYPE,
  API_HEADER_CONTENT_TYPE_JSON_VALUE,
  API_HEADER_CONTENT_TYPE_MULTIPART_FORM_DATA,
  API_HEADER_FORM_URL_ENCODED,
} from './apiCodes';

/**
 *
 * @param {*} accessToken
 */
export function generateAPIHeader() {
  const headers = {
    [API_HEADER_CONTENT_TYPE]: API_HEADER_CONTENT_TYPE_JSON_VALUE,
  };
  return headers;
}

export function generateAPIWithCambianHeader(accessToken) {
  const headers = {
    [API_HEADER_CAMIBIAN_AUTHORIZATION_KEY]: API_HEADER_AUTHORIZATION_VALUE.replace(
      API_HEADER_AUTHORIZATION_ACCESS_TOKEN_KEY,
      accessToken,
    ),
    [API_HEADER_CONTENT_TYPE]: API_HEADER_CONTENT_TYPE_JSON_VALUE,
  };
  return headers;
}

export function generateAPIHeaderForCambianUserInfoEndPoint(accessToken) {
  const headers = {
    [API_HEADER_AUTHORIZATION_KEY]: API_HEADER_AUTHORIZATION_VALUE.replace(
      API_HEADER_AUTHORIZATION_ACCESS_TOKEN_KEY,
      accessToken,
    ),
  };
  return headers;
}
/**
 *
 * @param {*} accessToken
 */
export function generateAPIHeaderFile(accessToken) {
  const headers = {
    [API_HEADER_AUTHORIZATION_KEY]: API_HEADER_AUTHORIZATION_VALUE.replace(
      API_HEADER_AUTHORIZATION_ACCESS_TOKEN_KEY,
      accessToken,
    ),
    [API_HEADER_CONTENT_TYPE]: API_HEADER_CONTENT_TYPE_MULTIPART_FORM_DATA,
  };
  return headers;
}

/**
 *
 */
export function formUrlEncodedAPIHeader() {
  const headers = {
    [API_HEADER_CONTENT_TYPE]: API_HEADER_FORM_URL_ENCODED,
  };
  return headers;
}

export function formUrlEncodedAPIHeaderWithToken(accessToken) {
  const headers = {
    [API_HEADER_AUTHORIZATION_KEY]: API_HEADER_AUTHORIZATION_VALUE.replace(
      API_HEADER_AUTHORIZATION_ACCESS_TOKEN_KEY,
      accessToken,
    ),
    [API_HEADER_CONTENT_TYPE]: API_HEADER_FORM_URL_ENCODED,
  };
  return headers;
}

export function formUrlEncodedAPIHeaderWithBasicToken(accessToken) {
  const headers = {
    [API_HEADER_AUTHORIZATION_KEY]: API_HEADER_AUTHORIZATION_BASIC.replace(
      API_HEADER_AUTHORIZATION_ACCESS_TOKEN_KEY,
      accessToken,
    ),
    [API_HEADER_CONTENT_TYPE]: API_HEADER_FORM_URL_ENCODED,
  };
  return headers;
}

/**
 *
 */
export function generateAPIHeaderWithoutAccessToken() {
  const headers = {
    [API_HEADER_CONTENT_TYPE]: API_HEADER_CONTENT_TYPE_JSON_VALUE,
  };
  return headers;
}
