import { formUrlEncoded<PERSON>IHeader<PERSON>ith<PERSON>asicToken, getHeader } from '../../commonUtility';
import { getCheckEmailData } from '../../Demographics/demographicUtility';
import { EMAIL, INDIVIDUAL_USER_BASIC_TOKEN } from '../../../utils/constants';
import {
  BASE_URL,
  CAMBIAN_SERVICE_BASE_URL,
  CHECK_EMAIL,
  CREATE_INDIVIDUAL_USER,
  FORGOT_PASSWORD,
  GET_AUTH_TOKEN,
} from '../../../utils/constants/apiEndpoints';
import { generateAPIHeader } from '../../../utils/constants/common';
import { getCreateUserData } from '../../../utils/data';

export const fetchIndividualUserOAuthToken = (username, password) => {
  const headers = formUrlEncodedAPIHeaderWithBasicToken(INDIVIDUAL_USER_BASIC_TOKEN);
  const oAuthData = {
    grant_type: 'password',
    client_id: 'cambian-pass',
    client_secret: 'secret',
    username,
    password,
  };
  const url = CAMBIAN_SERVICE_BASE_URL + GET_AUTH_TOKEN;

  let formBody = [];
  for (let loginProperty in oAuthData) {
    let encodedKey = encodeURIComponent(loginProperty);
    let encodedValue = encodeURIComponent(oAuthData[loginProperty]);
    formBody.push(encodedKey + '=' + encodedValue);
  }

  formBody = formBody.join('&');

  const config = {
    method: 'POST',
    headers: headers,
    body: formBody,
  };

  return fetch(url, config);
};

export const fetchEmailVerify = (email) => {
  const headers = getHeader();
  const checkEmailData = getCheckEmailData('EMAIL', email, 'SignUp_Email_Verify');
  const url = 'https://devtest1.cambianservices.com' + CHECK_EMAIL;

  const config = {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(checkEmailData),
  };

  return fetch(url, config);
};

export const fetchVerifyUserSignUp = (props) => {
  const headers = generateAPIHeader();

  const data = getCreateUserData(props);
  const url = 'https://devtest1.cambianservices.com' + CREATE_INDIVIDUAL_USER;

  const config = {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(data),
  };

  return fetch(url, config);
};

export const fetchForgotPassword = (email) => {
  const headers = generateAPIHeader();
  const url = CAMBIAN_SERVICE_BASE_URL + FORGOT_PASSWORD.replace(EMAIL, email);

  const config = {
    method: 'POST',
    headers,
  };

  return fetch(url, config);
};
