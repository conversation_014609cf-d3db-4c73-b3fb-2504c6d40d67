import { call, put, takeEvery } from 'redux-saga/effects';
import {
  CREATE_INDIVIDUAL_DATA_REQUEST,
  CREATE_INDIVIDUAL_DATA_SUCCESS,
  CREATE_INDIVIDUAL_DATA_ERROR,
} from '@/redux/actions/createIndividualData';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { createIndividualData } from './api';

export function* fetchCreateIndividualData(action) {
  try {
    const response = yield call(createIndividualData, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: CREATE_INDIVIDUAL_DATA_SUCCESS, payload: data });
    } else {
      yield put({ type: CREATE_INDIVIDUAL_DATA_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: CREATE_INDIVIDUAL_DATA_ERROR, payload: error });
  }
}

export function* watchCreateIndividualData() {
  yield takeEvery(CREATE_INDIVIDUAL_DATA_REQUEST, fetchCreateIndividualData);
}
