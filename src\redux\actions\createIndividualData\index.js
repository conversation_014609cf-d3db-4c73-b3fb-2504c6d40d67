export const CREATE_INDIVIDUAL_DATA_REQUEST = 'CREATE_INDIVIDUAL_DATA_REQUEST';
export const CREATE_INDIVIDUAL_DATA_SUCCESS = 'CREATE_INDIVIDUAL_DATA_SUCCESS';
export const CREATE_INDIVIDUAL_DATA_ERROR = 'CREATE_INDIVIDUAL_DATA_ERROR';
export const CREATE_INDIVIDUAL_DATA_RESET = 'CREATE_INDIVIDUAL_DATA_RESET';

export function createIndividualDataRequest(data) {
  return {
    type: CREATE_INDIVIDUAL_DATA_REQUEST,
    payload: data,
  };
}

export function createIndividualDataSuccess(data) {
  return {
    type: CREATE_INDIVIDUAL_DATA_SUCCESS,
    payload: data,
  };
}

export function createIndividualDataError(error) {
  return {
    type: CREATE_INDIVIDUAL_DATA_ERROR,
    payload: error,
  };
}

export const resetCreateIndividualData = () => {
  return {
    type: CREATE_INDIVIDUAL_DATA_RESET,
  };
};
