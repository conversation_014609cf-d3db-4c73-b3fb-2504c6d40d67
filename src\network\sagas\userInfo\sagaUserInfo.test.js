import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/userInfo/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { fetchUserInfo } from './index';

/**
 * This function test test case for get userInfo details saga
 * Fires get userInfo success of api gives success
 * Fires get userInfo error of api fails
 */

describe('userInfo', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiFetchUserInfo = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchUserInfo(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchUserInfo, requestResult).done;

    let successResult = actions.fetchActionUserInfoSuccess(DUMMY_ITEM.data);

    expect(api.makeApiFetchUserInfo.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchUserInfo = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchUserInfo(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchUserInfo, requestResult).done;

    expect(api.makeApiFetchUserInfo.mock.calls.length).toBe(1);

    let errorResult = actions.fetchActionUserInfoError(DUMMY_ITEM.data);
    expect(dispatchedActions).toContainEqual(errorResult);
  });
  // resetUserInfo

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchUserInfo = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchUserInfo(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchUserInfo, requestResult).done;

    expect(api.makeApiFetchUserInfo.mock.calls.length).toBe(1);

    let resetResult = actions.resetUserInfo();
    const expectedAction = {
      type: actions.USER_INFO_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
