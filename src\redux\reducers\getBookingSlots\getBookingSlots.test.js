import {
  GET_BOOKING_SLOTS_SUCCESS,
  GET_BOOKING_SLOTS_REQUEST,
  GET_BOOKING_SLOTS_ERROR,
} from '../../actions/getBookingSlots';
import * as G<PERSON><PERSON>BA<PERSON> from '../globals';
import { bookingSlotsReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isBookingSlotsFetching: true,
  fetchBookingSlotsSuccess: false,
  fetchBookingSlotsError: false,
  bookingSlotsErrorData: null,
};

const successState = {
  isBookingSlotsFetching: false,
  fetchBookingSlotsSuccess: true,
  fetchBookingSlotsError: false,
  bookingSlotsErrorData: null,
  bookingSlotsSuccessData: data,
};

const errorState = {
  isBookingSlotsFetching: false,
  fetchBookingSlotsSuccess: false,
  fetchBookingSlotsError: true,
  bookingSlotsErrorData: data,
};

describe('Complete Booking  slots Reducer', () => {
  it('should return the initial state', () => {
    expect(bookingSlotsReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle GET_BOOKING_SLOTS_REQUEST', () => {
    expect(
      bookingSlotsReducer(initialState, {
        type: GET_BOOKING_SLOTS_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle GET_BOOKING_SLOTS_SUCCESS', () => {
    expect(
      bookingSlotsReducer(initialState, {
        type: GET_BOOKING_SLOTS_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle GET_BOOKING_SLOTS_ERROR', () => {
    expect(
      bookingSlotsReducer(initialState, {
        type: GET_BOOKING_SLOTS_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });
});
