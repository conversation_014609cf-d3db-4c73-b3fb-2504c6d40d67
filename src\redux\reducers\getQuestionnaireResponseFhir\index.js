import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  GET_QUESTIONNAIRE_RESPONSE_FHIR_REQUEST,
  GET_QUESTIONNAIRE_RESPONSE_FHIR_SUCCESS,
  GET_QUESTIONNAIRE_RESPONSE_FHIR_ERROR,
  GET_QUESTIONNAIRE_RESPONSE_FHIR_RESET,
  SAVE_QUESTIONNAIRE_RESPONSE_FHIR,
} from '../../actions/getQuestionnaireResponseFhir';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const getQuestionnaireResponseFhirRedux = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case GET_QUESTIONNAIRE_RESPONSE_FHIR_REQUEST:
      return {
        ...state,
        questionnaireResponseFhirFetching: true,
        questionnaireResponseFhirSuccess: false,
        questionnaireResponseFhirError: false,
        questionnaireResponseFhirErrorData: null,
      };
    case GET_QUESTIONNAIRE_RESPONSE_FHIR_SUCCESS: {
      return {
        ...state,
        questionnaireResponseFhirFetching: false,
        questionnaireResponseFhirSuccess: true,
        questionnaireResponseFhirError: false,
        questionnaireResponseFhirErrorData: null,
        questionnaireResponseFhirSuccessData: payload,
      };
    }
    case SAVE_QUESTIONNAIRE_RESPONSE_FHIR: {
      return {
        ...state,
        questionnaireResponseFhirFetching: false,
        questionnaireResponseFhirSuccess: true,
        questionnaireResponseFhirError: false,
        questionnaireResponseFhirErrorData: null,
        questionnaireResponseFhirSuccessData: payload,
      };
    }
    case GET_QUESTIONNAIRE_RESPONSE_FHIR_ERROR:
      return {
        ...state,
        questionnaireResponseFhirFetching: false,
        questionnaireResponseFhirSuccess: false,
        questionnaireResponseFhirError: true,
        questionnaireResponseFhirErrorData: payload,
      };
    case GET_QUESTIONNAIRE_RESPONSE_FHIR_RESET:
      return {
        ...state,
        questionnaireResponseFhirFetching: false,
        questionnaireResponseFhirSuccess: false,
        questionnaireResponseFhirError: false,
        questionnaireResponseFhirErrorData: null,
        questionnaireResponseFhirSuccessData: null,
      };
    default:
      return state;
  }
};
