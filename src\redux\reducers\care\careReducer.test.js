import { CARE_ERROR, CARE_REQUEST, CARE_RESET, CARE_SUCCESS } from '../../actions/care';
import * as GLOBALS from '../globals';
import { careReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isCareFetching: true,
  fetchCareSuccess: false,
  fetchCareError: false,
  careErrorData: null,
};

const errorState = {
  isCareFetching: false,
  fetchCareSuccess: false,
  fetchCareError: true,
  careErrorData: data,
};

const successState = {
  isCareFetching: false,
  fetchCareSuccess: true,
  fetchCareError: false,
  careErrorData: null,
  careSuccessData: data,
};

const resetState = {
  isCareFetching: false,
  fetchCareSuccess: false,
  fetchCareError: false,
  careErrorData: null,
};

describe('Complete Booked Details Reducer', () => {
  it('should return the initial state', () => {
    expect(careReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle CARE_REQUEST', () => {
    expect(
      careReducer(initialState, {
        type: CARE_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle CARE_SUCCESS', () => {
    expect(
      careReducer(initialState, {
        type: CARE_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle CARE_ERROR', () => {
    expect(
      careReducer(initialState, {
        type: CARE_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle CARE_RESET', () => {
    expect(
      careReducer(initialState, {
        type: CARE_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
