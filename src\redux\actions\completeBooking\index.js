/*
 *BOOKING_DETAILS action types
 */

export const COMPLETE_BOOKING_REQUEST = 'COMPLETE_BOOKING_REQUEST';
export const COMPLETE_BOOKING_SUCCESS = 'COMPLETE_BOOKING_SUCCESS';
export const COMPLETE_BOOKING_ERROR = 'COMPLETE_BOOKING_ERROR';
export const COMPLETE_BOOKING_RESET = 'COMPLETE_BOOKING_RESET';

/*
 * action creators
 */

export function completeBooking(data) {
  return {
    type: COMPLETE_BOOKING_REQUEST,
    payload: data,
  };
}

export function completeBookingActionSuccess(data) {
  return {
    type: COMPLETE_BOOKING_SUCCESS,
    payload: data,
  };
}

export function completeBookingActionError() {
  return {
    type: COMPLETE_BOOKING_ERROR,
  };
}

export const resetBooking = () => {
  return {
    type: COMPLETE_BOOKING_RESET,
  };
};
