/*
 *LOG_OUT action types
 */

export const LOG_OUT_REQUEST = 'LOG_OUT_REQUEST';
export const LOG_OUT_SUCCESS = 'LOG_OUT_SUCCESS';
export const LOG_OUT_ERROR = 'LOG_OUT_ERROR';
export const LOG_OUT_RESET = 'LOG_OUT_RESET';

/*
 * action creators
 */

export function userLoggedOut(data) {
  return {
    type: LOG_OUT_REQUEST,
    payload: data,
  };
}

export function userLoggedOutSuccess(data) {
  return {
    type: LOG_OUT_SUCCESS,
    payload: data,
  };
}

export function userLoggedOutError() {
  return {
    type: LOG_OUT_ERROR,
  };
}
