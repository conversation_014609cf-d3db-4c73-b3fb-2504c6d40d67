import { convertTimeIntoTwelveHourFormatIgnoreZone, formatDateName } from '../../utils/helpers/date-time';
import { TIME_FORMAT, DATE_FORMAT } from '../../utils/constants';

const getResourceTypeById = (entry, id) => {
  let resourceTypeData = null;
  if (entry && entry.length) {
    entry.map((data) => {
      if (data?.resource.id == id) {
        resourceTypeData = data;
      }
    });
  }
  return resourceTypeData;
};

export const getServicesFromSearchAndCreatePatient = (data) => {
  let services = [];
  const servicesCollection = getResourceTypeById(data?.entry, 'Services');

  if (servicesCollection?.resource?.entry && servicesCollection?.resource?.entry?.length) {
    servicesCollection.resource.entry.forEach((service) => {
      services = service.resource.compose.include[0].concept;
    });
  }

  return services;
};

export const getPatientDataFromSearchAndCreate = (data) => {
  const patientCollection = getResourceTypeById(data?.entry, 'Patient');

  let patientDemographic = {
    individuals: [],
    contact: {},
  };

  if (patientCollection?.resource?.entry?.length > 0) {
    for (let i = 0; i < patientCollection.resource.entry.length; i++) {
      if (patientCollection.resource.entry[i]) {
        patientDemographic.individuals.push({
          gender: patientCollection?.resource.entry[i].resource?.gender,
          dateOfBirth: patientCollection?.resource.entry[i].resource?.birthDate,
          firstName: patientCollection?.resource.entry[i].resource?.name[0]?.given[0],
          lastName: patientCollection?.resource.entry[i].resource?.name[0]?.family,
          healthCardNumber: patientCollection?.resource.entry[i].resource?.identifier
            ? patientCollection?.resource.entry[i].resource?.identifier[0]?.value
            : '',
        });
        if (patientCollection?.resource.entry[i]?.resource?.telecom) {
          patientDemographic.contact = {
            [patientCollection?.resource.entry[i].resource?.telecom[0]?.system]:
              patientCollection?.resource.entry[i]?.resource?.telecom[0]?.value,
          };
        }
      }
    }

    return patientDemographic;
  }
};

export const getVaccinationHistory = (data) => {
  const patientCollection = getResourceTypeById(data?.entry, 'Patient');
  let vaccinationHistoryCollection = [];
  let vaccineProfiles = [];

  let vaccinationHistory = [];

  if (patientCollection?.resource?.entry?.length > 0) {
    patientCollection?.resource?.entry?.forEach((item) => (vaccinationHistoryCollection = item.resource.extension));
  }

  if (vaccinationHistoryCollection?.length) {
    vaccinationHistoryCollection.forEach((history) => (vaccineProfiles = history));
  }

  if (vaccineProfiles?.extension?.length) {
    vaccineProfiles.extension.forEach((profile) => {
      vaccinationHistory.push(getDoseObject(profile));
    });
  }

  return vaccinationHistory;
};

const getDoseObject = (doseProfile) => {
  let doseObject = {};

  doseProfile?.extension?.forEach((item) => {
    if (item.url === 'lotNumber') {
      doseObject.doseName = item.valueString;
    } else if (item.url === 'dateGiven') {
      doseObject.doseDate = item.valueDate;
    } else if (item.url === 'site') {
      doseObject.doseSite = item.valueString;
    }
  });

  return doseObject;
};

const gePatientFromParticipants = (participants) => {
  let patient = {};

  participants.forEach((participant) => {
    if (participant.actor.reference.includes('Patient')) {
      patient = participant.actor;
    }
  });
  return patient;
};
const getLocationFromParticipants = (participants) => {
  let location = {};

  participants.forEach((participant) => {
    if (participant.actor.reference.includes('Location')) {
      location = participant.actor;
    }
  });
  return location;
};

export const getAppointmentsDataFromSearchAndCreatePatient = (data) => {
  const appointmentCollection = getResourceTypeById(data?.entry, 'Appointments');
  let appointmentData = [];

  if (appointmentCollection?.resource?.entry?.length > 0) {
    appointmentCollection.resource.entry.forEach((appointment) => {
      let appointmentObject = {};
      appointmentObject.appointmentType = appointment.resource.appointmentType.coding[0];
      appointmentObject.startTime = appointment.resource.start;
      appointmentObject.endTime = appointment.resource.end;
      appointmentObject.location = getLocationFromParticipants(appointment.resource.participant);
      appointmentObject.patient = gePatientFromParticipants(appointment.resource.participant);
      appointmentData.push(appointmentObject);
    });
  }

  return appointmentData;
};

export const getAppointments = (appointmentData) => {
  let appointments = [];

  if (appointmentData) {
    appointmentData.forEach((appointment) => {
      const obj = {
        service: appointment.appointmentType.display,
        date: formatDateName(appointment.startTime, DATE_FORMAT),
        time: `${convertTimeIntoTwelveHourFormatIgnoreZone(
          appointment.startTime,
          TIME_FORMAT,
        )} to ${convertTimeIntoTwelveHourFormatIgnoreZone(appointment.endTime, TIME_FORMAT)}`,
        clinic: appointment.location.display,
        clinicAddress: 'Canada',
      };
      appointments.push(obj);
    });
  }
  return appointments;
};

export const getEligibilityMessage = (data) => {
  const messageCollection = getResourceTypeById(data?.entry, 'Message');
  let messages = [];

  messageCollection?.resource?.entry?.forEach((message) => {
    let messageObject = {};

    message?.resource?.extension?.forEach((item) => {
      if (item.url === 'dose') {
        messageObject.doseType = item.valueString;
      } else if (item.url === 'tradeNames') {
        messageObject.doseName = item.valueString;
      } else if (item.url === 'eligibleDate') {
        messageObject.eligibleDate = item.valueDate;
      }
    });
    messages.push(messageObject);
  });

  return messages;
};

export const sortVaccineHistory = (a, b) => {
  return new Date(a?.doseDate) - new Date(b?.doseDate);
};
