import React, { Fragment } from 'react';
import { Grid, Box, Typography } from '@mui/material';

export const PageNotFound = () => {
  return (
    <Fragment>
      <Box
        sx={{
          display: 'flex',
          height: '100vh',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        {' '}
        <Grid
          sx={{
            borderRadius: 10,
            p: 10,
          }}
        >
          <Grid textAlign="center">
            <Typography
              color="primary"
              variant="h1"
              sx={{
                fontSize: { xs: 100, sm: 200 },
              }}
            >
              404
            </Typography>
            <Typography variant="h4" sx={{ my: 2 }} color="primary">
              Sorry!!
            </Typography>
            <Typography variant="h6" color="primary">
              The page you are trying to reach does not exist or an error occured.
            </Typography>
          </Grid>
        </Grid>
      </Box>
    </Fragment>
  );
};
