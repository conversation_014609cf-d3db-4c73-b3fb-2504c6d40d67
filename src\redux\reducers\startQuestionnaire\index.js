import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  START_QUESTIONNAIRE_REQUEST,
  START_QUESTIONNAIRE_SUCCESS,
  START_QUESTIONNAIRE_ERROR,
  START_QUESTIONNAIRE_RESET,
} from '../../actions/startQuestionnaire';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const startQuestionnaireReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;

  switch (type) {
    case START_QUESTIONNAIRE_REQUEST:
      return {
        ...state,
        isStartQuestionnaireFetching: true,
        isStartQuestionnaireSuccess: false,
        startQuestionnaireSuccessData: false,
        isStartQuestionnaireError: null,
      };
    case START_QUESTIONNAIRE_SUCCESS: {
      return {
        ...state,
        isStartQuestionnaireFetching: false,
        isStartQuestionnaireSuccess: true,
        startQuestionnaireSuccessData: payload,
        isStartQuestionnaireError: false,
        startQuestionnaireErrorData: null,
      };
    }
    case START_QUESTIONNAIRE_ERROR:
      return {
        ...state,
        isStartQuestionnaireFetching: false,
        isStartQuestionnaireSuccess: false,
        isStartQuestionnaireError: true,
        startQuestionnaireErrorData: payload,
      };
    case START_QUESTIONNAIRE_RESET:
      return {
        ...state,
        isStartQuestionnaireFetching: false,
        isStartQuestionnaireSuccess: false,
        startQuestionnaireSuccessData: null,
        isStartQuestionnaireError: false,
        startQuestionnaireErrorData: null,
      };
    default:
      return state;
  }
};
