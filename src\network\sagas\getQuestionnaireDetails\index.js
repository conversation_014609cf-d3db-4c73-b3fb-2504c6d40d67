/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  GET_QUESTIONNAIRE_ERROR,
  GET_QUESTIONNAIRE_SUCCESS,
  GET_QUESTIONNAIRE_REQUEST,
} from '../../../redux/actions/getQuestionnaireDetails';

import { makeApiFetchQuestionnaireQuestions } from './api';

/**
 *
 * @param {*} action
 */

export function* fetchQuestionnaireQuestions(action) {
  try {
    const response = yield call(makeApiFetchQuestionnaireQuestions, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: GET_QUESTIONNAIRE_SUCCESS, payload: data });
    } else {
      yield put({ type: GET_QUESTIONNAIRE_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: GET_QUESTIONNAIRE_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchQuestionnaireQuestions() {
  yield takeEvery(GET_QUESTIONNAIRE_REQUEST, fetchQuestionnaireQuestions);
}
