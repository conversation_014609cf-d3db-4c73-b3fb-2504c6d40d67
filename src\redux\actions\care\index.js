/*
 * CARE action types
 */

export const CARE_REQUEST = 'CARE_REQUEST';
export const CARE_SUCCESS = 'CARE_SUCCESS';
export const CARE_ERROR = 'CARE_ERROR';
export const CARE_RESET = 'CARE_RESET';

/*
 * action creators
 */

export function fetchCare(data) {
  return {
    type: CARE_REQUEST,
    payload: { data },
  };
}

export function fetchCareSuccess(data) {
  return {
    type: CARE_SUCCESS,
    payload: data,
  };
}

export function fetchCareError() {
  return {
    type: CARE_ERROR,
  };
}

export const resetCare = () => {
  return {
    type: CARE_RESET,
  };
};
