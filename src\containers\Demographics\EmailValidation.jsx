import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { validateEmailInBooking } from '../../redux/actions/validateEmailInBooking';
import * as QuestionnaireUtility from '../QuestionnaireWidget/questionnaireUtility';
import * as CommonUtility from '../commonUtility';
import * as DemographicUtility from './demographicUtility';
import { Box, Button, Grid, Paper, Link, TextField, Typography, Stack } from '@mui/material';
import { actions, otpContactType } from '../commonConstants';
import { Loader } from '../../components';
import useNotification from '../../hooks/useNotification';
import { useTranslation } from 'react-i18next';

export const EmailValidation = (props) => {
  const { handleEmailValidationCallback, handleResendCodeCallback, demographic, isClientSummary, headerSummary } =
    props;
  const dispatch = useDispatch();
  const [otp, setOtp] = useState(null);
  const [msg, sendNotification] = useNotification();

  const { t } = useTranslation();

  const {
    isValidateEmailInBookingFetching,
    validateEmailInBookingSuccess,
    validateEmailInBookingSuccessData,
    validateEmailInBookingError,
    validateEmailInBookingErrorData,
  } = useSelector((state) => state.validateEmailInBookingReducer);

  useEffect(() => {
    if (validateEmailInBookingSuccess && validateEmailInBookingSuccessData) {
      handleEmailValidationCallback(actions.NEXT);
    } else if (validateEmailInBookingError && validateEmailInBookingErrorData) {
      if (validateEmailInBookingErrorData && validateEmailInBookingErrorData.explanation === 'Invalid OTP value') {
        sendNotification({ msg: t('otpError'), variant: 'error' });
      } else if (validateEmailInBookingErrorData && validateEmailInBookingErrorData.explanation === 'OTP has expired') {
        sendNotification({ msg: t('otpError'), variant: 'error' });
      } else if (validateEmailInBookingErrorData?.status === 401) {
        sendNotification({ msg: t('existingAccountError'), variant: 'error' });
      } else {
        sendNotification({ msg: t('apiError'), variant: 'error' });
      }
    }
  }, [
    validateEmailInBookingSuccess,
    validateEmailInBookingSuccessData,
    validateEmailInBookingError,
    validateEmailInBookingErrorData,
  ]);

  const resendCode = () => {
    handleResendCodeCallback(demographic);
  };

  const handleEdit = () => {
    QuestionnaireUtility.resetAllAPIReducers(dispatch);
    handleEmailValidationCallback(actions.PREVIOUS);
  };

  const validateEmail = () => {
    const headers = CommonUtility.getHeader();
    const contactType =
      demographic?.preferredContactMethod === 'Email' ? otpContactType.EMAIL : otpContactType.CELL_PHONE;
    const contactInfo =
      demographic?.preferredContactMethod === 'Email' ? demographic?.contact?.email : demographic?.contact?.phone;

    let data = DemographicUtility.getValidateEmailCodeData(contactType, contactInfo, otp);
    dispatch(validateEmailInBooking({ headers, validateEmailData: data }));
  };

  const handleOtp = (event) => {
    setOtp(event.target.value.trim());
  };

  return (
    <>
      <Loader active={isValidateEmailInBookingFetching} />
      <Paper sx={{ p: 4, pt: 0, mt: 2 }}>
        {!headerSummary && (
          <>
            <Grid container alignItems="center" sx={{ px: 2, pt: 2, mb: 3 }}>
              <Grid item md={2}>
                <Typography>{t('individual')}</Typography>
              </Grid>
              <Grid item md={8}>
                <Typography>
                  {demographic.individuals[0].firstName} {demographic.individuals[0].lastName}
                </Typography>
              </Grid>
              <Grid item md={2} sx={{ textAlign: 'right' }}>
                {!isClientSummary && <Button onClick={() => handleEdit()}>{t('edit')}</Button>}
              </Grid>
            </Grid>
            <hr />
          </>
        )}
        <Typography variant="h5" sx={{ my: 2 }}>
          {t('confirmationNeeded')}
        </Typography>
        <Paper>
          <Grid container alignItems="center" sx={{ py: 1, bgcolor: '#f0f0f0' }}>
            <Grid item xs={11} sx={{ pl: 1 }}>
              <Typography variant="h6">{t('verificationCode')}</Typography>
            </Grid>
          </Grid>
          <Box sx={{ mt: 5, pb: 2, px: 3 }}>
            <Stack direction="row">
              <Typography variant="body1">
                {t('VerificationCodeSentTo')}
                {demographic?.preferredContactMethod === 'Email' ? 'email address' : 'phone number'}:{' '}
                <strong>
                  {demographic?.preferredContactMethod === 'Email'
                    ? demographic.contact.email
                    : demographic?.contact?.phone}
                </strong>
              </Typography>
            </Stack>
            {demographic?.preferredContactMethod === 'Email' && (
              <Typography variant="body1">{t('otpValidationSubHeadingEmail')}</Typography>
            )}
            <Typography variant="body1" sx={{ py: 2 }}>
              {t('didNotReceiveOTP')}&nbsp;
              <Link variant="body1" underline="none" sx={{ cursor: 'pointer ' }} onClick={() => resendCode()}>
                {t('resendCode')}
              </Link>{' '}
              {!isClientSummary && (
                <>
                  or&nbsp;
                  <Link variant="body1" underline="none" sx={{ cursor: 'pointer ' }} onClick={() => handleEdit()}>
                    {t('goBack')}
                    &nbsp;
                  </Link>
                  {demographic?.preferredContactMethod === 'Email' ? t('toEnterNewEmail') : t('toEnterNewPhone')}
                </>
              )}
            </Typography>
            <Grid container>
              <Grid item xs={12} sm={5.5}>
                <TextField fullWidth label="CODE" value={otp} onChange={handleOtp} required />
              </Grid>
            </Grid>
            <Grid container sx={{ textAlign: 'center' }}>
              <Grid item xs={12} sx={{ mt: 2 }}>
                <Stack direction="row" justifyContent="center" spacing={2} alignItems="center">
                  {isClientSummary && (
                    <Button variant="outlined" onClick={() => handleEdit()}>
                      {t('back')}
                    </Button>
                  )}
                  <Button variant="contained" disabled={otp === null || otp === ''} onClick={() => validateEmail()}>
                    {t('submit')}
                  </Button>
                </Stack>
              </Grid>
            </Grid>
          </Box>
        </Paper>
      </Paper>
    </>
  );
};
