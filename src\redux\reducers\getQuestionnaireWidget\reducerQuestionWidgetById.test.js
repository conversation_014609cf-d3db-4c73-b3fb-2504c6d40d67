import {
  QUESTION_WIDGET_DETAILS_REQUEST,
  QUESTION_WIDGET_DETAILS_SUCCESS,
  QUESTION_WIDGET_DETAILS_ERROR,
} from '../../actions/getQuestionnaireWidget';
import * as G<PERSON><PERSON><PERSON><PERSON> from '../globals';
import { getQuestionWidgetDetailsReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isQuestionWidgetDetailsFetching: true,
  isQuestionWidgetDetailsSuccess: false,
  isQuestionWidgetDetailsError: false,
  QuestionWidgetDetailsErrorData: null,
};

const successState = {
  isQuestionWidgetDetailsFetching: false,
  isQuestionWidgetDetailsSuccess: true,
  isQuestionWidgetDetailsError: false,
  QuestionWidgetDetailsErrorData: null,
  QuestionWidgetDetailsSuccessData: data,
};

const errorState = {
  isQuestionWidgetDetailsFetching: false,
  isQuestionWidgetDetailsSuccess: false,
  isQuestionWidgetDetailsError: true,
  QuestionWidgetDetailsErrorData: data,
};

describe('Complete Booking  slots Reducer', () => {
  it('should return the initial state', () => {
    expect(getQuestionWidgetDetailsReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle QUESTION_WIDGET_DETAILS_REQUEST', () => {
    expect(
      getQuestionWidgetDetailsReducer(initialState, {
        type: QUESTION_WIDGET_DETAILS_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle QUESTION_WIDGET_DETAILS_SUCCESS', () => {
    expect(
      getQuestionWidgetDetailsReducer(initialState, {
        type: QUESTION_WIDGET_DETAILS_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle QUESTION_WIDGET_DETAILS_ERROR', () => {
    expect(
      getQuestionWidgetDetailsReducer(initialState, {
        type: QUESTION_WIDGET_DETAILS_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });
});
