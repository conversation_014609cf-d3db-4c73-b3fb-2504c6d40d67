import React from 'react';
import { Modal, Box, Typography, Button } from '@mui/material';
import { useTranslation } from 'react-i18next';

export const ActionModal = (props) => {
  const { open, handleClose, handleAction, headerText, messageText, cancelText, actionText } = props;
  const { t } = useTranslation();

  return (
    <Modal open={open} onClose={handleClose} aria-labelledby="modal-title" aria-describedby="modal-description">
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 500,
          bgcolor: 'background.paper',
          boxShadow: 24,
          p: 4,
          borderRadius: 1,
        }}
      >
        <Typography id="modal-title" variant="h6" component="h2">
          {t(headerText)}
        </Typography>
        <Typography id="modal-description" sx={{ mt: 1 }}>
          {t(messageText)}
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
          <Button variant="outlined" onClick={handleClose} sx={{ mr: 2 }}>
            {t(cancelText)}
          </Button>
          <Button variant="contained" color="error" onClick={handleAction}>
            {t(actionText)}
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};
