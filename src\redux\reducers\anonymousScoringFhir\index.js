/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import {
  ANONYMOUS_SCORING_FHIR_REQUEST,
  ANONYMOUS_SCORING_FHIR_SUCCESS,
  ANONYMOUS_SCORING_FHIR_ERROR,
  ANONYMOUS_SCORING_FHIR_RESET,
} from '../../actions/anonymousScoringFhir';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const anonymousScoringFhirReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;

  switch (type) {
    case ANONYMOUS_SCORING_FHIR_REQUEST:
      return {
        ...state,
        anonymousScoringFetching: true,
        anonymousScoringFhirSuccess: false,
        anonymousScoringFhirError: false,
        anonymousScoringFhirErrorData: null,
      };
    case ANONYMOUS_SCORING_FHIR_SUCCESS: {
      return {
        ...state,
        anonymousScoringFetching: false,
        anonymousScoringFhirSuccess: true,
        anonymousScoringFhirError: false,
        anonymousScoringFhirErrorData: null,
        anonymousScoringFhirSuccessData: payload,
      };
    }
    case ANONYMOUS_SCORING_FHIR_ERROR:
      return {
        ...state,
        anonymousScoringFetching: false,
        anonymousScoringFhirSuccess: false,
        anonymousScoringFhirError: true,
        anonymousScoringFhirErrorData: payload,
      };
    case ANONYMOUS_SCORING_FHIR_RESET:
      return {
        ...state,
        anonymousScoringFetching: false,
        anonymousScoringFhirSuccess: false,
        anonymousScoringFhirError: false,
        anonymousScoringFhirErrorData: null,
        anonymousScoringFhirSuccessData: null,
      };
    default:
      return state;
  }
};
