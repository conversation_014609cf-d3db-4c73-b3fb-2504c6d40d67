/*
 *CONFIRM_APPOINTMENT action types
 */

export const CONFIRM_APPOINTMENT_REQUEST = 'CONFIRM_APPOINTMENT_REQUEST';
export const CONFIRM_APPOINTMENT_SUCCESS = 'CONFIRM_APPOINTMENT_SUCCESS';
export const CONFIRM_APPOINTMENT_ERROR = 'CONFIRM_APPOINTMENT_ERROR';
export const CONFIRM_APPOINTMENT_RESET = 'CONFIRM_APPOINTMENT_RESET';

/*
 * action creators
 */

export function confirmAppointmentDetails(data) {
  return {
    type: CONFIRM_APPOINTMENT_REQUEST,
    payload: data,
  };
}

export function confirmAppointmentActionSuccess(data) {
  return {
    type: CONFIRM_APPOINTMENT_SUCCESS,
    payload: data,
  };
}

export function confirmAppointmentActionError() {
  return {
    type: CONFIRM_APPOINTMENT_ERROR,
  };
}

export const resetConfirmAppointment = () => {
  return {
    type: CONFIRM_APPOINTMENT_RESET,
  };
};
