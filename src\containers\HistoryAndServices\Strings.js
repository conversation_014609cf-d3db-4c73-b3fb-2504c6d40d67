import LocalizedStrings from 'react-localization';

/*
 *
 */

const localizationData = {
  en: {
    personalDetails: 'Personal Details',
    vaccinationHistory: 'Vaccination History',
    upcomingAppointments: 'Upcoming Appointments',
    dose: 'Dose',
    select: 'select',
    edit: 'Edit',
    service: 'Service',
    serviceSuggestionText:
      'Based on your age and immunization history, you are eligible to receive one of the following options below:',
    reschedule: 'Reschedule',
    cancel: 'Cancel',
  },

  /* 
	De
	*/
  de: {
    personalDetails: 'Personal Details',
    vaccinationHistory: 'Vaccination History',
    upcomingAppointments: 'Upcoming Appointments',
    dose: 'Dose',
    select: 'select',
    edit: 'Edit',
    service: 'Service',
    serviceSuggestionText:
      'Based on your age and immunization history, you are eligible to receive one of the following options below:',
    reschedule: 'Reschedule',
    cancel: 'Cancel',
  },
};

const strings = new LocalizedStrings(localizationData);

export default strings;
