/**
 * API route to proxy iCalendar requests to the external API
 * This route extracts organization ID and appointment ID from the URL
 * and forwards the request to the external API
 */

export async function GET(request, { params }) {
  const { organizationId, appointmentId } = params;

  // const calendarAPI = `https://2a4aos0n07.execute-api.ca-central-1.amazonaws.com/dev/organizations/${organizationId}/appointments/${appointmentId}/icalendar`;
  const calendarAPI = `${process.env.NEXT_PUBLIC_BOOKING_SERVICES_BASE_URL}/organizations/${organizationId}/appointments/${appointmentId}/icalendar`;

  try {
    const response = await fetch(calendarAPI);

    if (!response.ok) {
      throw new Error(`External API returned ${response.status}`);
    }

    const data = await response.arrayBuffer();

    const contentType = response.headers.get('content-type') || 'text/calendar';

    const contentDisposition = response.headers.get('content-disposition');

    const headers = new Headers();
    headers.set('content-type', contentType);

    if (contentDisposition) {
      headers.set('content-disposition', contentDisposition);
    } else {
      headers.set('content-disposition', `attachment; filename="appointment.ics"`);
    }

    return new Response(data, {
      status: response.status,
      headers,
    });
  } catch (error) {
    console.error('Error proxying to external API:', error);

    // Return an error response
    return new Response(JSON.stringify({ error: 'Failed to fetch calendar data' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
