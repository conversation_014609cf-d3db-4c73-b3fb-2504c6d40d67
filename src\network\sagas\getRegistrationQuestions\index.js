/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  GET_REGISTRATION_QUESTIONS_ERROR,
  GET_REGISTRATION_QUESTIONS_SUCCESS,
  GET_REGISTRATION_QUESTIONS_REQUEST,
} from '../../../redux/actions/getRegistrationQuestions';

import { makeApiFetchRegistrationQuestions } from './api';
/**
 *
 * @param {*} action
 */
/**
 

/**
 *
 * @param {*} action
 */

export function* fetchRegistrationQuestions(action) {
  try {
    const response = yield call(makeApiFetchRegistrationQuestions, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: GET_REGISTRATION_QUESTIONS_SUCCESS, payload: data });
    } else {
      yield put({ type: GET_REGISTRATION_QUESTIONS_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: GET_REGISTRATION_QUESTIONS_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchRegistrationQuestions() {
  yield takeEvery(GET_REGISTRATION_QUESTIONS_REQUEST, fetchRegistrationQuestions);
}
