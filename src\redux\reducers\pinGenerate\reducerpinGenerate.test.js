import {
  PIN_GENERATE_REQUEST,
  PIN_GENERATE_SUCCESS,
  PIN_GENERATE_ERROR,
  PIN_GENERATE_RESET,
} from '../../actions/pinGenerate';
import * as GLOBALS from '../globals';
import { pinGenerateReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  pinGenerateFetching: true,
  pinGenerateSuccess: false,
  pinGenerateError: false,
  pinGenerateErrorData: null,
};

const successState = {
  pinGenerateFetching: false,
  pinGenerateSuccess: true,
  pinGenerateError: false,
  pinGenerateErrorData: null,
  pinGenerateSuccessData: data,
};

const errorState = {
  pinGenerateFetching: false,
  pinGenerateSuccess: false,
  pinGenerateError: true,
  pinGenerateErrorData: data,
};

const resetState = {
  pinGenerateFetching: false,
  pinGenerateSuccess: false,
  pinGenerateError: false,
  pinGenerateErrorData: null,
  pinGenerateSuccessData: null,
};

describe('Oauth Booking Details Reducer', () => {
  it('should return the initial state', () => {
    expect(pinGenerateReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle PIN_GENERATE_REQUEST', () => {
    expect(
      pinGenerateReducer(initialState, {
        type: PIN_GENERATE_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle PIN_GENERATE_SUCCESS', () => {
    expect(
      pinGenerateReducer(initialState, {
        type: PIN_GENERATE_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle PIN_GENERATE_ERROR', () => {
    expect(
      pinGenerateReducer(initialState, {
        type: PIN_GENERATE_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle PIN_GENERATE_RESET', () => {
    expect(
      pinGenerateReducer(initialState, {
        type: PIN_GENERATE_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
