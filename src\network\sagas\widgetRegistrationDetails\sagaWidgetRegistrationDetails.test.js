import { runSaga } from 'redux-saga';
import * as api from './api';
import { fetchWidgetRegistrationDetails } from './index';

import * as actions from '../../../redux/actions/widgetRegistrationDetails/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

/**
 * This function test test case for get widgetRegistrationDetails details saga
 * Fires get widgetRegistrationDetails success of api gives success
 * Fires get widgetRegistrationDetails error of api fails
 */

describe('widgetRegistrationDetails', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiFetchWidgetRegistrationDetails = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchWidgetRegistrationDetails(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchWidgetRegistrationDetails, requestResult).done;

    let successResult = actions.getWidgetRegistrationDetailsSuccess(DUMMY_ITEM.data);

    expect(api.makeApiFetchWidgetRegistrationDetails.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchWidgetRegistrationDetails = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchWidgetRegistrationDetails(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchWidgetRegistrationDetails, requestResult).done;

    expect(api.makeApiFetchWidgetRegistrationDetails.mock.calls.length).toBe(1);

    let errorResult = actions.getWidgetRegistrationDetailsError();
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch reset action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchWidgetRegistrationDetails = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 2 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchWidgetRegistrationDetails(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchWidgetRegistrationDetails, requestResult).done;

    expect(api.makeApiFetchWidgetRegistrationDetails.mock.calls.length).toBe(1);
    let resetResult = actions.resetWidgetRegistrationDetails();
    const expectedAction = {
      type: actions.WIDGET_REGISTRATION_DETAILS_RESET,
    };
    expect(resetResult).toEqual(expectedAction);
  });
});
