/*
 * POST Questions action types
 */

export const POST_REGISTRATION_QUESTIONS_REQUEST = 'POST_REGISTRATION_QUESTIONS_REQUEST';
export const POST_REGISTRATION_QUESTIONS_SUCCESS = 'POST_REGISTRATION_QUESTIONS_SUCCESS';
export const POST_REGISTRATION_QUESTIONS_ERROR = 'POST_REGISTRATION_QUESTIONS_ERROR';
export const POST_REGISTRATION_QUESTIONS_RESET = 'POST_REGISTRATION_QUESTIONS_RESET';
/*
 * action creators
 */

export function fetchSubmitAnswers(data) {
  return {
    type: POST_REGISTRATION_QUESTIONS_REQUEST,
    payload: data,
  };
}

export function fetchSubmitAnswersSuccess(data) {
  return {
    type: POST_REGISTRATION_QUESTIONS_SUCCESS,
    payload: data,
  };
}

export function fetchSubmitAnswersError() {
  return {
    type: POST_REGISTRATION_QUESTIONS_ERROR,
  };
}

export function fetchSubmitAnswersReset() {
  return {
    type: POST_REGISTRATION_QUESTIONS_RESET,
  };
}
