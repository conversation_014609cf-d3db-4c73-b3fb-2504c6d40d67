import { call, put, takeEvery } from 'redux-saga/effects';
import {
  SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_REQUEST,
  SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_SUCCESS,
  SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_ERROR,
} from '../../../redux/actions/saveQuestionnaireResponseIndividual';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { makeApiSaveQuestionnaireResponseIndividual } from './api';

export function* saveQuestionnaireResponseIndividual(action) {
  try {
    const response = yield call(makeApiSaveQuestionnaireResponseIndividual, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_SUCCESS, payload: data });
    } else {
      yield put({ type: SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_ERROR });
  }
}

export function* watchSaveQuestionnaireResponseIndividual() {
  yield takeEvery(SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_REQUEST, saveQuestionnaireResponseIndividual);
}
