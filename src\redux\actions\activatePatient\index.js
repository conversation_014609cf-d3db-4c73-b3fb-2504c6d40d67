/*
 * ACTIVATE_PATIENT action types
 */

export const ACTIVATE_PATIENT_REQUEST = 'ACTIVATE_PATIENT_REQUEST';
export const ACTIVATE_PATIENT_SUCCESS = 'ACTIVATE_PATIENT_SUCCESS';
export const ACTIVATE_PATIENT_ERROR = 'ACTIVATE_PATIENT_ERROR';
export const ACTIVATE_PATIENT_RESET = 'ACTIVATE_PATIENT_RESET';

/*
 * action creators
 */
export function activatePatient(data) {
  return {
    type: ACTIVATE_PATIENT_REQUEST,
    payload: data,
  };
}

export function activateActionPatientSuccess(data) {
  return {
    type: ACTIVATE_PATIENT_SUCCESS,
    payload: data,
  };
}

export function activateActionPatientError() {
  return {
    type: ACTIVATE_PATIENT_ERROR,
  };
}

export const resetActivatePatient = () => {
  return {
    type: ACTIVATE_PATIENT_RESET,
  };
};
