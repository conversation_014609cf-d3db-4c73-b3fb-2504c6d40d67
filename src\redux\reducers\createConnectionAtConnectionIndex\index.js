import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  CREATE_CONNECTION_AT_CONNECTION_INDEX_ERROR,
  CREATE_CONNECTION_AT_CONNECTION_INDEX_REQUEST,
  CREATE_CONNECTION_AT_CONNECTION_INDEX_RESET,
  CREATE_CONNECTION_AT_CONNECTION_INDEX_SUCCESS,
} from '@/redux/actions/createConnectionAtConnectionIndex';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const createConnectionAtConnectionIndexReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case CREATE_CONNECTION_AT_CONNECTION_INDEX_REQUEST:
      return {
        ...state,
        isCreateConnectionAtConnectionIndexFetching: true,
        createConnectionAtConnectionIndexSuccess: false,
        createConnectionAtConnectionIndexError: false,
        createConnectionAtConnectionIndexErrorData: null,
      };
    case CREATE_CONNECTION_AT_CONNECTION_INDEX_SUCCESS: {
      return {
        ...state,
        isCreateConnectionAtConnectionIndexFetching: false,
        createConnectionAtConnectionIndexSuccess: true,
        createConnectionAtConnectionIndexError: false,
        createConnectionAtConnectionIndexErrorData: null,
        createConnectionAtConnectionIndexSuccessData: payload,
      };
    }
    case CREATE_CONNECTION_AT_CONNECTION_INDEX_ERROR:
      return {
        ...state,
        isCreateConnectionAtConnectionIndexFetching: false,
        createConnectionAtConnectionIndexSuccess: false,
        createConnectionAtConnectionIndexSuccessData: null,
        createConnectionAtConnectionIndexError: true,
        createConnectionAtConnectionIndexErrorData: payload,
      };
    case CREATE_CONNECTION_AT_CONNECTION_INDEX_RESET:
      return {
        ...state,
        isCreateConnectionAtConnectionIndexFetching: undefined,
        createConnectionAtConnectionIndexSuccess: undefined,
        createConnectionAtConnectionIndexSuccessData: undefined,
        createConnectionAtConnectionIndexError: undefined,
        createConnectionAtConnectionIndexErrorData: undefined,
      };
    default:
      return state;
  }
};
