import { ADD_DATA, CLEAR_DATA, DELETE_DATA, REFRESH_DATA } from '../../actions/storageData';

export const initialState = {
  fieldData: false,
  inValidate: false,
};
export const storageReducer = (state = initialState, action = {}) => {
  let key = '';
  switch (action.type) {
    case ADD_DATA:
      return {
        ...state,
        ...action.payload,
        fieldData: true,
        inValidate: false,
      };
    case DELETE_DATA:
      key = action?.payload?.key;
      delete state['key'];
      delete state[key];
      return {
        ...state,
        fieldData: true,
      };
    case CLEAR_DATA:
      return {
        fieldData: false,
      };
    case REFRESH_DATA:
      return {
        ...state,
        inValidate: true,
      };
    default:
      return state;
  }
};
