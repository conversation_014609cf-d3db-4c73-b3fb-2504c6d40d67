export const FORGOT_PASSWORD_REQUEST = 'FORGOT_PASSWORD_REQUEST';
export const FORGOT_PASSWORD_SUCCESS = 'FORGOT_PASSWORD_SUCCESS';
export const FORGOT_PASSWORD_ERROR = 'FORGOT_PASSWORD_ERROR';
export const FORGOT_PASSWORD_RESET = 'RESET_PASSWORD_RESET';

/*
 * action creators
 */

export function forgotPassword(data) {
  return {
    type: FORGOT_PASSWORD_REQUEST,
    payload: data,
  };
}

export function forgotPasswordSuccess(data) {
  return {
    type: FORGOT_PASSWORD_SUCCESS,
    payload: data,
  };
}

export function forgotPasswordError() {
  return {
    type: FORGOT_PASSWORD_ERROR,
  };
}

export function resetDownloadReport() {
  return {
    type: FORGOT_PASSWORD_RESET,
  };
}
