/*
 * OAUTH action types
 */

export const OAUTH_REQUEST = 'OAUTH_REQUEST';
export const OAUTH_SUCCESS = 'OAUTH_SUCCESS';
export const OAUTH_ERROR = 'OAUTH_ERROR';
export const OAUTH_RESET = 'OAUTH_RESET';

/*
 * action creators
 */

export function fetchToken(data) {
  return {
    type: OAUTH_REQUEST,
    payload: data,
  };
}

export function fetchTokenSuccess(data) {
  return {
    type: OAUTH_SUCCESS,
    payload: data,
  };
}

export function fetchTokenError() {
  return {
    type: OAUTH_ERROR,
  };
}

export const resetToken = () => {
  return {
    type: OAUTH_RESET,
  };
};
