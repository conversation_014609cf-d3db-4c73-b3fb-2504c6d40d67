/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import {
  GET_BOOKING_SLOTS_ERROR,
  GET_BOOKING_SLOTS_REQUEST,
  GET_BOOKING_SLOTS_SUCCESS,
} from '../../../redux/actions/getBookingSlots';

import { makeApiFetchBookingSlots } from './api';
/**
 *
 * @param {*} action
 */
/**
  
/**
 *
 * @param {*} action
 */

export function* fetchBookingSlots(action) {
  try {
    const response = yield call(makeApiFetchBookingSlots, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: GET_BOOKING_SLOTS_SUCCESS, payload: data });
    } else {
      yield put({ type: GET_BOOKING_SLOTS_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: GET_BOOKING_SLOTS_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchBookingSlots() {
  yield takeEvery(GET_BOOKING_SLOTS_REQUEST, fetchBookingSlots);
}
