export const CREATE_CONNECTION_AT_CONNECTION_INDEX_REQUEST = 'CREATE_CONNECTION_AT_CONNECTION_INDEX_REQUEST';
export const CREATE_CONNECTION_AT_CONNECTION_INDEX_SUCCESS = 'CREATE_CONNECTION_AT_CONNECTION_INDEX_SUCCESS';
export const CREATE_CONNECTION_AT_CONNECTION_INDEX_ERROR = 'CREATE_CONNECTION_AT_CONNECTION_INDEX_ERROR';
export const CREATE_CONNECTION_AT_CONNECTION_INDEX_RESET = 'CREATE_CONNECTION_AT_CONNECTION_INDEX_RESET';

export function createConnectionAtConnectionIndex(data) {
  return {
    type: CREATE_CONNECTION_AT_CONNECTION_INDEX_REQUEST,
    payload: data,
  };
}

export function createConnectionAtConnectionIndexSuccess(data) {
  return {
    type: CREATE_CONNECTION_AT_CONNECTION_INDEX_SUCCESS,
    payload: data,
  };
}

export function createConnectionAtConnectionIndexError(data) {
  return {
    type: CREATE_CONNECTION_AT_CONNECTION_INDEX_ERROR,
    payload: data,
  };
}

export const resetCreateConnectionAtConnectionIndex = () => {
  return {
    type: CREATE_CONNECTION_AT_CONNECTION_INDEX_RESET,
  };
};
