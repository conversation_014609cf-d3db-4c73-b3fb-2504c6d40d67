/*
 *
 */
import { makeNetworkCall } from '../..';
import { PATIENT_ID } from '../../../utils/constants';
import { ACTIVATE_PATIENT, BASE_URL } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiActivatePatient(action) {
  const { payload = {} } = action || {};
  const { headers = {}, patientId, reqData } = payload || {};
  const mainUrl = BASE_URL + ACTIVATE_PATIENT.replace(PATIENT_ID, patientId);
  const config = {
    method: 'PUT',
    url: mainUrl,
    headers: headers,
    data: reqData,
  };
  return makeNetworkCall(config);
}
