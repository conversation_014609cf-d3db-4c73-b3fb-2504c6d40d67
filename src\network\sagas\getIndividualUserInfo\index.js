/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import {
  INDIVIDUAL_USER_INFO_ERROR,
  INDIVIDUAL_USER_INFO_REQUEST,
  INDIVIDUAL_USER_INFO_SUCCESS,
} from '../../../redux/actions/getIndividualUserInfo';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiFetchIndividualUserInfo } from './api';

/**
 *
 * @param {*} action
 */

export function* fetchIndividualUserInfo(action) {
  try {
    const response = yield call(makeApiFetchIndividualUserInfo, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: INDIVIDUAL_USER_INFO_SUCCESS, payload: data });
    } else {
      yield put({ type: INDIVIDUAL_USER_INFO_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: INDIVIDUAL_USER_INFO_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchIndividualUserInfo() {
  yield takeEvery(INDIVIDUAL_USER_INFO_REQUEST, fetchIndividualUserInfo);
}
