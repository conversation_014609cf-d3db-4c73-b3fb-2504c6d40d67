export const ADD_DATA = 'ADD_DATA';
export const CLEAR_DATA = 'CLEAR_DATA';
export const DELETE_DATA = 'DELETE_DATA';
export const REFRESH_DATA = 'REFRESH_DATA';

const addData = (fieldName, value) => {
  return {
    type: ADD_DATA,
    payload: {
      [fieldName]: value,
    },
  };
};

const removeData = (key) => {
  return {
    type: DELETE_DATA,
    payload: {
      key,
    },
  };
};

const clearAllData = () => {
  return {
    type: CLEAR_DATA,
  };
};

const refreshData = () => {
  return {
    type: REFRESH_DATA,
  };
};

export { addData, removeData, clearAllData, refreshData };
