import { makeServerFetchNetworkCall } from '@/network/serverAPIUtility';
import { NextResponse } from 'next/server';

const ORG_REQUEST_BASE_URL = process.env.NEXT_PUBLIC_ORGANIZATION_REQUEST_BASE_URL;

export async function GET(request, { params }) {
  try {
    const { organizationId, requestId } = params;

    if (!requestId) {
      return NextResponse.json({ status: 404 }, { status: 404 });
    }

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    const url = `${ORG_REQUEST_BASE_URL}/organizations/${organizationId}/requests/${requestId}`;

    const response = await makeServerFetchNetworkCall({ method: 'GET', url: url });

    if (!response.ok) {
      return NextResponse.json({ error: 'Failed to validate request ID' }, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error validating request ID:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
