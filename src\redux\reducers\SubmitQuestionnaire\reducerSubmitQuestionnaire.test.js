import {
  POST_QUESTIONNAIRE_QUESTIONS_REQUEST,
  POST_QUESTIONNAIRE_QUESTIONS_SUCCESS,
  POST_QUESTIONNAIRE_QUESTIONS_ERROR,
} from '../../actions/SubmitQuestionnaire/index';
import * as G<PERSON><PERSON>BA<PERSON> from '../globals';
import { submitQuestionnaireAnswersReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isQuestionnaireSubmitAnswersFetching: true,
  isQuestionnaireSubmitAnswersSuccess: false,
  isQuestionnaireSubmitAnswersError: false,
  questionnaireSubmitAnswersErrorData: null,
};

const successState = {
  isQuestionnaireSubmitAnswersFetching: false,
  isQuestionnaireSubmitAnswersSuccess: true,
  isQuestionnaireSubmitAnswersError: false,
  questionnaireSubmitAnswersErrorData: null,
  questionnaireSubmitAnswersSuccessData: data,
};

const errorState = {
  isQuestionnaireSubmitAnswersFetching: false,
  isQuestionnaireSubmitAnswersSuccess: false,
  isQuestionnaireSubmitAnswersError: true,
  questionnaireSubmitAnswersErrorData: data,
};

describe('Submit Questionnaire Reducer', () => {
  it('should return the initial state', () => {
    expect(submitQuestionnaireAnswersReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle POST_QUESTIONNAIRE_QUESTIONS_REQUEST', () => {
    expect(
      submitQuestionnaireAnswersReducer(initialState, {
        type: POST_QUESTIONNAIRE_QUESTIONS_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle POST_QUESTIONNAIRE_QUESTIONS_SUCCESS', () => {
    expect(
      submitQuestionnaireAnswersReducer(initialState, {
        type: POST_QUESTIONNAIRE_QUESTIONS_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle POST_QUESTIONNAIRE_QUESTIONS_ERROR', () => {
    expect(
      submitQuestionnaireAnswersReducer(initialState, {
        type: POST_QUESTIONNAIRE_QUESTIONS_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });
});
