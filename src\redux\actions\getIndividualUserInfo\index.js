/*
 * WIDGET_DETAILS action types
 */

export const INDIVIDUAL_USER_INFO_REQUEST = 'INDIVIDUAL_USER_INFO_REQUEST';
export const INDIVIDUAL_USER_INFO_SUCCESS = 'INDIVIDUAL_USER_INFO_SUCCESS';
export const INDIVIDUAL_USER_INFO_ERROR = 'INDIVIDUAL_USER_INFO_ERROR';
export const INDIVIDUAL_USER_INFO_RESET = 'INDIVIDUAL_USER_INFO_RESET';
export const SAVE_INDIVIDUAL_USER_INFO = 'SAVE_INDIVIDUAL_USER_INFO';

/*
 * action creators
 */

export function fetchIndividualUserInfo(data) {
  return {
    type: INDIVIDUAL_USER_INFO_REQUEST,
    payload: data,
  };
}

export function fetchIndividualUserInfoSuccess(data) {
  return {
    type: INDIVIDUAL_USER_INFO_SUCCESS,
    payload: data,
  };
}

export function fetchIndividualUserInfoError() {
  return {
    type: INDIVIDUAL_USER_INFO_ERROR,
  };
}

export const resetIndividualUserInfo = () => {
  return {
    type: INDIVIDUAL_USER_INFO_RESET,
  };
};

export const saveIndividualUserInfo = (data) => {
  return {
    type: SAVE_INDIVIDUAL_USER_INFO,
    payload: data,
  };
};
