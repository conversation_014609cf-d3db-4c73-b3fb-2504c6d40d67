import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  GET_CONSENT_AGREEMENT_ERROR,
  GET_CONSENT_AGREEMENT_SUCCESS,
  GET_CONSENT_AGREEMENT_REQUEST,
  GET_CONSENT_AGREEMENT_RESET,
} from '../../actions/getConsentAgreement';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const consentAgreementReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case GET_CONSENT_AGREEMENT_REQUEST:
      return {
        ...state,
        isConsentAgreementFetching: true,
        consentAgreementSuccess: false,
        consentAgreementError: false,
        consentAgreementErrorData: null,
      };
    case GET_CONSENT_AGREEMENT_SUCCESS: {
      return {
        ...state,
        isConsentAgreementFetching: false,
        consentAgreementSuccess: true,
        consentAgreementError: false,
        consentAgreementErrorData: null,
        consentAgreementSuccessData: payload,
      };
    }
    case GET_CONSENT_AGREEMENT_ERROR:
      return {
        ...state,
        isConsentAgreementFetching: false,
        consentAgreementSuccess: false,
        consentAgreementError: true,
        consentAgreementErrorData: payload,
      };
    case GET_CONSENT_AGREEMENT_RESET:
      return {
        ...state,
        isConsentAgreementFetching: false,
        consentAgreementSuccess: false,
        consentAgreementError: false,
        consentAgreementErrorData: null,
        consentAgreementSuccessData: null,
      };
    default:
      return state;
  }
};
