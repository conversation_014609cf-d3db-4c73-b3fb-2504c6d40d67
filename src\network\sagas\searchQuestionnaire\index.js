/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import {
  SEARCH_QUESTIONNAIRE_REQUEST,
  SEARCH_QUESTIONNAIRE_SUCCESS,
  SEARCH_QUESTIONNAIRE_ERROR,
} from '../../../redux/actions/SearchQuestionnaire';

import { makeApiFetchSearchQuestionnaire } from './api';
/**
 *
 * @param {*} action
 */

export function* fetchSearchQuestionnaire(action) {
  try {
    const response = yield call(makeApiFetchSearchQuestionnaire, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: SEARCH_QUESTIONNAIRE_SUCCESS, payload: data });
    } else {
      yield put({ type: SEARCH_QUESTIONNAIRE_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: SEARCH_QUESTIONNAIRE_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchSearchQuestionnaire() {
  yield takeEvery(SEARCH_QUESTIONNAIRE_REQUEST, fetchSearchQuestionnaire);
}
