import 'server-only';
import { UNVERIFIED_USER_DETAILS, UNVERIFIED_USER_EMAIL } from '@/utils/constants';
import { cookies } from 'next/headers';

export const setCookiesForUnverifiedUser = ({ email, firstName, lastName }) => {
  const oneDay = 24 * 60 * 60 * 1000;

  if (firstName && lastName) {
    cookies().set(UNVERIFIED_USER_DETAILS, JSON.stringify({ firstName, lastName }), {
      path: '/',
      httpOnly: true,
      secure: true,
      expires: Date.now() + oneDay,
      sameSite: 'none',
    });
  }

  cookies().set(UNVERIFIED_USER_EMAIL, email, {
    path: '/',
    httpOnly: true,
    secure: true,
    expires: Date.now() + oneDay,
    sameSite: 'none',
  });
};
