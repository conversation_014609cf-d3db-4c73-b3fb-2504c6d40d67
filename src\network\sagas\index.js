/*
 *
 */
import { all } from 'redux-saga/effects';
import { watchCare } from './care';
import { watchBookingSlots } from './bookingSlots';
import { watchWidgetDetails } from './widgetDetails';
import { watchCompleteBooking } from './completeBooking';
import { watchCancelBooking } from './cancelBooking';
import { watchUserLogOut } from './logOut';
import { watchGetBookedAppointment } from './getBookedAppointmentDetails';
import { watchRescheduleAppointment } from './rescheduleAppointment';
import { watchConfirmAppointment } from './confirmAppointment';
import { watchLocations } from './getLocations';
import { watchOauth } from './oAuth';
import { watchUserInfo } from './userInfo';
import { watchWidgetRegistrationDetails } from './widgetRegistrationDetails';
import { watchCompleteRegistration } from './completeRegistration';
import { watchValidateEmail } from './validateEmail';
import { watchResendOtp } from './resendOtp';
import { watchQuestionnaireQuestions } from './getQuestionnaireDetails';
import { watchCheckEmail } from './checkEmailInBooking';
import { watchValidateEmailInBooking } from './validateEmailInBooking';
import { watchFetchQuestionnaireWidget } from './getQuestionnaireWidget';
import { watchCreateOtp } from './CreateOtp';
import { watchaddNewPatient } from './addNewClient';
import { watchNextAvailableSlot } from './getNextAvailableSlot';
import { watchSearchAndCreatePatient } from './searchAndCreatePatient';

import { watchGetPdf } from './getPdfReport';
import { watchIndividualUserOauth } from './getIndividualUserOAuth';
import { watchIndividualUserInfo } from './getIndividualUserInfo';
import { watchSaveQuestionnaireResponseForLater } from './saveQuestionnaireResponseForLater';
import { watchGenerateHtmlReportAsPdf } from './generateHtmlReportAsPdf';
import { watchGetReportDataForPdf } from './getReportDataForPdf';
import { watchSendConnectionInvite } from './sendConnectionInvite';
import { watchSaveQuestionnaireResponseIndividual } from './saveQuestionnaireResponseIndividual';
import { watchSaveQuestionnaireResponseOrganization } from './saveQuestionnaireResponseOrganization';
import { watchCheckExistingClientIndex } from './checkExistingClientIndex';
import { watchCheckExistingConnectionIndex } from './checkExistingConnectionIndex';
import { watchCreateConnectionAtClientIndex } from './createConnectionAtClientIndex';
import { watchCreateConnectionAtConnectionIndex } from './createConnectionAtConnectionIndex';
import { watchUpdateConnectionAtClientIndex } from './updateConnectionAtClientIndex';
import { watchSearchIndividualAtClientIndex } from './searchIndividualAtClientIndex';
import { watchFetchConsentAgreement } from './getConsentAgreement';
import { watchGetIndividualData } from './getIndividualData';
import { watchCreateIndividualData } from './createIndividualData';

/**
 * single entry point to start all Sagas at once
 */
export default function* rootSaga() {
  yield all([
    watchCare(),
    watchBookingSlots(),
    watchUserInfo(),
    watchWidgetDetails(),
    watchOauth(),
    watchCancelBooking(),
    watchCompleteBooking(),
    watchGetBookedAppointment(),
    watchRescheduleAppointment(),
    watchConfirmAppointment(),
    watchLocations(),
    watchWidgetRegistrationDetails(),
    watchCompleteRegistration(),
    watchValidateEmail(),
    watchResendOtp(),
    watchQuestionnaireQuestions(),
    watchCheckEmail(),
    watchValidateEmailInBooking(),
    watchFetchQuestionnaireWidget(),
    watchCreateOtp(),
    watchUserLogOut(),
    watchaddNewPatient(),
    watchNextAvailableSlot(),
    watchSearchAndCreatePatient(),
    watchGetPdf(),
    watchIndividualUserOauth(),
    watchIndividualUserInfo(),
    watchSaveQuestionnaireResponseForLater(),
    watchGenerateHtmlReportAsPdf(),
    watchGetReportDataForPdf(),
    watchSendConnectionInvite(),
    watchSaveQuestionnaireResponseIndividual(),
    watchSaveQuestionnaireResponseOrganization(),
    watchCheckExistingClientIndex(),
    watchCheckExistingConnectionIndex(),
    watchCreateConnectionAtClientIndex(),
    watchCreateConnectionAtConnectionIndex(),
    watchUpdateConnectionAtClientIndex(),
    watchSearchIndividualAtClientIndex(),
    watchFetchConsentAgreement(),
    watchGetIndividualData(),
    watchCreateIndividualData(),
  ]);
}
