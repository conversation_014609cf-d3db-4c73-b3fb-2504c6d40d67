import {
  SEARCH_QUESTIONNAIRE_REQUEST,
  SEARCH_QUESTIONNAIRE_SUCCESS,
  SEARCH_QUESTIONNAIRE_ERROR,
} from '../../actions/SearchQuestionnaire';

import * as GLOBALS from '../globals';
import { searchQuestionnaireReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isSearchQuestionFetching: true,
  isSearchQuestionSuccess: false,
  isSearchQuestionError: false,
  searchQuestionErrorData: null,
};

const successState = {
  isSearchQuestionFetching: false,
  isSearchQuestionSuccess: true,
  isSearchQuestionError: false,
  searchQuestionErrorData: null,
  searchQuestionSuccessData: data,
};

const errorState = {
  isSearchQuestionFetching: false,
  isSearchQuestionSuccess: false,
  isSearchQuestionError: true,
  searchQuestionErrorData: data,
};

describe('Oauth Booking Details Reducer', () => {
  it('should return the initial state', () => {
    expect(searchQuestionnaireReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle SEARCH_QUESTIONNAIRE_REQUEST', () => {
    expect(
      searchQuestionnaireReducer(initialState, {
        type: SEARCH_QUESTIONNAIRE_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle SEARCH_QUESTIONNAIRE_SUCCESS', () => {
    expect(
      searchQuestionnaireReducer(initialState, {
        type: SEARCH_QUESTIONNAIRE_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle SEARCH_QUESTIONNAIRE_ERROR', () => {
    expect(
      searchQuestionnaireReducer(initialState, {
        type: SEARCH_QUESTIONNAIRE_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });
});
