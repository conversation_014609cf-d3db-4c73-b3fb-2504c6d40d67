/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import { CARE_RESET, CARE_ERROR, CARE_SUCCESS, CARE_REQUEST } from '../../actions/care';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const careReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case CARE_REQUEST:
      return {
        ...state,
        isCareFetching: true,
        fetchCareSuccess: false,
        fetchCareError: false,
        careErrorData: null,
      };
    case CARE_SUCCESS: {
      return {
        ...state,
        isCareFetching: false,
        fetchCareSuccess: true,
        fetchCareError: false,
        careErrorData: null,
        careSuccessData: payload,
      };
    }
    case CARE_ERROR:
      return {
        ...state,
        isCareFetching: false,
        fetchCareSuccess: false,
        fetchCareError: true,
        careErrorData: payload,
      };
    case CARE_RESET:
      return {
        ...state,
        isCareFetching: false,
        fetchCareSuccess: false,
        fetchCareError: false,
        careErrorData: null,
      };
    default:
      return state;
  }
};
