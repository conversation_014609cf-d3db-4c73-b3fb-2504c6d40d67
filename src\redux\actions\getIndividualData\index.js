export const GET_INDIVIDUAL_DATA_REQUEST = 'GET_INDIVIDUAL_DATA_REQUEST';
export const GET_INDIVIDUAL_DATA_SUCCESS = 'GET_INDIVIDUAL_DATA_SUCCESS';
export const GET_INDIVIDUAL_DATA_ERROR = 'GET_INDIVIDUAL_DATA_ERROR';
export const GET_INDIVIDUAL_DATA_RESET = 'GET_INDIVIDUAL_DATA_RESET';

export function getIndividualDataRequest(data) {
  return {
    type: GET_INDIVIDUAL_DATA_REQUEST,
    payload: data,
  };
}

export function getIndividualDataSuccess(data) {
  return {
    type: GET_INDIVIDUAL_DATA_SUCCESS,
    payload: data,
  };
}

export function getIndividualDataError(error) {
  return {
    type: GET_INDIVIDUAL_DATA_ERROR,
    payload: error,
  };
}

export const resetGetIndividualData = () => {
  return {
    type: GET_INDIVIDUAL_DATA_RESET,
  };
};
