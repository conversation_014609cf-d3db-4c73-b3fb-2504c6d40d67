import { useSelector } from 'react-redux';
import { Stack, Grid, Divider, Typography } from '@mui/material';
import * as BookingUtility from '../../bookingUtility';
import dataValidation from '@/utils/dataValidation/dataValidation';
import { convertLongDateTimeFormat, convertTimeIntoTwelveHourFormat, weekDays } from '@/utils/helpers/date-time';
import { TIME_FORMAT_SMALL_CASE } from '@/utils/constants/index';
import { LocationCard } from './LocationCard';
import { useTranslation } from 'react-i18next';

function LocationList(props) {
  console.log('TRACE: LocationList');
  const { handleNavigationCallback, locations, locationDistanceMap, radius } = props;
  const { t } = useTranslation();

  const widgetDetailsSuccessData = useSelector((state) => state.widgetDetailsReducer.widgetDetailsSuccessData);
  const { locationRequired } = widgetDetailsSuccessData || {};

  const isLocationsSuccess = useSelector((state) => state.getLocationsReducer.isLocationsSuccess);

  const { isNextAvailableSlotFetching, isNextAvailableSlotSuccess, nextAvailableSlotSuccessData } = useSelector(
    (state) => state.getNextAvailableSlotReducer,
  );

  const getNextAvailableSlot = (location) => {
    let nextAvlSlot = null;
    if (isNextAvailableSlotSuccess && nextAvailableSlotSuccessData?.entry?.length > 0) {
      nextAvailableSlotSuccessData.entry.forEach((nextAvailableSlot) => {
        let locationObj = nextAvailableSlot.resource.extension.filter(
          (obj) => obj.url == 'http://cambian.com/Location/location-id',
        );
        if (locationObj.length && locationObj[0].valueString == location.resource.id) {
          if (nextAvailableSlot.resource && nextAvailableSlot.resource.start) {
            nextAvlSlot = nextAvailableSlot.resource.start;
          }
        }
      });
    }
    if (nextAvlSlot) {
      return convertLongDateTimeFormat(nextAvlSlot) + ' (PST)';
    } else {
      return nextAvlSlot;
    }
  };

  if (locationDistanceMap && locationDistanceMap.size) {
    locations.map((location) => {
      location.resource.distance = BookingUtility.getLocationDistance(locationDistanceMap, location);
    });
  }

  const getFormattedDayName = (dayCode) => {
    return weekDays[dayCode.toLowerCase()] || dayCode.charAt(0).toUpperCase() + dayCode.slice(1);
  };

  const getClinicWorkingTime = (day) => {
    if (!dataValidation.isDataEmpty(day)) {
      return `${convertTimeIntoTwelveHourFormat(
        t('dateFormat') + day.openingTime,
        TIME_FORMAT_SMALL_CASE,
      )} - ${convertTimeIntoTwelveHourFormat(t('dateFormat') + day.closingTime, TIME_FORMAT_SMALL_CASE)}`;
    }
    return;
  };

  const getClinicSchedule = (locationDetails) => {
    console.log(locationDetails?.resource?.hoursOfOperation);
    return (
      <Grid container>
        <Grid item sm={12} md={6} lg={5}>
          {locationDetails?.resource?.hoursOfOperation?.length &&
            locationDetails.resource.hoursOfOperation.map((day, index) => {
              return (
                <Grid container className="row" key={`str${index}`}>
                  <Grid item xs={12}>
                    {day.daysOfWeek?.length && getFormattedDayName(day.daysOfWeek[0])} {`${getClinicWorkingTime(day)}`}
                  </Grid>
                </Grid>
              );
            })}
        </Grid>
      </Grid>
    );
  };

  const isLocationInRadius = (location) => {
    if (locationRequired !== true) return true;
    return Math.floor(Number(location?.resource?.distance)) <= Number(radius);
  };

  const noClinicsInRadius = locations
    ?.sort(BookingUtility.sortLocation)
    .every((location) => !isLocationInRadius(location));

  return (
    <>
      <Stack
        direction="column"
        alignItems="center"
        spacing={2}
        divider={<Divider orientation="horizontal" flexItem />}
        sx={{ pb: 2 }}
      >
        {isLocationsSuccess && noClinicsInRadius ? (
          <Typography variant="button" sx={{ width: '100%' }}>
            {t('noClinicsFound')}
          </Typography>
        ) : (
          locations
            .sort(BookingUtility.sortLocation)
            .map(
              (location, index) =>
                isLocationInRadius(location) && (
                  <LocationCard
                    key={index}
                    location={location}
                    isNextAvailableSlotFetching={isNextAvailableSlotFetching}
                    getNextAvailableSlot={getNextAvailableSlot}
                    getClinicSchedule={getClinicSchedule}
                    handleNavigationCallback={handleNavigationCallback}
                  />
                ),
            )
        )}
      </Stack>
    </>
  );
}

export { LocationList };
