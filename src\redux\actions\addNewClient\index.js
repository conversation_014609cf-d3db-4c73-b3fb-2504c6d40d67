/*
 *ADD_NEW_CLIENT action types
 */

export const ADD_NEW_CLIENT_REQUEST = 'ADD_NEW_CLIENT_REQUEST';
export const ADD_NEW_CLIENT_SUCCESS = 'ADD_NEW_CLIENT_SUCCESS';
export const ADD_NEW_CLIENT_ERROR = 'ADD_NEW_CLIENT_ERROR';
export const ADD_NEW_CLIENT_RESET = 'ADD_NEW_CLIENT_RESET';

/*
 * action creators
 */

export function addNewClientRequest(data) {
  return {
    type: ADD_NEW_CLIENT_REQUEST,
    payload: data,
  };
}

export function addNewClientSuccess(data) {
  return {
    type: ADD_NEW_CLIENT_SUCCESS,
    payload: data,
  };
}

export function addNewClientError() {
  return {
    type: ADD_NEW_CLIENT_ERROR,
  };
}

export function addNewClientReset() {
  return {
    type: ADD_NEW_CLIENT_RESET,
  };
}
