/*
 * POST Questions action types
 */

export const UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_REQUEST = 'UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_REQUEST';
export const UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_SUCCESS = 'UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_SUCCESS';
export const UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_ERROR = 'UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_ERROR';
export const UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_RESET = 'UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_RESET';
/*
 * action creators
 */

export function updateSaveLiteQuestionnaireResponse(data) {
  return {
    type: UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_REQUEST,
    payload: data,
  };
}

export function updateSaveLiteQuestionnaireResponseSuccess(data) {
  return {
    type: UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_SUCCESS,
    payload: data,
  };
}

export function updateSaveLiteQuestionnaireResponseError() {
  return {
    type: UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_ERROR,
  };
}

export function resetUpdateSaveLiteQuestionnaireResponse() {
  return {
    type: UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_RESET,
  };
}
