/*
 *
 */

import { makeNetworkCall } from '../..';
import { ORGANIZATION_ID } from '../../../utils/constants';
import { BASE_URL, CREATE_PATIENT } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiFetchAddNewPatient(action) {
  const { payload = {} } = action || {};
  const { headers = {}, patientData, organizationId } = payload || {};
  const mainUrl = BASE_URL + CREATE_PATIENT.replace(ORGANIZATION_ID, organizationId);
  const config = {
    method: 'POST',
    url: mainUrl,
    headers: headers,
    data: patientData,
  };
  return makeNetworkCall(config);
}
