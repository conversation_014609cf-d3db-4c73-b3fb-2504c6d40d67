import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_REQUEST,
  SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_SUCCESS,
  SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_ERROR,
  SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_RESET,
} from '../../actions/searchIndividualAtClientIndex';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const searchIndividualAtClientIndexReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_REQUEST:
      return {
        ...state,
        isSearchIndividualAtClientIndexFetching: true,
        searchIndividualAtClientIndexSuccess: false,
        searchIndividualAtClientIndexError: false,
        searchIndividualAtClientIndexErrorData: null,
      };
    case SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_SUCCESS: {
      return {
        ...state,
        isSearchIndividualAtClientIndexFetching: false,
        searchIndividualAtClientIndexSuccess: true,
        searchIndividualAtClientIndexError: false,
        searchIndividualAtClientIndexErrorData: null,
        searchIndividualAtClientIndexSuccessData: payload,
      };
    }
    case SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_ERROR:
      return {
        ...state,
        isSearchIndividualAtClientIndexFetching: false,
        searchIndividualAtClientIndexSuccess: false,
        searchIndividualAtClientIndexSuccessData: null,
        searchIndividualAtClientIndexError: true,
        searchIndividualAtClientIndexErrorData: payload,
      };
    case SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_RESET:
      return {
        ...state,
        isSearchIndividualAtClientIndexFetching: undefined,
        searchIndividualAtClientIndexSuccess: undefined,
        searchIndividualAtClientIndexSuccessData: undefined,
        searchIndividualAtClientIndexError: undefined,
        searchIndividualAtClientIndexErrorData: undefined,
      };
    default:
      return state;
  }
};
