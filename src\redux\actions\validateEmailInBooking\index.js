/*
 * VALIDATE_EMAIL_IN_BOOKING action types
 */

export const VALIDATE_EMAIL_IN_BOOKING_REQUEST = 'VALIDATE_EMAIL_IN_BOOKING_REQUEST';
export const VALIDATE_EMAIL_IN_BOOKING_SUCCESS = 'VALIDATE_EMAIL_IN_BOOKING_SUCCESS';
export const VALIDATE_EMAIL_IN_BOOKING_ERROR = 'VALIDATE_EMAIL_IN_BOOKING_ERROR';
export const VALIDATE_EMAIL_IN_BOOKING_RESET = 'VALIDATE_EMAIL_IN_BOOKING_RESET';

/*
 * action creators
 */
export function validateEmailInBooking(data) {
  return {
    type: VALIDATE_EMAIL_IN_BOOKING_REQUEST,
    payload: data,
  };
}

export function validateActionEmailInBookingSuccess(data) {
  return {
    type: VALIDATE_EMAIL_IN_BOOKING_SUCCESS,
    payload: data,
  };
}

export function validateActionEmailInBookingError() {
  return {
    type: VALIDATE_EMAIL_IN_BOOKING_ERROR,
  };
}

export const resetValidateEmail = () => {
  return {
    type: VALIDATE_EMAIL_IN_BOOKING_RESET,
  };
};
