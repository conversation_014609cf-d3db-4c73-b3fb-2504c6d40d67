import { APPOINTMENT_ID, ORGANIZATION, ORGANIZATION_ID } from '@/utils/constants';
import { GET_APPOINTMENT_DETAIL, GET_ELIGIBILITY } from '@/utils/constants/awsApiEndpoints';
import { getOrganizationAndWidgetId } from '../commonUtility';
import { makeFetchNetworkCall } from '@/network';

export const getEligibility = async (serviceId, schedulerClientId) => {
  const [organizationId] = getOrganizationAndWidgetId();
  const URL = GET_ELIGIBILITY.replace(ORGANIZATION_ID, organizationId);

  try {
    const response = await makeFetchNetworkCall({
      method: 'POST',
      url: URL,
      data: { services: serviceId, schedulerClientId },
    });
    const data = await response?.json();

    return data;
  } catch (error) {
    console.error('Error getting eligibility:', error);
    return null;
  }
};

export const fetchAppointmentDetails = async (appointmentId) => {
  const [organizationId] = getOrganizationAndWidgetId();
  const url = GET_APPOINTMENT_DETAIL.replace(ORGANIZATION_ID, organizationId).replace(APPOINTMENT_ID, appointmentId);

  const response = await makeFetchNetworkCall({ url, method: 'GET' });
  return response;
};
