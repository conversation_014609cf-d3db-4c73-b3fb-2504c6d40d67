import React, { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { Box } from '@mui/material';
import moment from 'moment';
import { QuestionnaireReportV2, BannerV2 } from '@/components';
import * as QuestionnaireUtility from '../QuestionnaireWidget/questionnaireUtility';
import { getDemographicFromUrl } from '../Demographics/demographicUtility';
import { useAPIs } from './useAPIs';
import { getParamFromUrl, removeHtmlLoader } from '../commonUtility';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { ORGANIZATION_QUERY_KEY, REPORT_SETTINGS_QUERY_KEY } from '../commonConstants';

export const ReportDownload = () => {
  const { fetchGetReportDataForPdf, fetchOrganizationDetails, fetchReportSettings } = useAPIs();
  const questionnaireReportById = getParamFromUrl('questionnaireReportById');
  const queryClient = useQueryClient();

  const [reportHeader, setReportHeader] = useState({ title: '', date: '' });
  const [reportDemographics, setReportDemographics] = useState(null);
  const [demographic, setDemographic] = useState();
  const [bannerTemplate, setBannerTemplate] = useState();
  const [organization, setOrganization] = useState();
  //var bannerData = { Client: { firstName: 'John', middleName: 'Kumar', lastName: 'Day' }, Org: { name: 'BSL' } };
  //var bannerTemplate = '<b>{Client.lastName}</b>, <i>{Client.firstName}</i> {Client.middleName} | {Org.name}';

  const { getReportDataForPdfSuccess, getReportDataForPdfSuccessData } = useSelector(
    (state) => state.getReportDataForPdfReducer,
  );

  const { reportResponse } = getReportDataForPdfSuccessData || {};
  //const { reportResponse, showBanner, bannerData } = getReportDataForPdfSuccessData || {};
  const [showBanner, setShowBanner] = useState(false);

  const reportRef = useRef(null);

  useEffect(() => {
    removeHtmlLoader(); // remove loader hardcode in index.html

    if (questionnaireReportById) {
      fetchGetReportDataForPdf(questionnaireReportById);
    }
  }, [questionnaireReportById]);

  useEffect(() => {
    if (getReportDataForPdfSuccess && getReportDataForPdfSuccessData) {
      if (reportResponse && reportResponse.questionnaire && reportResponse?.questionnaireResponse) {
        setDemographic(reportResponse?.demographic);
        checkResultData();

        const fetchReportSettings = async () => {
          try {
            const response = await callReportSettings();
            if (response?.data) {
              setBannerTemplate(response?.data?.reportHeader);
              setShowBanner(true);
            } else {
              setShowBanner(false);
            }
          } catch (error) {
            console.error('Error fetching Organization data:', error);
          }
        };

        const fetchOrganization = async () => {
          try {
            const response = await callOrganizationDetails();
            if (response?.data) {
              setOrganization(response?.data);
              setShowBanner(true);
            } else {
              setShowBanner(false);
            }
          } catch (error) {
            console.error('Error fetching Organization data:', error);
          }
        };

        fetchOrganization();
        fetchReportSettings();

        // remove the outer border for the report on downloading report
        let element = document.querySelector('#questionnaireReport')?.children[0];
        if (element) element.style.border = 'none';
      }
    }
  }, [getReportDataForPdfSuccess, getReportDataForPdfSuccessData]);

  const callOrganizationDetails = async () => {
    const response = await queryClient.fetchQuery({
      queryKey: ORGANIZATION_QUERY_KEY,
      queryFn: () => fetchOrganizationDetails(),
    });

    return response;
  };

  const callReportSettings = async () => {
    const response = await queryClient.fetchQuery({
      queryKey: REPORT_SETTINGS_QUERY_KEY,
      queryFn: () => fetchReportSettings(),
    });

    return response;
  };

  const checkResultData = () => {
    let demographicSection = QuestionnaireUtility.extractResultPageSection(
      reportResponse?.questionnaire,
      '/result-page',
      'Demographics',
    );

    let resultData = QuestionnaireUtility.getResultData(reportResponse?.questionnaire?.extension);

    if (resultData) {
      const demographicFields = reportResponse?.demographic || getDemographicFromUrl(demographicSection?.fields);

      resultData = JSON.parse(resultData);
      if (resultData) {
        let reportHeader = {};

        const dateSection = QuestionnaireUtility.extractResultPageSection(
          reportResponse?.questionnaire,
          '/result-page',
          'Date',
        );
        const headerSection = QuestionnaireUtility.extractResultPageSection(
          reportResponse?.questionnaire,
          '/result-page',
          'Label',
        );

        if (dateSection) {
          const formattedDate = moment(new Date()).format(getDateFormat(dateSection));
          let dateString = dateSection?.htmlText.replace('&lt;completion date&gt;', ' ' + formattedDate);
          reportHeader.date = dateString.replace('[COMPLETION_DATE]', ' ' + formattedDate);
          setReportHeader(reportHeader);
        }

        if (headerSection) {
          reportHeader.title = headerSection.displayName ? headerSection.displayName : '';
          setReportHeader(reportHeader);
        }

        //check demographic
        if (reportResponse && demographicSection && demographicFields) {
          setReportDemographics(
            QuestionnaireUtility.buildDemographicByParameter(demographicSection, demographicFields),
          );
        }
      }
    }
  };

  const getDateFormat = (dateSection) => {
    let format = 'YYYY-MM-DD HH:mm:ss';
    if (dateSection && dateSection.fields && dateSection.fields[0] && dateSection.fields[0].format) {
      format = dateSection.fields[0].format;
    }
    return format;
  };

  const getResult = () => {
    if (getReportDataForPdfSuccess && getReportDataForPdfSuccessData) {
      return (
        <>
          {showBanner && (
            <BannerV2 demographic={demographic} organization={organization} bannerTemplate={bannerTemplate}></BannerV2>
          )}
          <Box id="questionnaireReport">
            <QuestionnaireReportV2
              fhirQuestionnaire={reportResponse?.questionnaire}
              fhirResponse={reportResponse?.questionnaireResponse}
              headerJson={reportHeader}
              demographic={reportResponse?.demographic}
              isCallbackPrint={true}
              browserTimezone={reportResponse?.browserTimezone}
            />
          </Box>
        </>
      );
    } else {
      <></>;
    }
  };

  return (
    <>
      <Box sx={{ px: '4%', pt: '24px', pb: '36px' }} className="report" ref={reportRef}>
        {getResult()}
      </Box>
    </>
  );
};
