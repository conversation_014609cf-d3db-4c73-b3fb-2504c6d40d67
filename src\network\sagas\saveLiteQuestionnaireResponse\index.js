/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  SAVE_LITE_QUESTIONNAIRE_RESPONSE_REQUEST,
  SAVE_LITE_QUESTIONNAIRE_RESPONSE_SUCCESS,
  SAVE_LITE_QUESTIONNAIRE_RESPONSE_ERROR,
} from '../../../redux/actions/saveLiteQuestionnaireResponse';

import { makeApiFetchSaveLiteQuestionnaireResponse } from './api';

/**
 *
 * @param {*} action
 */

export function* fetchSaveLiteQuestionnaireResponse(action) {
  try {
    const response = yield call(makeApiFetchSaveLiteQuestionnaireResponse, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: SAVE_LITE_QUESTIONNAIRE_RESPONSE_SUCCESS, payload: data });
    } else {
      yield put({ type: SAVE_LITE_QUESTIONNAIRE_RESPONSE_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: SAVE_LITE_QUESTIONNAIRE_RESPONSE_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchSaveLiteQuestionnaireResponse() {
  yield takeEvery(SAVE_LITE_QUESTIONNAIRE_RESPONSE_REQUEST, fetchSaveLiteQuestionnaireResponse);
}
