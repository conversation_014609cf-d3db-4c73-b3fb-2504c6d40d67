import { useDispatch, useSelector } from 'react-redux';
import {
  generateAPIHeaderForCambianUserInfoEndPoint,
  generateAPIHeaderWithoutAccessToken,
} from '@/utils/constants/common';
import { getOrganizationAndWidgetId, getParamFromUrl } from '../commonUtility';
import { generateHtmlReportAsPdf } from '@/redux/actions/generateHtmlReportAsPdf';
import { getReportDataForPdf } from '@/redux/actions/getReportDataForPdf';
import { saveQuestionnaireResponseIndividual } from '@/redux/actions/saveQuestionnaireResponseIndividual';
import { saveQuestionnaireResponseOrganization } from '@/redux/actions/saveQuestionnaireResponseOrganization';
import { useCommonAPIs } from '@/hooks/useCommonAPIs';
import { useSession } from 'next-auth/react';
import { getPdfInfo } from '@/redux/actions/getPdfReport';
import { fetchQuestionnaireQuestions } from '@/redux/actions/getQuestionnaireDetails';
import {
  GET_ORGANIZATION_DETAILS,
  GET_REPORT_SETTINGS,
  DOWNLOAD_HTML_REPORT_AS_PDF,
  INDIVIDUAL_REQUESTS,
} from '@/utils/constants/awsApiEndpoints';
import { INDIVIDUAL, INDIVIDUAL_ID, ORGANIZATION_ID } from '@/utils/constants';
import { makeFetchNetworkCall } from '@/network';

export const useAPIs = () => {
  const dispatch = useDispatch();
  const { data: session } = useSession();

  const { questionnaireQuestionsSuccessData } = useSelector((state) => state.getQuestionnaireQuestionRedux);

  const { getClientId } = useCommonAPIs();
  const [organizationId] = getOrganizationAndWidgetId();
  const { individualUserOAuthSuccessData } = useSelector((state) => state.individualUserOAuthReducer);
  const { individualUserInfoSuccessData } = useSelector((state) => state.individualUserInfoReducer);

  const headersWithoutAccessToken = generateAPIHeaderWithoutAccessToken();
  let headersWithAccessToken = generateAPIHeaderForCambianUserInfoEndPoint(individualUserOAuthSuccessData?.accessToken);
  headersWithAccessToken['Content-type'] = 'application/json';

  const saveQuestionnaireResponseAtIndividual = (response) => {
    dispatch(
      saveQuestionnaireResponseIndividual({
        headers: { Authorization: session?.user?.idToken },
        data: {
          questionnaire: questionnaireQuestionsSuccessData?.questionnaire,
          questionnaireResponse: response,
        },
        individualId: session?.user?.cambianId,
      }),
    );
  };

  const saveQuestionnaireResponseAtOrganization = (response) => {
    const requestId = getParamFromUrl('requestId');
    dispatch(
      saveQuestionnaireResponseOrganization({
        data: requestId ? { ...response, requestId } : response,
        organizationId,
      }),
    );
  };

  const fetchGenerateHtmlReportAsPdf = (data) => {
    dispatch(generateHtmlReportAsPdf({ headers: headersWithoutAccessToken, data }));
  };

  const fetchGetReportDataForPdf = (questionnaireReportById) => {
    dispatch(getReportDataForPdf({ questionnaireReportById }));
  };

  const fetchGeneratePdfReport = (pdfReportData) => {
    dispatch(getPdfInfo({ pdfReportData }));
  };

  const fetchQuestionnaire = (questionnaireId, repository) => {
    dispatch(
      fetchQuestionnaireQuestions({
        questionnaireId,
        repository,
      }),
    );
  };

  const fetchOrganizationDetails = async () => {
    const [organizationId] = getOrganizationAndWidgetId();
    const url = GET_ORGANIZATION_DETAILS.replace(ORGANIZATION_ID, organizationId);

    const response = await makeFetchNetworkCall({ url, method: 'GET' });
    return response;
  };

  const fetchReportSettings = async () => {
    const url = GET_REPORT_SETTINGS.replace(ORGANIZATION_ID, organizationId);

    const response = await makeFetchNetworkCall({ url, method: 'GET' });
    return response;
  };

  const fetchDownloadHtmlReport = async (appUrl, htmlString) => {
    const url = DOWNLOAD_HTML_REPORT_AS_PDF;
    const response = await makeFetchNetworkCall({
      url,
      method: 'POST',
      formData: true,
      data: JSON.stringify({
        htmlString,
      }),
    });
    return response;
  };

  const createIndividualRequest = async (qrId, orgRequest) => {
    try {
      const individualId = session?.user?.cambianId;
      const requestId = getParamFromUrl('requestId');

      const response = await fetch(`/api/individuals/${individualId}/requests`, {
        method: 'POST',
        body: JSON.stringify({
          qrId,
          orgRequest,
          requestId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating individual request:', error);
      return error;
    }
  };

  return {
    fetchGenerateHtmlReportAsPdf,
    fetchGetReportDataForPdf,
    saveQuestionnaireResponseAtIndividual,
    saveQuestionnaireResponseAtOrganization,
    fetchGeneratePdfReport,
    fetchQuestionnaire,
    fetchOrganizationDetails,
    fetchReportSettings,
    fetchDownloadHtmlReport,
    createIndividualRequest,
  };
};
