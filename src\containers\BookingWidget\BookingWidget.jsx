import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import LanguageIcon from '@mui/icons-material/Language';
import { Box, Button, Menu, MenuItem } from '@mui/material';
import * as BookingUtility from './bookingUtility';
import * as CommonUtility from '../commonUtility';
import * as DemographicUtility from '../Demographics/demographicUtility';
import { actions, APPOINTMENT_QUERY_KEY } from '../commonConstants';
import { pages, finalActions } from './bookingConstants';
import { fetchWidgetDetails } from '@/redux/actions/widgetDetails';
import { Loader, PageNotFound, HeadingAndDescription, Action, LanguageSelector } from '@/components';
import useNotification from '@/hooks/useNotification';
import { TIME_FORMAT, DATE_FORMAT } from '@/utils/constants/index';
import { convertTimeIntoTwelveHourFormatIgnoreZone } from '@/utils/helpers/date-time';
import { resetCheckEmail } from '@/redux/actions/checkEmailInBooking';
import { getPageByBookingWidgetFlowType } from './bookingUtility';
import { useTranslation } from 'react-i18next';
import { Login } from '../Demographics/Login';
import { HistoryAndServices } from '../HistoryAndServices';
import { Service, BookingReview, BookingSummary, RescheduleAppointment, SlotBooking, Location } from './components';
import { generateAPIHeader } from '@/utils/constants/common';
import { useCommonAPIs } from '@/hooks/useCommonAPIs';
import { resetUpdateConnectionAtClientIndex } from '@/redux/actions/updateConnectionAtClientIndex';
import { SignInSignUp } from '../auth/SignInSignUp';
import { EditDemographic } from '../Demographics';
import { fetchAppointmentDetails, getEligibility } from './api';
import { useQuery, useQueryClient } from '@tanstack/react-query';

export const BookingWidget = (props) => {
  console.log('TRACE: BookingWidget');

  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const { getClientId, getSchedulerClientId } = useCommonAPIs();
  const queryClient = useQueryClient();

  const [organizationId, widgetId] = CommonUtility.getOrganizationAndWidgetId();

  const [, sendNotification] = useNotification();
  const [selectedService, setSelectedService] = useState(null);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [searchedPlace, setSearchedPlace] = useState({});
  const [locationDistanceMap, setLocationDistanceMap] = useState(null);
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [isDemographicFromUrl, setIsDemographicFromUrl] = useState(false);
  const [showDemographicFromUrl, setShowDemographicFromUrl] = useState(false);
  const [redirectBackToWidgetId, setRedirectBackToWidgetId] = useState(null);
  const [demographic, setDemographic] = useState(null);
  const [appointmentId, setAppointmentId] = useState(null);
  const [headerSummary, setHeaderSummary] = useState(new Map());
  const [isFreshRecordFetched, setIsFreshRecordFetched] = useState(false);
  const [finalAction, setFinalAction] = useState(finalActions.BOOKING); // it can be booking or reschedule or cancel
  const [isSignInDialogOpen, setIsSignInDialogOpen] = useState(false);
  const [showDemographicForm, setShowDemographicForm] = useState(!showDemographicFromUrl);
  const [isActionPerformed, setIsActionPerformed] = useState(false);
  const [eligibilityData, setEligibilityData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);

  const handleLanguageChange = (lang) => {
    setAnchorEl(null);

    try {
      const headers = generateAPIHeader();
      dispatch(
        fetchWidgetDetails({
          organizationId,
          widgetId: widgetId,
          headers,
          lang,
        }),
      );

      setSelectedService(null);
      setSelectedLocation(null);
      setSelectedSlot(null);
      setDemographic(null);
      setHeaderSummary(new Map());
      setIsActionPerformed(false);

      const appointmentId = CommonUtility.getParamFromUrl('appointmentId');
      if (appointmentId) {
        widgetDetailsSuccessData?.confirmationPage?.enabled
          ? setCurrentPage(pages.CONFIRMATION)
          : setCurrentPage(pages.DEMOGRAPHIC);
      } else {
        setCurrentPage(BookingUtility.getFirstBookingScreen(introduction));
      }
    } catch (error) {
      console.error('Error changing language:', error);
      sendNotification({ msg: t('apiError'), variant: 'error' });
    }
  };

  const { isGetIndividualDataFetching } = useSelector((state) => state.getIndividualDataReducer);

  const {
    isWidgetDetailsFetching,
    fetchWidgetDetailsSuccess,
    widgetDetailsSuccessData,
    fetchWidgetDetailsError,
    widgetDetailsErrorData,
  } = useSelector((state) => state.widgetDetailsReducer);
  const { searchIndividualAtClientIndexError, searchIndividualAtClientIndexErrorData } = useSelector(
    (state) => state.searchIndividualAtClientIndexReducer,
  );

  // const {
  //   getBookedAppointmentFetching,
  //   getBookedAppointmentSuccess,
  //   getBookedAppointmentSuccessData,
  //   getBookedAppointmentError,
  //   getBookedAppointmentErrorData,
  // } = useSelector((state) => state.getBookedAppointmentReducer);

  const {
    services = [],
    fields,
    matchingInfoOnly,
    introduction = {},
    action = {},
    showSignIn,
    individualNotFoundPage,
    isConnectionRequired,
    clientInformationPageTitle,
    clientInformationPageSubtitle,
    widgetType,
    displayEligibility,
    multipleIndividualEnabled: isMultipleIndividualEnabled,
    isConsentRequired,
    selfRegistration,
    cancellationMessage,
    otpVerificationEnabled,
    byRequestOnly,
    requestNotFoundPage,
    availableLanguages,
    currentLanguage,
    defaultLanguage,
  } = widgetDetailsSuccessData || {};

  const appointmentCancellationMessage = cancellationMessage || t('appointmentCancellationMessage');

  const { data: appointmentData, isLoading: getAppointmentLoading } = useQuery({
    queryKey: APPOINTMENT_QUERY_KEY,
    enabled: false,
  });

  const matchFields = useMemo(() => {
    return matchingInfoOnly ? fields.filter((field) => field.match) : fields;
  }, [fields]);

  const [currentPage, setCurrentPage] = useState(BookingUtility.getFirstBookingScreen(introduction));

  const smartDates = BookingUtility.getSmartDates(eligibilityData, selectedService);

  const smartDatesStatus = {
    earliestDateStatus: smartDates?.earliestDate
      ? BookingUtility.validateSmartDates(smartDates.earliestDate)
      : 'INVALID',
    latestDateStatus: smartDates?.latestDate ? BookingUtility.validateSmartDates(smartDates.latestDate) : 'INVALID',
    bothDateValid:
      smartDates.earliestDate && smartDates.latestDate
        ? ['PRESENT', 'FUTURE'].includes(BookingUtility.validateSmartDates(smartDates.earliestDate)) &&
          BookingUtility.validateSmartDates(smartDates.latestDate) === 'FUTURE'
        : false,
  };

  const widgetDetailsFetchedRef = useRef(false);
  //fetch  widget details
  useEffect(() => {
    if (widgetDetailsFetchedRef?.current !== true) {
      console.log('TRACE: BookingWidget.useEffect[]');
      BookingUtility.resetAllAPIReducers(dispatch);

      const isDemographicPageShow = CommonUtility.getParamFromUrl('demographicPageShow');
      if (isDemographicPageShow === 'true') {
        setShowDemographicForm(false);
      }

      const appointmentId = CommonUtility.getParamFromUrl('appointmentId');
      if (appointmentId) {
        setFinalAction(finalActions.MODIFICATION);
        setAppointmentId(appointmentId);
      }

      const headers = generateAPIHeader();

      dispatch(
        fetchWidgetDetails({
          organizationId,
          widgetId: widgetId,
          headers,
          lang: currentLanguage,
        }),
      );
    }
  }, []);

  useEffect(() => {
    if (isWidgetDetailsFetching) {
      setIsFreshRecordFetched(true);
      return;
    }

    if (fetchWidgetDetailsSuccess && widgetDetailsSuccessData) {
      document.title = widgetDetailsSuccessData?.widgetTitle || 'Widget';
      widgetDetailsFetchedRef.current = true;
      const appointmentId = CommonUtility.getParamFromUrl('appointmentId');

      if (appointmentId) {
        const initialPage = widgetDetailsSuccessData?.confirmationPage?.enabled
          ? pages.CONFIRMATION
          : pages.DEMOGRAPHIC;
        setCurrentPage(initialPage);
      } else if (isFreshRecordFetched) {
        setCurrentPage(BookingUtility.getFirstBookingScreen(introduction));
      }

      if (widgetDetailsSuccessData.currentLanguage || widgetDetailsSuccessData.defaultLanguage) {
        const languageToUse = widgetDetailsSuccessData.currentLanguage || widgetDetailsSuccessData.defaultLanguage;

        if (languageToUse && i18n.language !== languageToUse) {
          i18n.changeLanguage(languageToUse);
        }
      }

      if (CommonUtility.isParamInUrl('demographicPageShow')) {
        const isDemographicPageShow = CommonUtility.getParamFromUrl('demographicPageShow');

        setIsDemographicFromUrl(true);
        setShowDemographicFromUrl(isDemographicPageShow === 'true');
        setRedirectBackToWidgetId(CommonUtility.getParamFromUrl('questionnaireWidgetId'));
        setDemographic(DemographicUtility.getDemographicFromUrl(fields));
      }
    }
  }, [isWidgetDetailsFetching, fetchWidgetDetailsSuccess, widgetDetailsSuccessData]);

  //this useEffect is for fresh booking when we will not get any appointmentID in url
  useEffect(() => {
    if (isFreshRecordFetched && fetchWidgetDetailsError && widgetDetailsErrorData) {
      sendNotification({ msg: t('apiError'), variant: 'error' });
      BookingUtility.resetAllAPIReducersIncludingBooking(dispatch);
      setCurrentPage(pages.PAGE_NOT_FOUND);
    }
  }, [fetchWidgetDetailsSuccess, widgetDetailsSuccessData, fetchWidgetDetailsError, widgetDetailsErrorData]);

  useEffect(() => {
    if (searchIndividualAtClientIndexError && searchIndividualAtClientIndexErrorData) {
      if (
        searchIndividualAtClientIndexErrorData?.errorDetails &&
        Array.isArray(searchIndividualAtClientIndexErrorData.errorDetails)
      ) {
        const appointmentNotExistError = searchIndividualAtClientIndexErrorData.errorDetails.find(
          (errorDetail) => errorDetail.message === 'Appointment does not exist.',
        );

        if (appointmentNotExistError) {
          setCurrentPage(pages.APPOINTMENT_NOT_FOUND);
          return;
        }
      }
    }
  }, [searchIndividualAtClientIndexError, searchIndividualAtClientIndexErrorData]);

  const callBookedAppointmentDetails = async (appointmentId) => {
    setAppointmentId(appointmentId);

    const response = await queryClient.fetchQuery({
      queryKey: APPOINTMENT_QUERY_KEY,
      queryFn: () => fetchAppointmentDetails(appointmentId),
    });

    return response;
  };

  const setBookingSummaryData = (appointmentDetails) => {
    if (!appointmentDetails) return;

    const { entry: bookedAppointmentData } = appointmentDetails || {};
    let headerSummary = new Map();

    let service = BookingUtility.getBookedService(bookedAppointmentData);
    setSelectedService(service);
    headerSummary.set(t('appointment'), service?.name);

    const location = BookingUtility.getBookedLocation(bookedAppointmentData);
    setSelectedLocation(location);
    headerSummary.set(t('location'), location?.resource?.name);

    const slotData = BookingUtility.getSlotData(bookedAppointmentData);
    const slotStartTime = convertTimeIntoTwelveHourFormatIgnoreZone(slotData?.resource?.start, TIME_FORMAT);
    const slotEndTime = convertTimeIntoTwelveHourFormatIgnoreZone(slotData?.resource?.end, TIME_FORMAT);
    const appointmentDate = BookingUtility.getScheduleDate(slotData?.resource?.start, DATE_FORMAT);
    const appointmentTime = `${slotStartTime} - ${slotEndTime}`;
    headerSummary.set(t('dateAndTime'), `${appointmentDate} <br /> ${appointmentTime}`);
    setHeaderSummary(headerSummary);
  };

  const setSlotAsCurrentPage = (isRescheduleAppointmentFlag) => {
    if (isRescheduleAppointmentFlag) {
      setFinalAction(finalActions.RESCHEDULE);
    } else {
      setFinalAction(finalActions.BOOKING);
    }
    setCurrentPage(pages.SLOT_BOOKING);
  };

  const handleSignInDialogState = (state) => {
    setIsSignInDialogOpen(state);
  };

  const handleIntroductionNavigation = (action) => {
    if (action === actions.NEXT) {
      setCurrentPage(pages.SERVICE);
    }
  };

  const handleServiceNavigation = (action, service) => {
    if (action === actions.NEXT) {
      let headerSummaryMap = headerSummary;
      if (services?.length > 1) {
        headerSummaryMap.set(t('appointment'), service?.name || service?.display);
        setSelectedService(service);
      } else {
        headerSummaryMap.set(t('appointment'), services[0]?.name);
        setSelectedService(services[0]);
      }
      setHeaderSummary(headerSummaryMap);
      if (isDemographicFromUrl) {
        if (showDemographicFromUrl) {
          setCurrentPage(pages.DEMOGRAPHIC);
        } else {
          handleDemographicNavigation(actions.NEXT, demographic);
        }
      } else {
        setCurrentPage(pages.DEMOGRAPHIC);
      }
    } else {
      dispatch(resetCheckEmail());
      setCurrentPage(pages.DEMOGRAPHIC);
    }
  };

  const handleDemographicNavigation = async (action, demographicFields) => {
    if (finalAction === finalActions.MODIFICATION) {
      setDemographic(demographicFields);
      const appointmentId = CommonUtility.getParamFromUrl('appointmentId');
      const response = await queryClient.fetchQuery({
        queryKey: APPOINTMENT_QUERY_KEY,
        queryFn: () => fetchAppointmentDetails(appointmentId),
      });

      if (response.status === 200) {
        setBookingSummaryData(response?.data);
        const { entry: bookedAppointmentData } = response.data || {};
        const appointmentStatus = BookingUtility.getAppointmentStatus(bookedAppointmentData);
        if (appointmentStatus === 'cancelled') {
          setCurrentPage(pages.CANCEL_APPOINTMENT_SUMMARY);
        } else {
          setCurrentPage(pages.BOOKING_SUMMARY);
        }
      }

      return;
    }
    if (action === actions.NEXT) {
      let headerSummaryMap = headerSummary;
      headerSummaryMap.set(
        t('individual'),
        demographicFields?.individuals?.map((individual) => `${individual.firstName} ${individual.lastName}`),
      );
      setHeaderSummary(headerSummaryMap);
      setDemographic(demographicFields);
      dispatch(resetUpdateConnectionAtClientIndex());
      const schedulerClientId = demographicFields?.clientIdentifiers?.find(
        (identifier) => identifier.type === 'SCHEDULER_CLIENT_ID',
      )?.value;
      if (schedulerClientId) {
        try {
          setLoading(true);
          const response = await getEligibility(selectedService?.id, schedulerClientId);
          setEligibilityData(response);
          setLoading(false);
        } catch (error) {
          setLoading(false);
          console.error('Error getting the eligibility');
        }
      }
      setCurrentPage(pages.LOCATION);
    } else if (action === actions.PREVIOUS) {
      setCurrentPage(pages.SERVICE);
    }
  };

  const handleConfirmationNavigation = (action) => {
    if (action === actions.NEXT) {
      setCurrentPage(pages.DEMOGRAPHIC);
    }
  };

  const handleLocationNavigation = (action, location) => {
    let headerSummaryMap = headerSummary;
    headerSummaryMap.set(t('location'), location?.resource?.name);
    setHeaderSummary(headerSummaryMap);
    setSelectedLocation(location);
    if (action === actions.NEXT) {
      setSlotAsCurrentPage(finalAction === finalActions.RESCHEDULE);
    }
  };

  const handleSlotBookingNavigation = (action, appointmentData) => {
    setSelectedSlot(appointmentData.slotData);
    let headerSummaryMap = headerSummary;
    headerSummaryMap.set(
      t('dateAndTime'),
      `${appointmentData?.appointmentDate} <br /> ${appointmentData?.appointmentTime}`,
    );
    setHeaderSummary(headerSummaryMap);
    if (action === actions.NEXT) {
      setCurrentPage(pages.REVIEW_BOOKING);
    }
  };

  const handleHeaderSummaryEditNavigation = (action, editType, demographicFields, isRescheduleAppointmentFlag) => {
    if (action === actions.PREVIOUS) {
      if (editType === t('individual')) {
        dispatch(resetCheckEmail());
        setCurrentPage(pages.DEMOGRAPHIC);
        setDemographic(demographicFields);
      } else if (editType === t('appointment')) {
        dispatch(resetCheckEmail());
        setCurrentPage(pages.SERVICE);
      } else if (editType === t('location')) {
        setCurrentPage(pages.LOCATION);
      } else if (editType === t('dateAndTime')) {
        setSlotAsCurrentPage(isRescheduleAppointmentFlag);
      }
    }
  };

  const handleCallSummaryAfterBookingReview = async (appointmentId) => {
    finalActions.BOOKING;
    const response = await callBookedAppointmentDetails(appointmentId);
    if (response?.data) {
      setBookingSummaryData(response?.data);
      setCurrentPage(pages.BOOKING_SUMMARY);
    }
  };

  const handleCallSummaryAfterRescheduleReview = async (appointmentId) => {
    setFinalAction(finalActions.RESCHEDULE_BOOKING_SUMMARY);
    const response = await callBookedAppointmentDetails(appointmentId);

    if (response?.data) {
      setBookingSummaryData(response?.data);
      setCurrentPage(pages.BOOKING_SUMMARY);
    }
  };

  const handleRescheduleAppointmentLinkNavigation = async () => {
    setFinalAction(finalActions.RESCHEDULE);
    if (appointmentData?.data) {
      setSlotAsCurrentPage(true);
      setBookingSummaryData(appointmentData?.data);
    } else {
      const response = await callBookedAppointmentDetails(appointmentId);

      if (response.data) {
        setBookingSummaryData(response?.data);
        setSlotAsCurrentPage(true);
      }
    }
  };

  const handleCancelAppointmentNavigation = () => {
    //setFinalAction(finalActions.CANCEL);
    //callBookedAppointmentDetails(appointmentId);
    //commented above 2 line as now cancellation is happening in bookingSummary page and this handler will be used to display cancel message
    setCurrentPage(pages.CANCEL_APPOINTMENT_SUMMARY);
  };

  const handleBackToAppointmentSummary = () => {
    setCurrentPage(pages.BOOKING_SUMMARY);
  };

  const handleRescheduleAppointment = () => {
    setSlotAsCurrentPage(true);
  };

  const redirectToSameBookingWidget = () => {
    CommonUtility.removeParams();
    BookingUtility.resetAllAPIReducersIncludingBooking(dispatch);
    resetState();
    if (currentPage === pages.RESCHEDULE_APPOINTMENT) {
      dispatch(
        fetchWidgetDetails({
          organizationId,
          widgetId: widgetId,
        }),
      );
    }
    setCurrentPage(introduction?.enabled ? pages.INTRODUCTION : getPageByBookingWidgetFlowType(displayEligibility));
  };

  const isActionEnabled = action?.enabled;

  const selectedServiceAction =
    action?.actionConditions?.find((condition) =>
      condition?.selectedServices?.find((service) => service?.name === selectedService?.name),
    ) || action?.actionConditions?.find((condition) => condition.default);

  const handleBookAgain = (page) => {
    if (action?.enabled === false || page !== pages.BOOKING_SUMMARY) {
      redirectToSameBookingWidget();
    } else if (action?.enabled) {
      setCurrentPage(pages.ACTION);
    } else {
      redirectToSameBookingWidget();
    }
  };

  const resetState = () => {
    setSelectedService(null);
    setSelectedLocation(null);
    setSelectedSlot(null);
    setIsDemographicFromUrl(false);
    setShowDemographicFromUrl(false);
    setRedirectBackToWidgetId(null);
    setDemographic(null);
    setAppointmentId(null);
    setFinalAction(finalActions.BOOKING);
    setHeaderSummary(new Map());
  };

  const getAppointmentTypeId = () => {
    return selectedService.id;
  };

  const getSlotId = () => {
    return selectedSlot.resource.id;
  };

  const getBookRescheduleAppointmentData = () => {
    const clientJson = CommonUtility.createClientInfoForAppointment(demographic);

    const data = {
      appointmentTypeId: getAppointmentTypeId(),
      client: clientJson,
      slotId: getSlotId(),
      widgetId: widgetId,
    };

    return data;
  };

  const getCancelData = (demographic) => {
    const clientJson = CommonUtility.createClientInfoForAppointment(demographic);
    return {
      clientId: getClientId(),
      client: clientJson,
    };
  };

  const getPage = () => {
    let page;
    if (isFreshRecordFetched && (widgetDetailsSuccessData || widgetDetailsErrorData)) {
      if (currentPage === pages.INTRODUCTION) {
        page = (
          <HeadingAndDescription
            handleNavigationCallback={(action) => handleIntroductionNavigation(action)}
            headingAndDescriptionData={introduction}
          />
        );
      } else if (currentPage === pages.SERVICE) {
        if (services.length > 1) {
          page = <Service handleNavigationCallback={(action, service) => handleServiceNavigation(action, service)} />;
        } else if (services.length === 1) {
          setSelectedService(services[0]);
          handleServiceNavigation(actions.NEXT);
        }
      } else if (currentPage === pages.HISTORY_AND_SERVICES) {
        page = (
          <HistoryAndServices
            handleNavigationCallback={(action, service) => handleServiceNavigation(action, service)}
            headerSummary={headerSummary}
            demographic={demographic}
            handleEditNavigationCallback={(action, editType, demographic) =>
              handleHeaderSummaryEditNavigation(action, editType, demographic)
            }
          />
        );
      } else if (currentPage === pages.DEMOGRAPHIC) {
        page = (
          <EditDemographic
            title={clientInformationPageTitle}
            subtitle={clientInformationPageSubtitle}
            fields={finalAction === finalActions?.MODIFICATION && matchingInfoOnly ? matchFields : fields}
            demographic={demographic}
            handleDemographicCreationCallback={handleDemographicNavigation}
            isMultipleIndividualEnabled={isMultipleIndividualEnabled}
            isConsentRequired={isConsentRequired}
            widgetType={widgetType}
            clientNotFoundPage={individualNotFoundPage}
            selfRegistration={finalAction === finalActions?.MODIFICATION ? false : selfRegistration}
            otpVerificationEnabled={otpVerificationEnabled}
            byRequestOnly={byRequestOnly}
            requestNotFoundPage={requestNotFoundPage}
          />
        );
      } else if (currentPage === pages.LOGIN) {
        page = <Login />;
      } else if (currentPage === pages.LOCATION) {
        page = (
          <Location
            handleEditNavigationCallback={(action, editType, demographic) =>
              handleHeaderSummaryEditNavigation(action, editType, demographic)
            }
            handleNavigationCallback={(action, location) => handleLocationNavigation(action, location)}
            selectedService={selectedService}
            headerSummary={headerSummary}
            demographic={demographic}
            isRescheduleAppointment={finalAction === finalActions.RESCHEDULE}
            searchedPlace={searchedPlace}
            setSearchedPlace={setSearchedPlace}
            locationDistanceMap={locationDistanceMap}
            setLocationDistanceMap={setLocationDistanceMap}
            smartDates={smartDates}
            smartDatesStatus={smartDatesStatus}
          />
        );
      } else if (currentPage === pages.SLOT_BOOKING) {
        page = (
          <SlotBooking
            handleNavigationCallback={(action, appointmentData) => handleSlotBookingNavigation(action, appointmentData)}
            handleEditNavigationCallback={(action, editType, demographic) =>
              handleHeaderSummaryEditNavigation(action, editType, demographic)
            }
            selectedService={selectedService}
            selectedLocation={selectedLocation}
            headerSummary={headerSummary}
            demographic={demographic}
            isRescheduleAppointment={finalAction === finalActions.RESCHEDULE}
            smartDates={smartDates}
            smartDatesStatus={smartDatesStatus}
          />
        );
      } else if (currentPage === pages.REVIEW_BOOKING) {
        page = (
          <BookingReview
            headerSummary={headerSummary}
            handleEditNavigationCallback={(action, editType, demographic, isRescheduleAppointmentFlag) =>
              handleHeaderSummaryEditNavigation(action, editType, demographic, isRescheduleAppointmentFlag)
            }
            callSummaryAfterBookingReviewCallback={handleCallSummaryAfterBookingReview}
            callSummaryAfterRescheduleReviewCallback={handleCallSummaryAfterRescheduleReview}
            appointmentPayload={getBookRescheduleAppointmentData()}
            demographic={demographic}
            appointmentId={appointmentId}
            isRescheduleAppointment={[finalActions.RESCHEDULE, finalActions.RESCHEDULE_BOOKING_SUMMARY].includes(
              finalAction,
            )}
            organizationId={organizationId}
            locationId={selectedLocation?.resource?.id}
          />
        );
      } else if (currentPage === pages.BOOKING_SUMMARY && appointmentData?.data) {
        page = (
          <BookingSummary
            appointmentId={appointmentId}
            demographic={demographic}
            selectedService={selectedService}
            selectedLocation={selectedLocation}
            getBookedAppointmentSuccess={appointmentData?.status === 200}
            getBookedAppointmentSuccessData={appointmentData?.data}
            rescheduleAppointmentCallback={(appointmentId) => handleRescheduleAppointmentLinkNavigation(appointmentId)}
            cancelAppointmentCallback={(appointmentId) => handleCancelAppointmentNavigation(appointmentId)}
            handleBookAgainCallback={() => handleBookAgain(pages.BOOKING_SUMMARY)}
            header={
              finalAction == finalActions.RESCHEDULE_BOOKING_SUMMARY
                ? t('rescheduleConfirmation')
                : t('appointmentDetails')
            }
            subHeader={finalAction == finalActions.RESCHEDULE_BOOKING_SUMMARY ? t('rescheduleMessage') : ''}
            isActionEnabled={isActionEnabled}
            matchedAction={selectedServiceAction}
            isActionPerformed={isActionPerformed}
            cancelPayload={getCancelData(demographic)}
            modificationFlag={finalAction == finalActions.MODIFICATION ? true : false}
          />
        );
      } else if (currentPage === pages.ACTION) {
        page = (
          <Action
            matchedAction={selectedServiceAction}
            demographic={demographic}
            appointmentId={appointmentId}
            handleNavigationCallback={handleBackToAppointmentSummary}
            setIsActionPerformed={setIsActionPerformed}
          />
        );
      } else if (currentPage === pages.CANCEL_APPOINTMENT_SUMMARY) {
        page = (
          <HeadingAndDescription
            headingAndDescriptionData={{
              heading: t('appointmentDetails'),
              description: appointmentCancellationMessage,
            }}
            showNextButton={false}
          />
        );
      } else if (currentPage === pages.RESCHEDULE_APPOINTMENT) {
        page = (
          <RescheduleAppointment
            headerSummary={headerSummary}
            handleNavigationCallback={() => handleBackToAppointmentSummary()}
            rescheduleAppointmentCallback={handleRescheduleAppointment}
            bookNewAppointmentCallback={() => handleBookAgain(pages.RESCHEDULE_APPOINTMENT)}
          />
        );
      } else if (currentPage === pages.CONFIRMATION) {
        const { confirmationPage = {} } = widgetDetailsSuccessData || {};

        page = (
          <HeadingAndDescription
            handleNavigationCallback={(action) => handleConfirmationNavigation(action)}
            headingAndDescriptionData={{
              heading: confirmationPage?.heading || t('confirmationPageHeading'),
              description: confirmationPage?.description || t('confirmationPageDescription'),
              buttonText: confirmationPage?.buttonText || t('next'),
              enabled: confirmationPage?.enabled,
            }}
          />
        );
      } else if (currentPage === pages.PAGE_NOT_FOUND) {
        page = <PageNotFound />;
      } else if (currentPage === pages.APPOINTMENT_NOT_FOUND) {
        const { appointmentNotFoundPage = {} } = widgetDetailsSuccessData || {};
        page = (
          <HeadingAndDescription
            headingAndDescriptionData={{
              heading: appointmentNotFoundPage?.heading || t('appointmentNotFoundHeading'),
              description: appointmentNotFoundPage?.description || t('appointmentNotFoundDescription'),
            }}
            showNextButton={false}
          />
        );
      }
    } else if (currentPage === pages.PAGE_NOT_FOUND) {
      page = <PageNotFound />;
    }
    return page;
  };

  return (
    <>
      <Loader active={isWidgetDetailsFetching || isGetIndividualDataFetching || getAppointmentLoading || loading} />
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', px: { xs: 1, md: '4.5%' }, mt: 0.5, gap: 0 }}>
        <LanguageSelector availableLanguages={availableLanguages} onLanguageChange={handleLanguageChange} />

        {showSignIn && (
          <SignInSignUp
            isSignInDialogOpen={isSignInDialogOpen}
            handleSignInDialogState={handleSignInDialogState}
            isConnectionRequired={isConnectionRequired}
          />
        )}
      </Box>
      {getPage()}
    </>
  );
};
