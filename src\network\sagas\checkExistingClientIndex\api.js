import { CHECK_EXISTING_CLIENT_INDEX } from '@/utils/constants/awsApiEndpoints';
import { makeNetworkCall } from '../..';
import { INDIVIDUAL_ID, ORGANIZATION_ID } from '@/utils/constants';

export function fetchCheckExistingClientIndex(action) {
  const { payload = {} } = action || {};
  const { headers = {}, organizationId, individualId } = payload || {};

  const URL = CHECK_EXISTING_CLIENT_INDEX.replace(ORGANIZATION_ID, organizationId).replace(INDIVIDUAL_ID, individualId);

  const config = {
    method: 'GET',
    url: URL,
    headers: headers,
    // data: checkEmailData,
  };

  return makeNetworkCall(config);
}
