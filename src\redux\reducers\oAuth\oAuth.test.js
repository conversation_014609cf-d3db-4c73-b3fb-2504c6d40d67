import { OAUTH_REQUEST, OAUTH_ERROR, OAUTH_SUCCESS, OAUTH_RESET } from '../../actions/oAuth';
import * as GLOBALS from '../globals';
import { oAuthReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isOauthFetching: true,
  fetchOauthSuccess: false,
  fetchOauthError: false,
  oauthErrorData: null,
};

const successState = {
  isOauthFetching: false,
  fetchOauthSuccess: true,
  fetchOauthError: false,
  oauthErrorData: null,
  oauthSuccessData: data,
};

const errorState = {
  isOauthFetching: false,
  fetchOauthSuccess: false,
  fetchOauthError: true,
  oauthErrorData: data,
};

const resetState = {
  isOauthFetching: false,
  fetchCareSuccess: false,
  fetchCareError: false,
  careErrorData: null,
  oauthSuccessData: null,
};

describe('Oauth Booking Details Reducer', () => {
  it('should return the initial state', () => {
    expect(oAuthReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle OAUTH_REQUEST', () => {
    expect(
      oAuthReducer(initialState, {
        type: OAUTH_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle OAUTH_SUCCESS', () => {
    expect(
      oAuthReducer(initialState, {
        type: OAUTH_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle OAUTH_ERROR', () => {
    expect(
      oAuthReducer(initialState, {
        type: OAUTH_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle OAUTH_RESET', () => {
    expect(
      oAuthReducer(initialState, {
        type: OAUTH_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
