import { call, put, takeEvery } from 'redux-saga/effects';
import {
  CHECK_EXISTING_CONNECTION_INDEX_REQUEST,
  CHECK_EXISTING_CONNECTION_INDEX_SUCCESS,
  CHECK_EXISTING_CONNECTION_INDEX_ERROR,
} from '../../../redux/actions/checkExistingConnectionIndex';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { fetchCheckExistingConnectionIndex } from './api';

export function* checkExistingConnectionIndex(action) {
  try {
    const response = yield call(fetchCheckExistingConnectionIndex, action);
    const { data = {} } = response;

    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: CHECK_EXISTING_CONNECTION_INDEX_SUCCESS, payload: data });
    } else {
      yield put({ type: CHECK_EXISTING_CONNECTION_INDEX_ERROR, payload: { statusCode: response.status, ...data } });
    }
  } catch (error) {
    yield put({ type: CHECK_EXISTING_CONNECTION_INDEX_ERROR });
  }
}

export function* watchCheckExistingConnectionIndex() {
  yield takeEvery(CHECK_EXISTING_CONNECTION_INDEX_REQUEST, checkExistingConnectionIndex);
}
