/*
 * Get Questions action types
 */

export const GET_QUESTIONNAIRE_REQUEST = 'GET_QUESTIONNAIRE_REQUEST';
export const GET_QUESTIONNAIRE_SUCCESS = 'GET_QUESTIONNAIRE_SUCCESS';
export const GET_QUESTIONNAIRE_ERROR = 'GET_QUESTIONNAIRE_ERROR';
export const GET_QUESTIONNAIRE_RESET = 'GET_QUESTIONNAIRE_RESET';
/*
 * action creators
 */

export function fetchQuestionnaireQuestions(data) {
  return {
    type: GET_QUESTIONNAIRE_REQUEST,
    payload: data,
  };
}

export function fetchQuestionnaireQuestionsSuccess(data) {
  return {
    type: GET_QUESTIONNAIRE_SUCCESS,
    payload: data,
  };
}

export function fetchQuestionnaireQuestionsError() {
  return {
    type: GET_QUESTIONNAIRE_ERROR,
  };
}

export function fetchQuestionnaireQuestionsReset() {
  return {
    type: GET_QUESTIONNAIRE_RESET,
  };
}
