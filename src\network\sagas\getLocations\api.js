import { makeNetworkCall } from '../..';
import { ORGANIZATION_ID } from '../../../utils/constants';
import { GET_LOCATIONS, CAMBIAN_API_BASE_URL, CAMBIAN_WIDGET_APIS_REGISTRY } from '@/utils/constants/awsApiEndpoints';

export function makeApiFetchLocations(action) {
  const { payload = {} } = action || {};
  const { headers = {}, organizationId } = payload || {};

  const mainUrl = GET_LOCATIONS.replace(ORGANIZATION_ID, organizationId);

  const config = {
    method: 'GET',
    url: mainUrl,
    headers: headers,
    formData: false,
  };

  return makeNetworkCall(config);
}
