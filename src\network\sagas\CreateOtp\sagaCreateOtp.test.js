import { runSaga } from 'redux-saga';
import * as api from './api';
import { createOtp } from './index';
import * as actions from '../../../redux/actions/CreateOtp/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

/**
 * This function test test case for create otp details saga
 * Fires create otp success of api gives success
 * Fires create otp error of api fails
 */

describe('Create OTP', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiCreateOtp = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.createOtp(DUMMY_ITEM.data);
    await runSaga(fakeStore, createOtp, requestResult).done;

    let successResult = actions.createOtpSuccess(DUMMY_ITEM.data);

    expect(api.makeApiCreateOtp.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiCreateOtp = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.createOtp(DUMMY_ITEM.data);
    await runSaga(fakeStore, createOtp, requestResult).done;

    expect(api.makeApiCreateOtp.mock.calls.length).toBe(1);

    let errorResult = actions.createOtpError(DUMMY_ITEM.data);
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch reset action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiCreateOtp = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.createOtp(DUMMY_ITEM.data);
    await runSaga(fakeStore, createOtp, requestResult).done;

    expect(api.makeApiCreateOtp.mock.calls.length).toBe(1);

    let resetResult = actions.resetOtp(DUMMY_ITEM.data);
    const expectedAction = {
      type: actions.CREATE_OTP_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
