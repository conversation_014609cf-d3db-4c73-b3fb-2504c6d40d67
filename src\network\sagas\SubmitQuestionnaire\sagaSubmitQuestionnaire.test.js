import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/SubmitQuestionnaire/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { fetchQuestionnaireSubmitAnswers } from './index';
/**
 * This function test test case for get search location details saga
 * Fires get search location success of api gives success
 * Fires get search location error of api fails
 */

describe('get submit questtionaire ', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiFetchQuestionnaireSubmitAnswers = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchQuestionnaireSubmitAnswers(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchQuestionnaireSubmitAnswers, requestResult).done;

    let successResult = actions.fetchQuestionnaireSubmitAnswersSuccess(DUMMY_ITEM.data);

    expect(api.makeApiFetchQuestionnaireSubmitAnswers.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchQuestionnaireSubmitAnswers = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchQuestionnaireSubmitAnswers(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchQuestionnaireSubmitAnswers, requestResult).done;

    expect(api.makeApiFetchQuestionnaireSubmitAnswers.mock.calls.length).toBe(1);

    let errorResult = actions.fetchQuestionnaireSubmitAnswersError();
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchQuestionnaireSubmitAnswers = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchQuestionnaireSubmitAnswers(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchQuestionnaireSubmitAnswers, requestResult).done;

    expect(api.makeApiFetchQuestionnaireSubmitAnswers.mock.calls.length).toBe(1);

    let resetResult = actions.fetchQuestionnaireSubmitAnswersReset();
    const expectedAction = {
      type: actions.POST_QUESTIONNAIRE_QUESTIONS_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
