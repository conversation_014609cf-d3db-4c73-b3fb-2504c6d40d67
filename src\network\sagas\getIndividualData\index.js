import { call, put, takeEvery } from 'redux-saga/effects';
import {
  GET_INDIVIDUAL_DATA_REQUEST,
  GET_INDIVIDUAL_DATA_SUCCESS,
  GET_INDIVIDUAL_DATA_ERROR,
} from '@/redux/actions/getIndividualData';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { getIndividualDataApi } from './api';

export function* fetchGetIndividualData(action) {
  try {
    const response = yield call(getIndividualDataApi, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: GET_INDIVIDUAL_DATA_SUCCESS, payload: data });
    } else {
      yield put({ type: GET_INDIVIDUAL_DATA_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: GET_INDIVIDUAL_DATA_ERROR, payload: error });
  }
}

export function* watchGetIndividualData() {
  yield takeEvery(GET_INDIVIDUAL_DATA_REQUEST, fetchGetIndividualData);
}
