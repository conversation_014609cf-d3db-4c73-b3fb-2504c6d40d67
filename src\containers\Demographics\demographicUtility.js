import * as CommonUtility from '../commonUtility';

export const getAddBookingDemographicData = (demographic, clientGroupId, services, displayEligibility) => {
  const [organizationId, widgetId] = CommonUtility.getOrganizationAndWidgetId();

  const data = {
    resourceType: 'Parameters',
    parameter: [
      {
        name: 'patient',
        resource: {
          resourceType: 'Patient',
          active: true,
          identifier: [
            {
              system: demographic.individuals[0].phnType || '',
              type: {
                text: 'phn',
              },
              value: demographic.individuals[0].personalHealthNumber,
            },
          ],
          name: [
            {
              family: demographic.individuals[0].lastName,
              given: [demographic.individuals[0].firstName],
            },
          ],
          telecom: [
            {
              system: 'email',
              value: demographic.contact.email,
              use: 'home',
              rank: demographic.preferredContactMethod === 'Email' ? 1 : 2,
            },
            {
              system: 'phone',
              value: demographic.contact.phone,
              use: 'mobile',
              rank: demographic.preferredContactMethod === 'Phone' ? 1 : 2,
            },
          ],
          gender: demographic.individuals[0].gender,
          birthDate: demographic.individuals[0].dateOfBirth,
          communication: [
            {
              language: {
                coding: [
                  {
                    system: 'urn:ietf:bcp:47',
                    code: 'en-CA',
                    display: 'English (Canada)',
                  },
                ],
                text: 'English (Canada)',
              },
              preferred: true,
            },
          ],
        },
      },
      {
        name: 'groupId',
        valueString: clientGroupId,
      },
      {
        name: 'organization',
        valueString: organizationId,
      },
      {
        name: 'widget',
        valueString: widgetId,
      },
      {
        name: 'uniquekey',
        valueString: widgetId,
      },
    ],
  };

  if (displayEligibility) {
    data.parameter.push({
      name: 'services',
      part: services.map((service) => {
        return {
          name: service.display,
          valueString: service.code,
        };
      }),
    });
  }

  return data;
};

export const getAwsAddBookingDemographicData = (demographic, clientGroupId, services, displayEligibility) => {
  const data = {
    firstName: demographic.individuals[0].firstName,
    lastName: demographic.individuals[0].lastName,
    birthDate: demographic.individuals[0].dateOfBirth,
    phnType: demographic.individuals[0].phnType || '',
    phn: demographic.individuals[0].personalHealthNumber,
    alternateId: '123',
    externalId: '321',
    email: demographic.contact.email,
    phone: demographic.contact.phone,
    gender: demographic.individuals[0].gender,
    inactive: false,
    preferredContact: demographic.preferredContactMethod,
  };
  return data;
};

export const getAddQuestionnaireDemographicData = (demographic, clientGroupId) => {
  const [organizationId, widgetId] = CommonUtility.getOrganizationAndWidgetId();

  const data = {
    resourceType: 'Parameters',
    parameter: [
      {
        name: 'patient',
        resource: {
          resourceType: 'Patient',
          active: true,
          identifier: [
            {
              system: demographic.individuals[0].phnType || '',
              type: {
                text: 'phn',
              },
              value: demographic.individuals[0].personalHealthNumber,
            },
          ],
          name: [
            {
              family: demographic.individuals[0].lastName,
              given: [demographic.individuals[0].firstName],
            },
          ],
          telecom: [
            {
              system: 'email',
              value: demographic.contact.email,
              use: 'home',
              rank: demographic.preferredContactMethod === 'Email' ? 1 : 2,
            },
            {
              system: 'phone',
              value: demographic.contact.phone,
              use: 'mobile',
              rank: demographic.preferredContactMethod === 'Phone' ? 1 : 2,
            },
          ],
          gender: demographic.individuals[0].gender,
          birthDate: demographic.individuals[0].dateOfBirth,
          communication: [
            {
              language: {
                coding: [
                  {
                    system: 'urn:ietf:bcp:47',
                    code: 'en-CA',
                    display: 'English (Canada)',
                  },
                ],
                text: 'English (Canada)',
              },
              preferred: true,
            },
          ],
        },
      },
      {
        name: 'groupId',
        valueString: clientGroupId,
      },
      {
        name: 'organization',
        valueString: organizationId,
      },
      {
        name: 'widget',
        valueString: widgetId,
      },
      {
        name: 'uniquekey',
        valueString: widgetId,
      },
    ],
  };

  return data;
};

export const getCheckEmailData = (contactType, contactValue, questionnaireWidgetName) => {
  const [organizationId, widgetId] = CommonUtility.getOrganizationAndWidgetId();
  let data = [
    {
      name: 'contactType',
      valueString: contactType,
    },
    {
      name: 'contactValue',
      valueString: contactValue,
    },
    {
      name: 'widgetName',
      valueString: 'questionnaire',
    },
    {
      name: 'serviceName',
      valueString: questionnaireWidgetName,
    },
    {
      name: 'organization',
      valueString: organizationId,
    },
    {
      name: 'widget',
      valueString: widgetId,
    },
    {
      name: 'uniqueKey',
      valueString: 'abcdefg',
    },
  ];
  let obj = { resourceType: 'Parameters', parameter: data };
  return obj;
};

export const getValidateEmailCodeData = (contactType, contactValue, otp) => {
  const [organizationId, widgetId] = CommonUtility.getOrganizationAndWidgetId();
  let data = [
    {
      name: 'contactType',
      valueString: contactType,
    },
    {
      name: 'contactValue',
      valueString: contactValue,
    },
    {
      name: 'otp',
      valueString: otp,
    },
    {
      name: 'organization',
      valueString: organizationId,
    },
    {
      name: 'widget',
      valueString: widgetId,
    },
    {
      name: 'uniqueKey',
      valueString: 'abcdefg',
    },
  ];
  let obj = { resourceType: 'Parameters', parameter: data };
  return obj;
};

export const sortFields = (a, b) => {
  if (a.position < b.position) {
    return -1;
  }
  if (a.position > b.position) {
    return 1;
  }
  return 0;
};

export const getDemographicFromUrl = (fields) => {
  const availableFields = fields?.map((field) => field.code);

  let demographic = null;
  let individuals = [];
  let individual = {
    id: CommonUtility.getParamFromUrl('id'),
    resourceType: CommonUtility.getParamFromUrl('resourceType'),
    firstName: availableFields?.includes('FIRST_NAME') ? CommonUtility.getParamFromUrl('firstName') : '',
    lastName: availableFields?.includes('LAST_NAME') ? CommonUtility.getParamFromUrl('lastName') : '',
    gender: availableFields?.includes('GENDER') ? CommonUtility.getParamFromUrl('gender') : '',
    dateOfBirth: availableFields?.includes('DATE_OF_BIRTH') ? CommonUtility.getParamFromUrl('dateOfBirth') : '',
    personalHealthNumber:
      availableFields?.includes('PHN') || availableFields?.includes('HEALTH_CARD')
        ? CommonUtility.getParamFromUrl('personalHealthNumber')
        : '',
    address: availableFields?.includes('ADDRESS') ? CommonUtility.getParamFromUrl('address') : '',
    city: availableFields?.includes('CITY') ? CommonUtility.getParamFromUrl('city') : '',
    province: availableFields?.includes('PROVINCE') ? CommonUtility.getParamFromUrl('province') : '',
    country: availableFields?.includes('COUNTRY') ? CommonUtility.getParamFromUrl('country') : '',
    postalCode: availableFields?.includes('POSTAL_CODE') ? CommonUtility.getParamFromUrl('postalCode') : '',
    email: availableFields?.includes('EMAIL') ? CommonUtility.getParamFromUrl('email') : '',
    phone: availableFields?.includes('PHONE') ? CommonUtility.getParamFromUrl('phone') : '',
  };
  individuals.push(individual);
  demographic = {
    id: individuals[0].id,
    resourceType: individuals[0].resourceType,
    individuals: individuals,
    contact: {
      email: individuals[0].email,
      phone: individuals[0].phone,
      city: individuals[0].city,
      province: individuals[0].province,
      country: individuals[0].country,
      postalCode: individuals[0].postalCode,
      address: individuals[0].address,
    },
    preferredContactMethod: CommonUtility.getParamFromUrl('preferredContactMethod') || '',
  };
  return demographic;
};

export const getDemographicOfLoggedInUser = (data) => {
  let demographic = null;
  let individual = {
    id: data.self_patient_id || '',
    firstName: data.given_name || '',
    lastName: data.family_name || '',
    gender: data.gender || '',
    dateOfBirth: data.birthdate || '',
    personalHealthNumber: data.healthcare_id_value || '',
  };
  let contact = {
    address: `${data.address1 ? data.address1 + ', ' : ''}${data.address2 ? data.address2 + ', ' : ''}${
      data.city ? data.city + ', ' : ''
    }${data.country ? data.country + ', ' : ''}${data.postalCode || ''}`,
    email: data.email || '',
    phone: data.phone_number || '',
  };
  demographic = {
    id: data.self_patient_id,
    // resourceType: 'Parameters',
    individuals: [individual],
    contact,
  };
  return demographic;
};

// this function is to get user demographics from SignIn API integrated with dialog and different from above function
export const getSignedIndividualUserDemographic = (existingDemographics, userInfo, fields = []) => {
  let demographic = { individuals: [{}], contact: {} };

  if (existingDemographics) {
    demographic = { ...existingDemographics };
    if (!demographic.individuals || !demographic.individuals.length) {
      demographic.individuals = [{}];
    }
    if (!demographic.contact) {
      demographic.contact = {};
    }
  }

  const fieldCodeMap = fields.reduce((map, field) => {
    map[field.code] = true;
    return map;
  }, {});

  let individual = {
    firstName: null,
    middleName: null,
    lastName: null,
    gender: null,
    dateOfBirth: null,
    healthCareIds: [],
  };

  if (fieldCodeMap.FIRST_NAME) {
    individual.firstName = userInfo?.firstName || existingDemographics?.individuals?.[0]?.firstName || '';
  }

  if (fieldCodeMap.MIDDLE_NAME) {
    individual.middleName = userInfo?.middleName || existingDemographics?.individuals?.[0]?.middleName || '';
  }

  if (fieldCodeMap.LAST_NAME) {
    individual.lastName = userInfo?.lastName || existingDemographics?.individuals?.[0]?.lastName || '';
  }

  if (fieldCodeMap.GENDER) {
    individual.gender = userInfo?.gender || existingDemographics?.individuals?.[0]?.gender || '';
  }

  if (fieldCodeMap.DATE_OF_BIRTH) {
    individual.dateOfBirth = userInfo?.dateOfBirth || existingDemographics?.individuals?.[0]?.dateOfBirth || '';
  }

  if (fieldCodeMap.IDENTIFICATION) {
    individual.healthCareIds = userInfo?.healthCareIds?.length
      ? userInfo?.healthCareIds
      : existingDemographics?.individuals?.[0]?.healthCareIds?.length
        ? existingDemographics?.individuals?.[0]?.healthCareIds
        : [];
  }

  demographic.individuals[0] = individual;

  let contact = {
    emailAddresses: [],
    phoneNumbers: [],
    addresses: [],
  };

  if (fieldCodeMap.EMAIL) {
    if (userInfo?.email) {
      contact.emailAddresses = [{ emailAddress: userInfo.email, primary: true }];
    } else if (userInfo?.emailAddresses?.length) {
      contact.emailAddresses = userInfo.emailAddresses;
    } else if (existingDemographics?.contact?.emailAddresses?.length) {
      contact.emailAddresses = existingDemographics.contact.emailAddresses;
    } else if (existingDemographics?.contact?.email) {
      contact.emailAddresses = [{ emailAddress: existingDemographics.contact.email, primary: true }];
    }
  }

  if (fieldCodeMap.PHONE) {
    contact.phoneNumbers = userInfo?.phoneNumbers?.length
      ? userInfo.phoneNumbers
      : existingDemographics?.contact?.phoneNumbers?.length
        ? existingDemographics.contact.phoneNumbers
        : [];
  }

  if (fieldCodeMap.ADDRESS) {
    contact.addresses = userInfo?.addresses?.length
      ? userInfo.addresses
      : existingDemographics?.contact?.addresses?.length
        ? existingDemographics.contact.addresses
        : [];
  }
  demographic.contact = contact;
  demographic.preferredContactMethod = null;
  demographic.subscribeToNotifications = null;
  if (fieldCodeMap.PREFERRED_CONTACT_METHOD) {
    demographic.preferredContactMethod =
      userInfo?.preferredContactMethod ||
      userInfo?.preferredContactMechanism ||
      existingDemographics?.preferredContactMethod ||
      '';
  }

  if (fieldCodeMap.NOTIFICATIONS) {
    demographic.subscribeToNotifications =
      userInfo?.subscribeToNotifications !== undefined && userInfo?.subscribeToNotifications !== null
        ? userInfo.subscribeToNotifications
        : existingDemographics?.subscribeToNotifications !== undefined &&
            existingDemographics?.subscribeToNotifications !== null
          ? existingDemographics.subscribeToNotifications
          : true;
  }
  demographic.id = userInfo?.individualId || userInfo?.cambianId || demographic.id || '';

  return demographic;
};

export const getExistingConnectionDemographicAtClientIndex = (existingDemographics, connectedClientData) => {
  let demographic = existingDemographics ? { ...existingDemographics } : { individuals: [], contact: {} };

  let individual = {
    firstName: connectedClientData?.firstName || existingDemographics?.individuals[0]?.firstName || '',
    middleName: connectedClientData?.middleName || existingDemographics?.individuals[0]?.middleName || '',
    lastName: connectedClientData?.lastName || existingDemographics?.individuals[0]?.lastName || '',
    gender:
      connectedClientData?.gender ||
      (existingDemographics?.individuals && existingDemographics?.individuals[0]?.gender) ||
      '',
    dateOfBirth:
      connectedClientData?.dateOfBirth ||
      (existingDemographics?.individuals && existingDemographics?.individuals[0]?.dateOfBirth) ||
      '',
    healthCareIds: connectedClientData?.healthCareIds?.length
      ? connectedClientData.healthCareIds
      : existingDemographics?.individuals[0]?.healthCareIds?.length
        ? existingDemographics?.individuals[0]?.healthCareIds
        : [],
  };

  let contact = {
    emailAddresses: connectedClientData?.emailAddresses?.length
      ? connectedClientData.emailAddresses
      : [{ emailAddress: existingDemographics?.contact?.email, primary: true }] || [],
    phoneNumbers: connectedClientData?.phoneNumbers?.length
      ? connectedClientData.phoneNumbers
      : existingDemographics?.contact?.phoneNumbers?.length
        ? existingDemographics.contact.phoneNumbers
        : [{ phoneNumber: existingDemographics?.contact?.phone, primary: true }] || [],
    addresses: connectedClientData?.addresses?.length
      ? connectedClientData.addresses
      : existingDemographics?.contact?.addresses?.length
        ? existingDemographics?.contact?.addresses
        : [],
  };

  demographic.individuals[0] = individual;
  demographic.contact = contact;
  demographic.preferredContactMethod =
    connectedClientData?.preferredContactMechanism || existingDemographics?.preferredContactMethod || '';
  demographic.id = connectedClientData?.cambianId || existingDemographics?.id || '';

  return demographic;
};
