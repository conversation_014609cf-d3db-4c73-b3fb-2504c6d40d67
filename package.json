{"name": "widgets", "version": "2.0.0", "private": true, "engines": {"node": ">=20.0.0", "npm": "please use YARN", "yarn": ">= 1.22.18"}, "scripts": {"dev:appscoop": "cross-env APP_ENV=appscoop BUILD_ENV=development next dev -p 3050", "dev:shared": "cross-env APP_ENV=shared BUILD_ENV=development next dev -p 3050", "dev:cambian": "cross-env APP_ENV=cambian BUILD_ENV=development next dev -p 3050", "dev:pcn": "cross-env APP_ENV=pcn BUILD_ENV=development next dev -p 3050", "test:pcn": "cross-env APP_ENV=pcn BUILD_ENV=test next dev -p 3050", "local:pcn": "cross-env APP_ENV=pcn BUILD_ENV=local next dev -p 3050", "local:cambian": "cross-env APP_ENV=cambian BUILD_ENV=local next dev -p 3050", "build": "next build", "build:standalone": "NEXT_OUTPUT_STANDALONE=true next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"./src/**/*.{js,jsx,ts,tsx,css,}\" --config ./.prettierrc"}, "dependencies": {"@appscoopsolutions/component-questionnaire": "^0.1.56", "@appscoopsolutions/component-ui": "^0.0.69", "@aws-sdk/client-cognito-identity": "^3.461.0", "@aws-sdk/client-cognito-identity-provider": "^3.461.0", "@date-io/date-fns": "^2.13.1", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.15", "@mui/material": "^5.15.15", "@mui/styles": "^5.15.15", "@mui/x-data-grid": "^7.2.0", "@tanstack/react-query": "^5.62.8", "axios": "^1.6.7", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "google-maps-react": "^2.0.6", "i18next": "^21.6.16", "i18next-browser-languagedetector": "^6.1.4", "i18next-http-backend": "^1.4.0", "libphonenumber-js": "^1.12.10", "material-ui-phone-number": "^3.0.0", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "next": "^14.1.2", "next-auth": "^4.24.5", "notistack": "^2.0.3", "pdfobject": "^2.2.8", "react": "18.2.0", "react-dom": "18.2.0", "react-google-autocomplete": "^2.6.1", "react-i18next": "^11.16.7", "react-imask": "^7.1.3", "react-phone-input-2": "^2.15.1", "react-qr-code": "^2.0.18", "react-redux": "^7.2.6", "react-to-print": "^2.14.4", "redux": "^4.1.2", "redux-persist": "^6.0.0", "redux-saga": "^1.1.3", "server-only": "^0.0.1", "uuid": "^9.0.1", "yup": "^0.32.11"}, "devDependencies": {"cross-env": "^7.0.3", "dotenv-expand": "^11.0.6", "dotenv-flow": "^4.1.0", "eslint": "8.49.0", "eslint-config-next": "13.5.1", "prettier": "3.1.0"}, "resolutions": {"@mui/x-data-grid": "^7.2.0"}}