import { APPOINTMENT_ID, ORGANIZATION_ID, WIDGET_ID } from '@/utils/constants';

export const ORGANIZATION_USER = 'ORGANIZATION_USER';

export const CAMBIAN_LOGIN_URL = process.env.REACT_APP_CAMBIAN_LOGIN_URL;

export const BASIC_TOKEN = 'd2lkZ2V0OnNlY3JldA==';
export const TOKEN = '2e624496-08d8-4750-9f68-2ec63e5d81d9';
export const BOOKING_WIDGET = 'Booking Widget';
export const QUESTIONNAIRE_WIDGET = 'Questionnaire Widget';
export const REGISTRATION_WIDGET = 'Registration Widget';
export const REDIRECT_URL = 'Redirect URL';
export const REDIRECT_TO_URL = 'URL';
export const REDIRECT_TO_WIDGET = 'Widget';
export const PAGE = 'Page';
export const REDIRECT_TO_ANOTHER_WIDGET_IN_IFRAME = 'Redirect to another widget in IFrame';
export const CALL_BACKGROUND_SERVICE = 'Service';
export const OPEN_IN_NEW_TAB = 'Open in new Tab';
export const open_in_new_tab = 'open in new tab';
export const OPEN_IN_SAME_TAB = 'Open in same Tab';
export const open_in_same_tab = 'open in same tab';
export const OPEN_IN_SAME_IFRAME = 'Open in same IFrame';
export const IDENTIFIED = 'IDENTIFIED';
export const DEIDENTIFIED = 'DEIDENTIFIED';
export const UNIDENTIFIED = 'UNIDENTIFIED';
export const FOUND = 'FOUND';
export const NOT_FOUND = 'NOT_FOUND';
export const MANAGE_NOTIFICATION_ROUTE = `/widget/organizations/${ORGANIZATION_ID}/bookingWidget/${WIDGET_ID}/manageNotification?appointmentId=${APPOINTMENT_ID}`;
export const INACTIVITY_TIMEOUT = 24 * 60 * 60 * 1000;

// TODO: Might move it to new dedicated file for react-query
// react-query queries
export const APPOINTMENT_QUERY_KEY = ['appointmentDetails'];
export const ORGANIZATION_QUERY_KEY = ['organizationDetails'];
export const REPORT_SETTINGS_QUERY_KEY = ['reportSettings'];
export const DOWNLOAD_HTML_REPORT_QUERY_KEY = ['downloadHtmlReport'];
export const GET_ORG_REQUEST_QUERY_KEY = ['getOrgRequest'];

export const CURRENT_DATE = (() => {
  const pad = (num) => num.toString().padStart(2, '0');
  const now = new Date();
  const year = now.getFullYear();
  const month = pad(now.getMonth() + 1);
  const day = pad(now.getDate());
  return `${year}-${month}-${day}`;
})();

export const identifications = {
  IDENTIFIED,
  DEIDENTIFIED,
  UNIDENTIFIED,
};

export const redirectTargets = {
  SAME_TAB: 'Same Tab',
  SAME_IFRAME: 'Same IFrame',
  NEW_TAB: 'New Tab',
  NEW_WINDOW: 'New Window',
};

export const actions = {
  PREVIOUS: 'PREVIOUS',
  NEXT: 'NEXT',
  FINISH: 'FINISH',
  CANCEL: 'CANCEL',
  EDIT: 'EDIT',
};

export const otpContactType = {
  EMAIL: 'EMAIL',
  CELL_PHONE: 'CELL_PHONE',
};

export const requestStatuses = {
  VALID: 'VALID',
  INVALID: 'INVALID',
};
