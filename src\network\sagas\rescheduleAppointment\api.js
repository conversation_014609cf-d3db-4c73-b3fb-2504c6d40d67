import { RESCHEDULE_APPOINTMENT } from '@/utils/constants/awsApiEndpoints';
import { makeNetworkCall } from '../..';
import { APPOINTMENT_ID, LOCATION_ID, ORGANI<PERSON>ATION_ID } from '../../../utils/constants';

export function rescheduleAppointmentApi(action) {
  const { payload = {} } = action || {};
  const { headers = {}, rescheduleData = {}, appointmentId, organizationId, locationId } = payload || {};

  const URL = RESCHEDULE_APPOINTMENT.replace(ORGANIZATION_ID, organizationId)
    .replace(LOCATION_ID, locationId)
    .replace(APPOINTMENT_ID, appointmentId);

  const config = {
    method: 'PUT',
    url: URL,
    headers: headers,
    data: rescheduleData,
  };

  return makeNetworkCall(config);
}
