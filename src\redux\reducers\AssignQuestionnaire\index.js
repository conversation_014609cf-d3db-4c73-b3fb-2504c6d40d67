import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  ASSIGN_QUESTIONNAIRE_REQUEST,
  ASSIGN_QUESTIONNAIRE_SUCCESS,
  ASSIGN_QUESTIONNAIRE_ERROR,
  ASSIGN_QUESTIONNAIRE_RESET,
} from '../../actions/AssignQuestionnaire';
export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};
export const assignQuestionnaireReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case ASSIGN_QUESTIONNAIRE_REQUEST:
      return {
        ...state,
        assignQuestionnaireFetching: true,
        assignQuestionnaireSuccess: false,
        assignQuestionnaireError: false,
        assignQuestionnaireErrorData: null,
      };
    case ASSIGN_QUESTIONNAIRE_SUCCESS: {
      return {
        ...state,
        assignQuestionnaireFetching: false,
        assignQuestionnaireSuccess: true,
        assignQuestionnaireError: false,
        assignQuestionnaireErrorData: null,
        assignQuestionnaireSuccessData: payload,
      };
    }
    case ASSIGN_QUESTIONNAIRE_ERROR:
      return {
        ...state,
        assignQuestionnaireFetching: false,
        assignQuestionnaireSuccess: false,
        assignQuestionnaireError: true,
        assignQuestionnaireErrorData: payload,
      };
    case ASSIGN_QUESTIONNAIRE_RESET:
      return {
        ...state,
        assignQuestionnaireFetching: false,
        assignQuestionnaireSuccess: false,
        assignQuestionnaireError: false,
        assignQuestionnaireErrorData: null,
        assignQuestionnaireSuccessData: null,
      };
    default:
      return state;
  }
};
