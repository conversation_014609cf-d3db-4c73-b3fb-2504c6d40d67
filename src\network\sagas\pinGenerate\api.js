import { makeNetworkCall } from '../..';
import { BASE_URL, UPDATE_QUESTIONNAIRE_RESPONSE } from '../../../utils/constants/apiEndpoints';

import { ORGANIZATION_ID, QUESTIONNAIRE_ID, QUESTIONNAIRE_RESPONSE_ID } from '../../../utils/constants';

export function makeApiFetchPinGenerate(action) {
  const { payload = {} } = action || {};
  const {
    headers = {},
    patientData,
    questionnaireResponseId,
    organizationId,
    questionnaireId,
    bookingAppointmentId,
  } = payload || {};

  let mainUrl =
    BASE_URL +
    UPDATE_QUESTIONNAIRE_RESPONSE.replace(QUESTIONNAIRE_RESPONSE_ID, questionnaireResponseId)
      .replace(ORGANIZATION_ID, organizationId)
      .replace(QUESTIONNAIRE_ID, questionnaireId);

  if (bookingAppointmentId) {
    mainUrl = mainUrl + `&appointmentId=${bookingAppointmentId}`;
  }

  const config = {
    method: 'PUT',
    url: mainUrl,
    headers: headers,
    data: patientData,
  };
  return makeNetworkCall(config);
}
