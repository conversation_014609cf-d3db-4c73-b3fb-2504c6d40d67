import { runSaga } from 'redux-saga';
import * as api from './api';
import { changeStatus } from './index';
import * as actions from '../../../redux/actions/changeStatus/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

/**
 * This function test test case for get booking slots details saga
 * Fires get booking slots success of api gives success
 * Fires get booking slots error of api fails
 */

describe('Activate Patient', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiChangeStatus = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.changeStatus(DUMMY_ITEM.data);
    await runSaga(fakeStore, changeStatus, requestResult).done;

    let successResult = actions.changeStatusSuccess(DUMMY_ITEM.data);

    expect(api.makeApiChangeStatus.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiChangeStatus = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.changeStatus(DUMMY_ITEM.data);
    await runSaga(fakeStore, changeStatus, requestResult).done;

    expect(api.makeApiChangeStatus.mock.calls.length).toBe(1);

    let errorResult = actions.changeStatusError(DUMMY_ITEM.data);
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch reset action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiChangeStatus = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.changeStatus(DUMMY_ITEM.data);
    await runSaga(fakeStore, changeStatus, requestResult).done;

    expect(api.makeApiChangeStatus.mock.calls.length).toBe(1);

    let resetResult = actions.resetChangeStatus(DUMMY_ITEM.data);
    const expectedAction = {
      type: actions.CHANGE_STATUS_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
