/*
 *
 */

import { makeNetworkCall } from '../..';
import { UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_URL, BASE_URL } from '../../../utils/constants/apiEndpoints';

import { FINALIZE, ORGANIZATION_ID, QUESTIONNAIRE_ID, QUESTIONNAIRE_RESPONSE_ID } from '../../../utils/constants';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiFetchUpdateSaveLiteQuestionnaireResponse(action) {
  const { payload = {} } = action || {};
  const {
    headers = {},
    patientData,
    questionnaireResponseId,
    organizationId,
    questionnaireId,
    isQuestionnaireFinished,
  } = payload || {};

  const mainUrl =
    BASE_URL +
    UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_URL.replace(QUESTIONNAIRE_ID, questionnaireId)
      .replace(QUESTIONNAIRE_RESPONSE_ID, questionnaireResponseId)
      .replace(ORGANIZATION_ID, organizationId)
      .replace(FINALIZE, isQuestionnaireFinished);
  const config = {
    method: 'PUT',
    url: mainUrl,
    headers: headers,
    data: patientData,
  };
  return makeNetworkCall(config);
}
