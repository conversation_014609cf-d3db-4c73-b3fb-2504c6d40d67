/*
 *REG<PERSON><PERSON><PERSON><PERSON>_DETAILS action types
 */

export const COMPLETE_REGISTRATION_REQUEST = 'COMPLETE_REGISTRATION_REQUEST';
export const COMPLETE_REGISTRATION_SUCCESS = 'COMPLETE_REGISTRATION_SUCCESS';
export const COMPLETE_REGISTRATION_ERROR = 'COMPLETE_REGISTRATION_ERROR';
export const COMPLETE_REGISTRATION_RESET = 'COMPLETE_REGISTRATION_RESET';

/*
 * action creators
 */

export function completeRegistration(data) {
  return {
    type: COMPLETE_REGISTRATION_REQUEST,
    payload: data,
  };
}

export function completePatientRegistrationSuccess(data) {
  return {
    type: COMPLETE_REGISTRATION_SUCCESS,
    payload: data,
  };
}

export function completePatientRegistrationError() {
  return {
    type: COMPLETE_REGISTRATION_ERROR,
  };
}

export function completeRegistrationReset() {
  return {
    type: COMPLETE_REGISTRATION_RESET,
  };
}
