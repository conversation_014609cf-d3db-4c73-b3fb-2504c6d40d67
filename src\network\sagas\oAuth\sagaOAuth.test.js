import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/oAuth/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { fetchToken } from './index';
/**
 * This function test test case for get oAuth details saga
 * Fires get oAuth success of api gives success
 * Fires get oAuth error of api fails
 */

describe('oAuth', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiOauth = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchToken(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchToken, requestResult).done;

    let successResult = actions.fetchTokenSuccess(DUMMY_ITEM.data);

    expect(api.makeApiOauth.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiOauth = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchToken(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchToken, requestResult).done;

    expect(api.makeApiOauth.mock.calls.length).toBe(1);

    let errorResult = actions.fetchTokenError();
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch reset action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiOauth = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchToken(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchToken, requestResult).done;

    expect(api.makeApiOauth.mock.calls.length).toBe(1);

    let resetResult = actions.resetToken();
    const expectedAction = {
      type: actions.OAUTH_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
