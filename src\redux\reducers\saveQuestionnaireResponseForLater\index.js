import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_REQUEST,
  SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_SUCCESS,
  SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_ERROR,
  SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_RESET,
} from '../../actions/saveQuestionnaireResponseForLater';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const saveQuestionnaireResponseForLaterReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;

  switch (type) {
    case SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_REQUEST:
      return {
        ...state,
        isSaveQuestionnaireResponseForLaterFetching: true,
        isSaveQuestionnaireResponseForLaterSuccess: false,
        saveQuestionnaireResponseForLaterSuccessData: false,
        isSaveQuestionnaireResponseForLaterError: null,
      };
    case SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_SUCCESS: {
      return {
        ...state,
        isSaveQuestionnaireResponseForLaterFetching: false,
        isSaveQuestionnaireResponseForLaterSuccess: true,
        saveQuestionnaireResponseForLaterSuccessData: payload,
        isSaveQuestionnaireResponseForLaterError: false,
        saveQuestionnaireResponseForLaterErrorData: null,
      };
    }
    case SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_ERROR:
      return {
        ...state,
        isSaveQuestionnaireResponseForLaterFetching: false,
        isSaveQuestionnaireResponseForLaterSuccess: false,
        isSaveQuestionnaireResponseForLaterError: true,
        saveQuestionnaireResponseForLaterErrorData: payload,
      };
    case SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_RESET:
      return {
        ...state,
        isSaveQuestionnaireResponseForLaterFetching: false,
        isSaveQuestionnaireResponseForLaterSuccess: false,
        saveQuestionnaireResponseForLaterSuccessData: null,
        isSaveQuestionnaireResponseForLaterError: false,
        saveQuestionnaireResponseForLaterErrorData: null,
      };
    default:
      return state;
  }
};
