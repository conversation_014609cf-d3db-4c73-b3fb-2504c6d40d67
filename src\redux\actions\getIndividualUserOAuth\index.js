/*
 * OAUTH action types
 */

export const INDIVIDUAL_USER_OAUTH_REQUEST = 'INDIVIDUAL_USER_OAUTH_REQUEST';
export const INDIVIDUAL_USER_OAUTH_SUCCESS = 'INDIVIDUAL_USER_OAUTH_SUCCESS';
export const INDIVIDUAL_USER_OAUTH_ERROR = 'INDIVIDUAL_USER_OAUTH_ERROR';
export const INDIVIDUAL_USER_OAUTH_RESET = 'INDIVIDUAL_USER_OAUTH_RESET';
export const INDIVIDUAL_USER_OAUTH_SAVE = 'INDIVIDUAL_USER_OAUTH_SAVE';
/*
 * action creators
 */

export function fetchIndividualUserToken(data) {
  return {
    type: INDIVIDUAL_USER_OAUTH_REQUEST,
    payload: data,
  };
}

export function fetchIndividualUserSuccess(data) {
  return {
    type: INDIVIDUAL_USER_OAUTH_SUCCESS,
    payload: data,
  };
}

export function fetchIndividualUserError() {
  return {
    type: INDIVIDUAL_USER_OAUTH_ERROR,
  };
}

export function fetchIndividualUserSaveToken(payload) {
  return {
    type: INDIVIDUAL_USER_OAUTH_SAVE,
    payload: payload,
  };
}

export const resetIndividualUserToken = () => {
  return {
    type: INDIVIDUAL_USER_OAUTH_RESET,
  };
};
