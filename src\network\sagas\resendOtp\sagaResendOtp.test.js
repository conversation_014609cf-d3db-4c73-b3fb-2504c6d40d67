import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/resendOtp/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { resendOtp } from './index';
/**
 * This function test test case for get resendOtp details saga
 * Fires get resendOtp success of api gives success
 * Fires get resendOtp error of api fails
 */

describe('resendOtp', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiResendOtp = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.resendOtp(DUMMY_ITEM.data);
    await runSaga(fakeStore, resendOtp, requestResult).done;

    let successResult = actions.resendRegistrationOtpSuccess(DUMMY_ITEM.data);

    expect(api.makeApiResendOtp.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiResendOtp = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.resendOtp(DUMMY_ITEM.data);
    await runSaga(fakeStore, resendOtp, requestResult).done;

    expect(api.makeApiResendOtp.mock.calls.length).toBe(1);

    let errorResult = actions.resendRegistrationOtpError(DUMMY_ITEM.data);
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch reset action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiResendOtp = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.resendOtp(DUMMY_ITEM.data);
    await runSaga(fakeStore, resendOtp, requestResult).done;

    expect(api.makeApiResendOtp.mock.calls.length).toBe(1);

    let resetResult = actions.resetResendOtp();
    const expectedAction = {
      type: actions.RESEND_OTP_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
