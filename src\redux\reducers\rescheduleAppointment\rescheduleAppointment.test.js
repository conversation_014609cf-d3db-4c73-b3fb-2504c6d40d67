import {
  RESCHEDULE_APPOINTMENT_REQUEST,
  RESCHEDULE_APPOINTMENT_SUCCESS,
  RESCHEDULE_APPOINTMENT_ERROR,
  RESCHEDULE_APPOINTMENT_RESET,
} from '../../actions/rescheduleAppointment';

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import { rescheduleAppointmentReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  rescheduleAppointmentFetching: true,
  rescheduleAppointmentSuccess: false,
  rescheduleAppointmentError: false,
  rescheduleAppointmentErrorData: null,
};

const successState = {
  rescheduleAppointmentFetching: false,
  rescheduleAppointmentSuccess: true,
  rescheduleAppointmentError: false,
  rescheduleAppointmentErrorData: null,
  rescheduleAppointmentSuccessData: data,
};

const errorState = {
  rescheduleAppointmentFetching: false,
  rescheduleAppointmentSuccess: false,
  rescheduleAppointmentError: true,
  rescheduleAppointmentErrorData: data,
};

const resetState = {
  rescheduleAppointmentFetching: false,
  rescheduleAppointmentSuccess: false,
  rescheduleAppointmentError: true,
  rescheduleAppointmentErrorData: null,
  rescheduleAppointmentSuccessData: null,
};

describe('Reschedule Reducer', () => {
  it('should return the initial state', () => {
    expect(rescheduleAppointmentReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle RESCHEDULE_APPOINTMENT_REQUEST', () => {
    expect(
      rescheduleAppointmentReducer(initialState, {
        type: RESCHEDULE_APPOINTMENT_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle RESCHEDULE_APPOINTMENT_SUCCESS', () => {
    expect(
      rescheduleAppointmentReducer(initialState, {
        type: RESCHEDULE_APPOINTMENT_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle RESCHEDULE_APPOINTMENT_ERROR', () => {
    expect(
      rescheduleAppointmentReducer(initialState, {
        type: RESCHEDULE_APPOINTMENT_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle RESCHEDULE_APPOINTMENT_RESET', () => {
    expect(
      rescheduleAppointmentReducer(initialState, {
        type: RESCHEDULE_APPOINTMENT_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
