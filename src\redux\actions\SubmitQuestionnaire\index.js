/*
 * POST Questions action types
 */

export const POST_QUESTIONNAIRE_QUESTIONS_REQUEST = 'POST_QUESTIONNAIRE_QUESTIONS_REQUEST';
export const POST_QUESTIONNAIRE_QUESTIONS_SUCCESS = 'POST_QUESTIONNAIRE_QUESTIONS_SUCCESS';
export const POST_QUESTIONNAIRE_QUESTIONS_ERROR = 'POST_QUESTIONNAIRE_QUESTIONS_ERROR';
export const POST_QUESTIONNAIRE_QUESTIONS_RESET = 'POST_QUESTIONNAIRE_QUESTIONS_RESET';
/*
 * action creators
 */

export function fetchQuestionnaireSubmitAnswers(data) {
  return {
    type: POST_QUESTIONNAIRE_QUESTIONS_REQUEST,
    payload: data,
  };
}

export function fetchQuestionnaireSubmitAnswersSuccess(data) {
  return {
    type: POST_QUESTIONNAIRE_QUESTIONS_SUCCESS,
    payload: data,
  };
}

export function fetchQuestionnaireSubmitAnswersError() {
  return {
    type: POST_QUESTIONNAIRE_QUESTIONS_ERROR,
  };
}

export function fetchQuestionnaireSubmitAnswersReset() {
  return {
    type: POST_QUESTIONNAIRE_QUESTIONS_RESET,
  };
}
