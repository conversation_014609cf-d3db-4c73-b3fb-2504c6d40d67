import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  GET_QUESTIONNAIRE_REQUEST,
  GET_QUESTIONNAIRE_SUCCESS,
  GET_QUESTIONNAIRE_ERROR,
  GET_QUESTIONNAIRE_RESET,
} from '../../actions/getQuestionnaireDetails';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const getQuestionnaireQuestionRedux = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case GET_QUESTIONNAIRE_REQUEST:
      return {
        ...state,
        isQuestionnaireQuestionsFetching: true,
        isQuestionnaireQuestionsSuccess: false,
        isQuestionnaireQuestionsError: false,
        questionnaireQuestionsErrorData: null,
      };
    case GET_QUESTIONNAIRE_SUCCESS: {
      return {
        ...state,
        isQuestionnaireQuestionsFetching: false,
        isQuestionnaireQuestionsSuccess: true,
        isQuestionnaireQuestionsError: false,
        questionnaireQuestionsErrorData: null,
        questionnaireQuestionsSuccessData: payload,
      };
    }
    case GET_QUESTIONNAIRE_ERROR:
      return {
        ...state,
        isQuestionnaireQuestionsFetching: false,
        isQuestionnaireQuestionsSuccess: false,
        isQuestionnaireQuestionsError: true,
        questionnaireQuestionsErrorData: payload,
      };
    case GET_QUESTIONNAIRE_RESET:
      return {
        ...state,
        isQuestionnaireQuestionsFetching: false,
        isQuestionnaireQuestionsSuccess: false,
        isQuestionnaireQuestionsError: false,
        questionnaireQuestionsErrorData: null,
        questionnaireQuestionsSuccessData: null,
      };
    default:
      return state;
  }
};
