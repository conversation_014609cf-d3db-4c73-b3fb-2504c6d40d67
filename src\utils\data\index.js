import { getOrganizationAndWidgetId } from '../../containers/commonUtility';

const getCreateUserData = (userData) => {
  const { firstName, lastName, username, password, verificationCode } = userData;
  const [organizationId, widgetId] = getOrganizationAndWidgetId();

  const userDetails = {
    firstName: firstName,
    lastName: lastName,
    emailAddress: username,
    password: password,
    oneTimePassword: verificationCode,
    organizationId: 'n/a',
    widgetId: 'n/a',
    uniqueKey: 'n/a',
  };

  return userDetails;
};

export { getCreateUserData };
