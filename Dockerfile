# Build stage
ARG PLATFORM=linux/arm64
FROM node:20-alpine AS builder
WORKDIR /app

ARG NPM_TOKEN
ARG ORG
ARG ENV

# Install dependencies
RUN npm config set registry https://registry.npmjs.org/
RUN npm config set @cambianrepo:registry https://npm.pkg.github.com/
RUN npm config set //npm.pkg.github.com/:_authToken ${NPM_TOKEN}

RUN npm i yarn
COPY package*.json ./
COPY yarn.lock ./
RUN yarn install

# Copy source files
COPY . .

# Copy environment files with variables replaced
COPY environments/.env .env
RUN if [ -f environments/${ORG}/.env.${ENV} ]; then \
    cp environments/${ORG}/.env.${ENV} .env.production; \
else \
    echo "Error: environment file for ${ORG}/${ENV} not found"; \
    exit 1; \
fi

# Build application
RUN npm run build:standalone

# Production stage
ARG PLATFORM=linux/arm64
FROM node:20-alpine AS runner
WORKDIR /app

# Set environment to production
ENV NODE_ENV=production

# Copy necessary files from builder
COPY --from=builder /app/next.config.js ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/yarn.lock ./
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/.next/cache ./.next/cache

# Configure for standalone server
ENV PORT=3000
EXPOSE 3000

CMD ["node", "server.js"]
