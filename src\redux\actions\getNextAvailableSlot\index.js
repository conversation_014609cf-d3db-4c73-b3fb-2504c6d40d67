/*
 * Get NextAvailableSlot action types
 */

export const GET_NEXT_AVAILABLE_SLOT_REQUEST = 'GET_NEXT_AVAILABLE_SLOT_REQUEST';
export const GET_NEXT_AVAILABLE_SLOT_SUCCESS = 'GET_NEXT_AVAILABLE_SLOT_SUCCESS';
export const GET_NEXT_AVAILABLE_SLOT_ERROR = 'GET_NEXT_AVAILABLE_SLOT_ERROR';
export const GET_NEXT_AVAILABLE_SLOT_RESET = 'GET_NEXT_AVAILABLE_SLOT_RESET';

/*
 * action creators
 */

export function fetchNextAvailableSlot(data) {
  return {
    type: GET_NEXT_AVAILABLE_SLOT_REQUEST,
    payload: data,
  };
}

export function fetchNextAvailableSlotSuccess(data) {
  return {
    type: GET_NEXT_AVAILABLE_SLOT_SUCCESS,
    payload: data,
  };
}

export function fetchNextAvailableSlotError() {
  return {
    type: GET_NEXT_AVAILABLE_SLOT_ERROR,
  };
}

export function fetchNextAvailableSlotReset() {
  return {
    type: GET_NEXT_AVAILABLE_SLOT_RESET,
  };
}
