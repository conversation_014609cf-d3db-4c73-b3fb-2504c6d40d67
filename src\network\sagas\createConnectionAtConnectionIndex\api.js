import { CREATE_CONNECTION_AT_CONNECTION_INDEX } from '@/utils/constants/awsApiEndpoints';
import { makeNetworkCall } from '../..';
import { INDIVIDUAL } from '@/utils/constants';

export function createConnectionAtConnectionIndex(action) {
  const { payload = {} } = action || {};
  const { headers = {}, connectionData } = payload || {};

  const URL = CREATE_CONNECTION_AT_CONNECTION_INDEX;

  const config = {
    targetAwsEnv: INDIVIDUAL,
    method: 'POST',
    url: URL,
    headers: headers,
    data: connectionData,
  };

  return makeNetworkCall(config);
}
