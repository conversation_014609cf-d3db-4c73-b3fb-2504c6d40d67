import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import LanguageIcon from '@mui/icons-material/Language';
import { Menu, MenuItem, Button } from '@mui/material';

export const LanguageSelector = ({ availableLanguages = ['en'], onLanguageChange }) => {
  const { t, i18n } = useTranslation();
  const [languageMenuAnchor, setLanguageMenuAnchor] = useState(null);

  const languageMap = {
    en: t('English'),
    fr: t('French'),
    es: t('Español'),
    de: t('Deutsch'),
  };

  const handleLanguageMenuOpen = (event) => {
    setLanguageMenuAnchor(event.currentTarget);
  };

  const handleLanguageMenuClose = () => {
    setLanguageMenuAnchor(null);
  };

  const changeLanguage = (lang) => {
    if (i18n.language !== lang) {
      i18n.changeLanguage(lang);

      if (onLanguageChange) {
        onLanguageChange(lang);
      }
    }
    setLanguageMenuAnchor(null);
  };

  return (
    <>
      <Button
        onClick={handleLanguageMenuOpen}
        aria-label={t('Change language')}
        color="primary"
        sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' } }}
      >
        <LanguageIcon />
      </Button>
      <Menu anchorEl={languageMenuAnchor} open={Boolean(languageMenuAnchor)} onClose={handleLanguageMenuClose}>
        {(availableLanguages && availableLanguages.length > 0 ? availableLanguages : ['en']).map((lang) => (
          <MenuItem key={lang} onClick={() => changeLanguage(lang)} selected={i18n.language === lang}>
            {languageMap[lang] || lang}
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};
