/*
 * WIDGET_DETAILS action types
 */

export const WIDGET_DETAILS_REQUEST = 'WIDGET_DETAILS_REQUEST';
export const WIDGET_DETAILS_SUCCESS = 'WIDGET_DETAILS_SUCCESS';
export const WIDGET_DETAILS_ERROR = 'WIDGET_DETAILS_ERROR';
export const WIDGET_DETAILS_RESET = 'WIDGET_DETAILS_RESET';

/*
 * action creators
 */

export function fetchWidgetDetails(data) {
  return {
    type: WIDGET_DETAILS_REQUEST,
    payload: data,
  };
}

export function fetchWidgetDetailsSuccess(data) {
  return {
    type: WIDGET_DETAILS_SUCCESS,
    payload: data,
  };
}

export function fetchWidgetDetailsError(data) {
  return {
    type: WIDGET_DETAILS_ERROR,
    payload: data,
  };
}

export const resetWidgetDetails = () => {
  return {
    type: WIDGET_DETAILS_RESET,
  };
};
