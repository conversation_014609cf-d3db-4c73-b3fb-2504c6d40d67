import { call, put, takeEvery } from 'redux-saga/effects';
import {
  CREATE_CONNECTION_AT_CONNECTION_INDEX_REQUEST,
  CREATE_CONNECTION_AT_CONNECTION_INDEX_SUCCESS,
  CREATE_CONNECTION_AT_CONNECTION_INDEX_ERROR,
} from '../../../redux/actions/createConnectionAtConnectionIndex';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { createConnectionAtConnectionIndex } from './api';

export function* fetchCreateConnectionAtConnectionIndex(action) {
  try {
    const response = yield call(createConnectionAtConnectionIndex, action);
    const { data = {} } = response;

    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: CREATE_CONNECTION_AT_CONNECTION_INDEX_SUCCESS, payload: data });
    } else {
      yield put({ type: CREATE_CONNECTION_AT_CONNECTION_INDEX_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: CREATE_CONNECTION_AT_CONNECTION_INDEX_ERROR });
  }
}

export function* watchCreateConnectionAtConnectionIndex() {
  yield takeEvery(CREATE_CONNECTION_AT_CONNECTION_INDEX_REQUEST, fetchCreateConnectionAtConnectionIndex);
}
