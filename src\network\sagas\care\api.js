/*
 *
 */

import { makeNetworkCall } from '../..';
import { TODO_ENDPOINT } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
export function makeUserFormData(action) {
  const { payload = {} } = action || {};
  const { data = {} } = payload || {};
  const { data: userDetails = {}, headers = {} } = data || {};

  let formBody = [];
  for (let userProperty in userDetails) {
    let encodedKey = encodeURIComponent(userProperty);
    let encodedValue = encodeURIComponent(userDetails[userProperty]);
    formBody.push(encodedKey + '=' + encodedValue);
  }
  formBody = formBody.join('&');

  const config = {
    method: 'POST',
    url: TODO_ENDPOINT,
    data: formBody,
    headers: headers,
    formData: true,
  };
  return makeNetworkCall(config);
}

/**
 *
 * @param {*} action
 */
export function makeApiFetchTypCode() {
  const config = {
    method: 'GET',
    url: TODO_ENDPOINT,
    formData: false,
  };
  return makeNetworkCall(config);
}
