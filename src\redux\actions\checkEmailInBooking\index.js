/*
 * CHECK_EMAIL action types
 */

export const CHECK_EMAIL_REQUEST = 'CHECK_EMAIL_REQUEST';
export const CHECK_EMAIL_SUCCESS = 'CHECK_EMAIL_SUCCESS';
export const CHECK_EMAIL_ERROR = 'CHECK_EMAIL_ERROR';
export const CHECK_EMAIL_RESET = 'CHECK_EMAIL_RESET';

/*
 * action creators
 */
export function checkEmail(data) {
  return {
    type: CHECK_EMAIL_REQUEST,
    payload: data,
  };
}

export function checkActionEmailSuccess(data) {
  return {
    type: CHECK_EMAIL_SUCCESS,
    payload: data,
  };
}

export function checkActionEmailError() {
  return {
    type: CHECK_EMAIL_ERROR,
  };
}

export const resetCheckEmail = () => {
  return {
    type: CHECK_EMAIL_RESET,
  };
};
