import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '@/redux/reducers/globals';
import defaultAction from '@/redux/actions';
import {
  SAVE_WIDGET_AUTH_REQUEST,
  SAVE_WIDGET_AUTH_SUCCESS,
  SAVE_WIDGET_AUTH_ERROR,
  SAVE_WIDGET_AUTH_RESET,
} from '@/redux/actions/widgetAuth';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const widgetAuthReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;

  switch (type) {
    case SAVE_WIDGET_AUTH_REQUEST:
      return {
        ...state,
        isWidgetAuthFetching: true,
        widgetAuthSuccess: false,
        widgetAuthError: false,
        widgetAuthErrorData: null,
      };
    case SAVE_WIDGET_AUTH_SUCCESS: {
      return {
        ...state,
        isWidgetAuthFetching: false,
        widgetAuthSuccess: true,
        widgetAuthSuccessData: payload,
        widgetAuthError: false,
        widgetAuthErrorData: null,
      };
    }
    case SAVE_WIDGET_AUTH_ERROR:
      return {
        ...state,
        isWidgetAuthFetching: false,
        widgetAuthSuccess: false,
        widgetAuthError: true,
        widgetAuthErrorData: payload,
      };
    case SAVE_WIDGET_AUTH_RESET:
      return {
        ...state,
        isWidgetAuthFetching: false,
        widgetAuthSuccess: false,
        widgetAuthSuccessData: undefined,
        widgetAuthError: false,
        widgetAuthErrorData: undefined,
      };
    default:
      return state;
  }
};
