/*
 *
 */
import { makeNetworkCall } from '../..';
import { VALIDATE_EMAIL_IN_BOOKING, BASE_URL } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */

export function makeApiValidateEmailInBooking(action) {
  const { payload = {} } = action || {};
  const { headers = {}, validateEmailData = {} } = payload || {};
  const config = {
    method: 'POST',
    url: BASE_URL + VALIDATE_EMAIL_IN_BOOKING,
    headers: headers,
    data: validateEmailData,
  };
  return makeNetworkCall(config);
}
