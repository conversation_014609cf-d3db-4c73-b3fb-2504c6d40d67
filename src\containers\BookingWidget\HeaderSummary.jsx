import React, { Fragment } from 'react';
import { <PERSON>ton, <PERSON>ack, Grid, Box, Divider } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { actions } from '../commonConstants';
import { useTranslation } from 'react-i18next';
import { resetCreateConnectionAtClientIndex } from '@/redux/actions/createConnectionAtClientIndex';

function HeaderSummary(props) {
  const {
    handleNavigationCallback,
    headerSummary,
    demographic,
    isRescheduleAppointment,
    handleRescheduleSlotNavigation,
  } = props;
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const { displayEligibility, services = [] } =
    useSelector((state) => state.widgetDetailsReducer.widgetDetailsSuccessData) || {};

  const isReadOnly = handleNavigationCallback === undefined;
  let headerSummaryArray = Array.from(headerSummary, ([type, value]) => ({ type, value }));

  const handleHeaderSummaryNavigationCallback = (summary) => {
    switch (summary.type) {
      case t('appointment'):
        if (!displayEligibility) {
          headerSummary.clear();
        }
        break;
      case t('individual'):
        dispatch(resetCreateConnectionAtClientIndex());
        if (displayEligibility) {
          headerSummary.clear();
        } else {
          headerSummaryArray.forEach(
            (summary) => summary.type !== t('appointment') && headerSummary.delete(summary.type, summary.value),
          );
        }
        break;
      case t('location'):
        headerSummaryArray.forEach(
          (summary) =>
            summary.type !== t('appointment') &&
            summary.type !== t('individual') &&
            headerSummary.delete(summary.type, summary.value),
        );
        break;
      case t('dateAndTime'):
        headerSummaryArray.forEach(
          (summary) =>
            summary.type !== t('appointment') &&
            summary.type !== t('individual') &&
            summary.type !== t('location') &&
            headerSummary.delete(summary.type, summary.value),
        );
        break;
      default:
        break;
    }
    if (isReadOnly && isRescheduleAppointment) {
      handleRescheduleSlotNavigation(actions.PREVIOUS, summary.type, demographic, isRescheduleAppointment);
    } else {
      handleNavigationCallback(actions.PREVIOUS, summary.type, demographic, isRescheduleAppointment);
    }
  };

  const showEditButton = (summary, index) => {
    if (isRescheduleAppointment) {
      if (summary.type == t('dateAndTime') || summary.type === t('location')) {
        return (
          <Button
            variant="text"
            sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' } }}
            onClick={() => handleHeaderSummaryNavigationCallback(summary, index)}
          >
            {t('edit')}
          </Button>
        );
      } else {
        <></>;
      }
    } else {
      return (
        <Button
          variant="text"
          sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' } }}
          onClick={() => handleHeaderSummaryNavigationCallback(summary, index)}
        >
          {t('edit')}
        </Button>
      );
    }
  };

  return (
    <Box sx={{ my: 2 }}>
      <>
        <Stack direction="column" spacing={2} sx={{}}>
          {headerSummaryArray?.length ? (
            headerSummaryArray.map((summary, index) => (
              <Fragment key={index}>
                <Grid container alignItems="center">
                  <Grid item xs={4} sm={2}>
                    {summary.type}
                  </Grid>
                  <Grid item xs={6} sm={8}>
                    <div
                      dangerouslySetInnerHTML={{
                        __html:
                          typeof summary?.value === 'string' ? summary?.value : summary?.value?.map((item) => item),
                      }}
                    />
                  </Grid>
                  {!isReadOnly && summary.type === 'Appointment' && services.length > 1 && (
                    <Grid
                      item
                      xs={2}
                      sm={2}
                      sx={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        alignItems: 'center',
                        '&:hover': { backgroundColor: 'transparent', color: '#23527c' },
                      }}
                    >
                      {showEditButton(summary, index)}
                    </Grid>
                  )}

                  {!isReadOnly && summary.type !== 'Appointment' && (
                    <Grid
                      item
                      xs={2}
                      sm={2}
                      sx={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        alignItems: 'center',
                        '&:hover': { backgroundColor: 'transparent', color: '#23527c' },
                      }}
                    >
                      {showEditButton(summary, index)}
                    </Grid>
                  )}
                  {isReadOnly && isRescheduleAppointment && summary.type === 'Location' && (
                    <Grid
                      item
                      xs={2}
                      sm={2}
                      sx={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        alignItems: 'center',
                        '&:hover': { backgroundColor: 'transparent', color: '#23527c' },
                      }}
                    >
                      {showEditButton(summary, index)}
                    </Grid>
                  )}
                </Grid>
                {index !== headerSummaryArray.length - 1 && <Divider sx={{ width: '100%' }} />}
              </Fragment>
            ))
          ) : (
            <></>
          )}
        </Stack>
      </>
    </Box>
  );
}

export { HeaderSummary };
