/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import { OAUTH_REQUEST, OAUTH_ERROR, OAUTH_SUCCESS, OAUTH_RESET } from '../../actions/oAuth';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const oAuthReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case OAUTH_REQUEST:
      return {
        ...state,
        isOauthFetching: true,
        fetchOauthSuccess: false,
        fetchOauthError: false,
        oauthErrorData: null,
      };
    case OAUTH_SUCCESS: {
      return {
        ...state,
        isOauthFetching: false,
        fetchOauthSuccess: true,
        fetchOauthError: false,
        oauthErrorData: null,
        oauthSuccessData: payload,
      };
    }
    case OAUTH_ERROR:
      return {
        ...state,
        isOauthFetching: false,
        fetchOauthSuccess: false,
        fetchOauthError: true,
        oauthErrorData: payload,
      };
    case OAUTH_RESET:
      return {
        ...state,
        isOauthFetching: false,
        fetchCareSuccess: false,
        fetchCareError: false,
        careErrorData: null,
        oauthSuccessData: null,
      };
    default:
      return state;
  }
};
