import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  GET_REGISTRATION_QUESTIONS_ERROR,
  GET_REGISTRATION_QUESTIONS_SUCCESS,
  GET_REGISTRATION_QUESTIONS_REQUEST,
} from '../../actions/getRegistrationQuestions';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const getRegistrationQuestionsReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case GET_REGISTRATION_QUESTIONS_REQUEST:
      return {
        ...state,
        isRegistrationQuestionsFetching: true,
        isRegistrationQuestionsSuccess: false,
        isRegistrationQuestionsError: false,
        registrationQuestionsErrorData: null,
      };
    case GET_REGISTRATION_QUESTIONS_SUCCESS: {
      return {
        ...state,
        isRegistrationQuestionsFetching: false,
        isRegistrationQuestionsSuccess: true,
        isRegistrationQuestionsError: false,
        registrationQuestionsErrorData: null,
        registrationQuestionsSuccessData: payload,
      };
    }
    case GET_REGISTRATION_QUESTIONS_ERROR:
      return {
        ...state,
        isRegistrationQuestionsFetching: false,
        isRegistrationQuestionsSuccess: false,
        isRegistrationQuestionsError: true,
        registrationQuestionsErrorData: payload,
      };
    default:
      return state;
  }
};
