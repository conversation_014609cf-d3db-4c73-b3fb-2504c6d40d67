/**
 *
 */
import {
  <PERSON><PERSON><PERSON><PERSON>ATION_ID,
  WIDGET_ID,
  LOCATION_ID,
  APPOINTMENT_TYPE,
  DATE_NOW,
  APPOINTMENT_ID,
  INDIVIDUALS_COUNT,
  PATIENT_ID,
  SERVICE_ID,
  QUESTIONNAIRE_ID,
  EMAIL_VERIFICATION_REQUIRED,
  <PERSON>ARCH_PATIENTS,
  BIRTH_DATE,
  FAMILY,
  GIVEN,
  EMAIL,
  GENDER,
  MATCHANY,
  PHN,
  UNIQUEKEY,
  QUESTIONNAIRE_RESPONSE_ID,
  FINALIZE,
  DOWNLOAD_REPORT_ID,
} from '../constants';
// Test and Dev Environment
// export const BASE_URL = 'https://jsonplaceholder.typicode.com';
export const TODO_ENDPOINT = '/todos/1';

// export const PROXY_URL = "https://cors-anywhere.herokuapp.com/";

//FOR PRODUCTION PURPOSE

export const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;
export const CLIENT_SERVICES_BASE_URL = process.env.REACT_APP_CLIENT_SERVICES_BASE_URL;
export const WIDGET_SERVICES_BASE_URL = process.env.REACT_APP_WIDGET_SERVICES_BASE_URL;
export const CAMBIAN_SERVICE_BASE_URL = process.env.REACT_APP_CAMBIAN_SERVICE_BASE_URL;
export const GET_BOOKING_WIDGET = `/cambian-widget-services/organizations/${ORGANIZATION_ID}/booking-widgets-global/${WIDGET_ID}`;
export const GET_BOOKING_SLOTS = `/cambian-client-services/timeslots?organization=${ORGANIZATION_ID}&locationId=${LOCATION_ID}&appointmentType=${APPOINTMENT_TYPE}&multiBookingCount=${INDIVIDUALS_COUNT}&multiBookingMode=parallel&start=${DATE_NOW}&authorizationCode=code`;

export const GET_LOCATIONS = `/cambian-client-services/locations?organization=${ORGANIZATION_ID}&appointmentType=${APPOINTMENT_TYPE}`;

export const GET_AUTH_TOKEN = '/oauth/token';
export const GET_USER_INFO = '/igateway/users/userinfo';
export const GET_INDIVIDUAL_USER_INFO = `/igateway/citizenusers?email=EMAIL_ID`;
export const CREATE_INDIVIDUAL_USER = `/igateway/citizenusers/create`;
// Test and Dev Environment
export const RESCHEDULE_APPOINTMENT = `/cambian-client-services/appointments/${APPOINTMENT_ID}/$groupreschedule`;
export const GET_BOOK_APPOINTMENT = `/cambian-client-services/appointments/${APPOINTMENT_ID}`;
export const BOOK_APPOINTMENT = '/cambian-client-services/appointments/$groupbook';
export const CANCEL_APPOINTMENT = `/cambian-client-services/appointments/${APPOINTMENT_ID}/$groupcancel`;
export const LOGGED_OUT = '/igateway/users/logout';
export const GET_REGISTRATION_WIDGET_DETAILS = `/cambian-widget-services/organizations/${ORGANIZATION_ID}/registration-widgets-global/${WIDGET_ID}`;
export const COMPLETE_REGISTRATION = `/cambian-client-services/patients?emailVerificationRequired=${EMAIL_VERIFICATION_REQUIRED}`;
export const COMPLETE_REGISTRATION_WITH_ID = `/cambian-client-services/patients/${PATIENT_ID}?emailVerificationRequired=${EMAIL_VERIFICATION_REQUIRED}`;
export const VALIDATE_EMAIL = '/cambian-authorizer/otp?role=PATIENT';
export const RESEND_OTP = '/cambian-client-services/patients/resend-otp';
export const GET_REGISTRATION_QUESTIONS = `/cambian-client-services/patients/${PATIENT_ID}/questions?serviceId=${SERVICE_ID}`;
export const GET_QUESTIONNAIRE_QUESTIONS = `/cambian-client-services/questionnaires/${QUESTIONNAIRE_ID}`;
export const SUBMIT_ANSWERS = `/cambian-client-services/patients/${PATIENT_ID}/ques-ans`;
export const SCORING = `/cambian-client-services/scoring?organizationId=${ORGANIZATION_ID}`;
export const QUESTIONNAIRE_SUBMIT_ANSWERS = '/cambian-client-services/questionnaireResponses/$submit';
export const ACTIVATE_PATIENT = `/cambian-client-services/patients/${PATIENT_ID}/status`;
export const CHECK_EMAIL = '/cambian-client-services/patient/$checkContact';
export const VALIDATE_EMAIL_IN_BOOKING = '/cambian-client-services/patient/$validateContact';
export const QUESTION_WIDGET_DETAILS_URL = `/cambian-widget-services/organizations/${ORGANIZATION_ID}/questionnaireWidgetsGlobal/${WIDGET_ID}`;
export const CRETAE_OTP_URL = '/cambian-authorizer/otp?role=PATIENT';
export const CHANGE_STATUS_URL = `/cambian-authorizer/patients/${PATIENT_ID}/status`;
export const SEARCH_QUESTIONNAIRE_URL = `/cambian-client-services/patient?searchQuery=${SEARCH_PATIENTS}&birthDate=${BIRTH_DATE}&patientEmail=${EMAIL}&family=${FAMILY}&gender=${GENDER}&given=${GIVEN}&matchany=${MATCHANY}&organizationId=${ORGANIZATION_ID}&phn=${PHN}&uniqueKey=${UNIQUEKEY}&widgetId=${WIDGET_ID}`;
export const CREATE_PATIENT = `/cambian-client-services/patient/$createPatient?organizationId=${ORGANIZATION_ID}`;
export const ASSIGN_QUESTIONNAIRE = `/cambian-client-services/assignQuestionnaire?organizationId=${ORGANIZATION_ID}`;
export const UPDATE_QUESTIONNAIRE_RESPONSE = `/cambian-client-services/getPasscode/${QUESTIONNAIRE_RESPONSE_ID}?organizationId=${ORGANIZATION_ID}&questionnaireId=${QUESTIONNAIRE_ID}`;
export const GET_SCORE_URL = `/cambian-client-services/questionnaireResponses/${QUESTIONNAIRE_RESPONSE_ID}/score?userId=${PATIENT_ID}&organizationId=${ORGANIZATION_ID}`;
export const GET_NEXT_AVAILABLE_SLOT = '/cambian-client-services/timeslots/$nextavailable';
export const SEARCH_AND_CREATE_PATIENT = `/cambian-client-services/patient/$searchAndCreatePatient?organizationId=${ORGANIZATION_ID}`;
export const GET_PDF_REPORT_URL = `/cambian-client-services/questionnaireResponses/${QUESTIONNAIRE_RESPONSE_ID}/pdf?organizationId=${ORGANIZATION_ID}`;
export const SAVE_LITE_QUESTIONNAIRE_RESPONSE_URL = `/cambian-client-services/postAutoSaveLite?organizationId=${ORGANIZATION_ID}`;
export const UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_URL = `/cambian-client-services/autoSave/${QUESTIONNAIRE_ID}/${QUESTIONNAIRE_RESPONSE_ID}?organizationId=${ORGANIZATION_ID}&finalize=${FINALIZE}`;
export const DELETE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_URL = `/cambian-client-services/autoSave/${QUESTIONNAIRE_ID}/${QUESTIONNAIRE_RESPONSE_ID}?organizationId=${ORGANIZATION_ID}`;
export const START_QUESTIONNAIRE = `/igateway/questionnaires/start`;
export const SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER = `/questionnaireresponses/questionnaireresponse/update/${QUESTIONNAIRE_RESPONSE_ID}?finalize=${FINALIZE}&email=${EMAIL}&organizationId=${ORGANIZATION_ID}&questionnaireId=${QUESTIONNAIRE_ID}&async=true`;
export const DOWNLOAD_REPORT = '/cambian-widget-services/downloadReport';
export const GET_DOWNLOADABLE_REPORT = `/cambian-widget-services/getReport?questionnaireReportById=${DOWNLOAD_REPORT_ID}`;
export const FORGOT_PASSWORD = `/igateway/citizenusers/${EMAIL}/requestpasswordreset`;
export const SEND_CONNECTION_INVITE = `/integrationdata/${ORGANIZATION_ID}/createConnectionInvitation?emailAddress=${EMAIL}`;
export const GET_QUESTIONNAIRE_RESPONSE_FHIR = `/cambian-client-services/QuestionnaireResponse/${QUESTIONNAIRE_RESPONSE_ID}?organizationId=${ORGANIZATION_ID}`;
export const ANONYMOUS_SCORING_FHIR = `/cambian-client-services/scoring/fhir?organizationId=${ORGANIZATION_ID}`;

export const GET_INDIVIDUAL_DATA = '';
export const CREATE_INDIVIDUAL_DATA = '';
