#!/bin/bash

# Exit immediately if a command exits with a non-zero status
set -e

# Prevent AWS CLI from opening Vim or less pager for long output
export AWS_PAGER=""

# Prompt for user inputs
read -p "Enter organization name (ORG) (eg. pcn | cambian | phsa): " ORG
read -p "Enter environment (ENV) (dev | test | cambian-dev): " ENV
read -p "Enter NPM_TOKEN: " -s NPM_TOKEN
echo
read -p "Enter VERSION: " VERSION

# VERSION=${VERSION_INPUT:-"phsa-dev-v2.4"}

# Validate inputs
if [[ -z "$ORG" || -z "$ENV" || -z "$NPM_TOKEN" || -z "$VERSION" ]]; then
    echo "Error: ORG, ENV, VERSION and NPM_TOKEN are required!"
    exit 1
fi

# AWS and ECR configuration

ECR_REPO="organization-images"
AWS_REGION="ca-central-1"
# AWS account ID
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
# ECR repository URL
ECR_REPO_URL="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPO}"
# VERSION="phsa-test-v2"

image_name="${ECR_REPO}:widget-${VERSION}"

echo "Building for Organization: $ORG, Environment: $ENV, Version: $VERSION"

# Function to log in to ECR
ecr_login() {
    echo "Logging in to Amazon ECR..."
    aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REPO_URL
}

check_or_create_ecr_repository() {
    echo "Checking if ECR repository ${ECR_REPO} exists..."
    
    REPO_EXISTS=$(aws ecr describe-repositories --region $AWS_REGION --repository-names $ECR_REPO 2>&1 || true)
    
    if echo "$REPO_EXISTS" | grep -q "RepositoryNotFoundException"; then
        echo "Repository ${ECR_REPO} does not exist. Creating repository..."
        aws ecr create-repository --repository-name $ECR_REPO --region $AWS_REGION
        echo "Repository ${ECR_REPO} created."
    else
        echo "Repository ${ECR_REPO} already exists."
    fi
}

# Function to build Docker image
build_docker_image() {
    echo "Building Docker image..."
    docker build \
        --build-arg NPM_TOKEN="$NPM_TOKEN" \
        --build-arg ORG="$ORG" \
        --build-arg ENV="$ENV" \
        --build-arg VERSION="$VERSION" \
        -t ${image_name} .
}

# Function to push Docker image to ECR
push_docker_image() {
    # ecr_login

    echo "Tagging Docker image..."
    docker tag "${image_name}" "${ECR_REPO_URL}:widget-${VERSION}"

    echo "Pushing Docker image to Amazon ECR..."
    docker push "${ECR_REPO_URL}:widget-${VERSION}"
}

# Main deployment workflow
echo "Starting deployment process..."
ecr_login
check_or_create_ecr_repository
build_docker_image
push_docker_image
echo "Docker image pushed to ECR successfully."
