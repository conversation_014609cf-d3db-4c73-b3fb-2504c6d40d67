import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import { GET_PDF_REQUEST, GET_PDF_SUCCESS, GET_PDF_ERROR, GET_PDF_RESET } from '../../actions/getPdfReport';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const getPdfReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;

  switch (type) {
    case GET_PDF_REQUEST:
      return {
        ...state,
        getPdfFetching: true,
        getPdfSuccess: false,
        getPdfError: false,
        getPdfErrorData: null,
      };
    case GET_PDF_SUCCESS: {
      return {
        ...state,
        getPdfFetching: false,
        getPdfSuccess: true,
        getPdfError: false,
        getPdfErrorData: null,
        getPdfSuccessData: payload,
      };
    }
    case GET_PDF_ERROR:
      return {
        ...state,
        getPdfFetching: false,
        getPdfSuccess: false,
        getPdfError: true,
        getPdfErrorData: payload,
      };
    case GET_PDF_RESET:
      return {
        ...state,
        getPdfFetching: false,
        getPdfSuccess: false,
        getPdfError: false,
        getPdfErrorData: null,
      };
    default:
      return state;
  }
};
