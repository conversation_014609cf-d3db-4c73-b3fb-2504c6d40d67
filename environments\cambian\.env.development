NEXT_PUBLIC_APP_ENV=local

NEXT_PUBLIC_NOTIFICATION_MESSAGE=

NEXT_PUBLIC_DEPLOYMENT_HOST=http://localhost:3010

NEXT_PUBLIC_BASE_URL=http://localhost:3010

# Google Map API key
NEXT_PUBLIC_GOOGLE_MAP_API_KEY=AIzaSyDeW0tEhWQnfETX_YpyZgK-ITlgfVnhFvw

# TODO: Need to inject client secret and IAM keys during build time instead for security.
COGNITO_REGION=ca-central-1

COGNITO_ORG_USER_POOL_ID=ca-central-1_3SiTl4bZl
COGNITO_ORG_APP_CLIENT_ID=6v7qmdr6oh5lrp9cpaq7scro2
COGNITO_ORG_APP_CLIENT_SECRET=1fghanol03egtkd1n2b2c8b0lnnu4o9r1i5l6vhk8crk3163f8j9

# Allow access to services in Org Env
COGNITO_ORG_MACHINE_USER_POOL_ID=ca-central-1_1XukDc4cG
COGNITO_ORG_MACHINE_APP_CLIENT_ID=25lbh8o6kbs03157lkn59q5sr2
COGNITO_ORG_MACHINE_APP_CLIENT_SECRET=1sh5aqit15k03km7p6hudfmrhi1otfo3mrvebs6nvav5cvjshaqs
COGNITO_ORG_MACHINE_USERNAME=fdf0b32c0401998d9f8b67a032a902f1
COGNITO_ORG_MACHINE_PASSWORD=0RpWbMRFK5Rnz5/FnU+jVTF8uO/T8x94fIdewN2cTcw=

# Allow access to services in Network Env
COGNITO_NETWORK_MACHINE_USER_POOL_ID=ca-central-1_x2uEYYC7f
COGNITO_NETWORK_MACHINE_APP_CLIENT_ID=4qc8fg7vj3mh8bggpp1r147adl
COGNITO_NETWORK_MACHINE_APP_CLIENT_SECRET=13ird8dj2t1pji1utouhgmm0r4r6tfolq9rodluk0h278v8q3eni
COGNITO_NETWORK_MACHINE_USERNAME=
COGNITO_NETWORK_MACHINE_PASSWORD=

WIDGET_ALLOW_COGNITO_ACCESS_IAM_ACCESS_KEY=********************
WIDGET_ALLOW_COGNITO_ACCESS_IAM_SECRET_KEY=IdXjuaAg2Me7DTIED6ifCz8mFQM11nQciTUZbzaX


NEXTAUTH_URL=$NEXT_PUBLIC_DEPLOYMENT_HOST
NEXTAUTH_SECRET=RaB2obLvjFGIAWGLSLHBxDjQjMbpESnOxGeyPEJ6w6I=

#
# AWS Config
#
NEXT_PUBLIC_ORG_REGISTRY_BASE_URL=https://gvvbymtsva.execute-api.ca-central-1.amazonaws.com/dev
NEXT_PUBLIC_PUBLIC_ARTIFACT_REPOSITORY_BASE_URL=https://tk5fsm3ipe.execute-api.ca-central-1.amazonaws.com/dev
NEXT_PUBLIC_PRIVATE_ARTIFACT_REPOSITORY_BASE_URL=https://lyd6kt4b88.execute-api.ca-central-1.amazonaws.com/dev
NEXT_PUBLIC_ORGANISATION_DATA_BASE_URL=https://cwwvbuvvec.execute-api.ca-central-1.amazonaws.com/dev
NEXT_PUBLIC_ORGANISATION_CDR_BASE_URL=


# Client Index
NEXT_PUBLIC_CLIENT_INDEX_BASE_URL=
NEXT_PUBLIC_CLIENT_INDEX_ORG_ID=
NEXT_PUBLIC_ORGANIZATION_MESSAGING_SERVICE_BASE_URL=

NEXT_PUBLIC_ORGANIZATION_INDIVIDUAL_SERVICE_BASE_URL=

#Activity Tracker
NEXT_PUBLIC_ACTIVITY_TRACKER_SERVICE_BASE_URL=