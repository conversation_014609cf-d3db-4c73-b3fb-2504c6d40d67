import { call, put, takeEvery } from 'redux-saga/effects';
import {
  SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_REQUEST,
  SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_SUCCESS,
  SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_ERROR,
} from '../../../redux/actions/searchIndividualAtClientIndex';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { searchIndividualAtClientIndex } from './api';

export function* fetchSearchIndividualAtClientIndex(action) {
  try {
    const response = yield call(searchIndividualAtClientIndex, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_SUCCESS, payload: data });
    } else {
      yield put({ type: SEAR<PERSON>_INDIVIDUAL_AT_CLIENT_INDEX_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_ERROR });
  }
}

export function* watchSearchIndividualAtClientIndex() {
  yield takeEvery(SEARCH_INDIVIDUAL_AT_CLIENT_INDEX_REQUEST, fetchSearchIndividualAtClientIndex);
}
