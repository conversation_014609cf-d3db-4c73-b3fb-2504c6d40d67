import {
  CANCEL_BOOKING_REQUEST,
  CANCEL_BOOKING_SUCCESS,
  CANCEL_BOOKING_ERROR,
  CANCEL_BOOKING_RESET,
} from '../../actions/cancelBooking/index';
import * as GL<PERSON>BA<PERSON> from '../globals';
import { cancelBookingReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  cancelBookingFetching: true,
  cancelBookingSuccess: false,
  cancelBookingError: false,
  cancelBookingErrorData: null,
};

const errorState = {
  cancelBookingFetching: false,
  cancelBookingSuccess: false,
  cancelBookingError: true,
  cancelBookingErrorData: data,
};

const successState = {
  cancelBookingFetching: false,
  cancelBookingSuccess: true,
  cancelBookingError: false,
  cancelBookingErrorData: null,
  cancelBookingSuccessData: data,
};

const resetState = {
  cancelBookingFetching: false,
  cancelBookingSuccess: false,
  cancelBookingError: true,
  cancelBookingErrorData: null,
  cancelBookingSuccessData: null,
};

describe('Cancel Booking Details Reducer', () => {
  it('should return the initial state', () => {
    expect(cancelBookingReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle CANCEL_BOOKING_REQUEST', () => {
    let result = cancelBookingReducer(initialState, {
      type: CANCEL_BOOKING_REQUEST,
    });

    expect(result).toEqual({ ...initialState, ...requestState });
  });

  it('should handle CANCEL_BOOKING_ERROR', () => {
    expect(
      cancelBookingReducer(initialState, {
        type: CANCEL_BOOKING_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle CANCEL_BOOKING_SUCCESS', () => {
    expect(
      cancelBookingReducer(initialState, {
        type: CANCEL_BOOKING_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle CANCEL_BOOKING_RESET', () => {
    expect(
      cancelBookingReducer(initialState, {
        type: CANCEL_BOOKING_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
