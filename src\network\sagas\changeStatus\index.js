/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import { CHANGE_STATUS_ERROR, CHANGE_STATUS_REQUEST, CHANGE_STATUS_SUCCESS } from '../../../redux/actions/changeStatus';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiChangeStatus } from './api';

/**
 *
 * @param {*} action
 */

export function* changeStatus(action) {
  try {
    const response = yield call(makeApiChangeStatus, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: CHANGE_STATUS_SUCCESS, payload: data });
    } else {
      yield put({ type: CHANGE_STATUS_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: CHANGE_STATUS_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchChangeStatus() {
  yield takeEvery(CHANGE_STATUS_REQUEST, changeStatus);
}
