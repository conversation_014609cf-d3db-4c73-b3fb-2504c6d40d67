NEXT_PUBLIC_AWS_SERVICES_ENVIRONMENT=dev
# DNS for Widget Public hosting
NEXT_PUBLIC_BASE_URL=https://dev-scheduling.bchealthcloud.ca

# Google Map API key
NEXT_PUBLIC_GOOGLE_MAP_API_KEY=AIzaSyD_r2SlxPR2ZzfEQDODY17j1wSvTmBehhw

# * APIs variables
NEXT_PUBLIC_WIDGET_SERVICES_BASE_URL=https://widget-services-dhp-dev1.bchealthcloud.ca

# Schedular Booking API Gateway
NEXT_PUBLIC_BOOKING_SERVICES_BASE_URL=https://booking-services-dhp-dev1.bchealthcloud.ca

# Artifact repository 
# Private Artifact repository
NEXT_PUBLIC_ARTIFACT_PRIVATE_REPOSITORY_BASE_URL=https://artifact-repository-dhp-dev1.bchealthcloud.ca

# Public Artifact repository
NEXT_PUBLIC_ARTIFACT_PUBLIC_REPOSITORY_BASE_URL=https://t3p1nv7z97.execute-api.ca-central-1.amazonaws.com/dev

# CDRs
NEXT_PUBLIC_INDIVIDUAL_CDR_BASE_URL=https://70e4ltesad.execute-api.ca-central-1.amazonaws.com/dev

NEXT_PUBLIC_ORGANIZATION_CDR_BASE_URL=https://orgprecdr-dhp-dev1.bchealthcloud.ca

# Indexes
NEXT_PUBLIC_CONNECTION_INDEX_BASE_URL=https://r0bh12nufa.execute-api.ca-central-1.amazonaws.com/dev

NEXT_PUBLIC_CLIENT_INDEX_BASE_URL=https://client-index-dhp-dev1.bchealthcloud.ca

# DOC Gen Service
NEXT_PUBLIC_PDF_GENERATION_SERVICE_BASE_URL=https://doc-generation-dhp-dev1.bchealthcloud.ca

# Org messaging Service
NEXT_PUBLIC_ORGANIZATION_MESSAGING_SERVICE_BASE_URL=https://orgmessaging-dhp-dev1.bchealthcloud.ca

# Allow access to services in Org Env
COGNITO_ORG_MACHINE_USER_POOL_ID=ca-central-1_rKsk02sVk
COGNITO_ORG_MACHINE_APP_CLIENT_ID=6qnsef8cql8t3mpe4a8dq66t41
COGNITO_ORG_MACHINE_APP_CLIENT_SECRET=1mig0ncbss8rbau7vj2apo0408khssk3t4bq5v7aleq929p0n2sa
COGNITO_ORG_MACHINE_USERNAME=widget_machine_user
COGNITO_ORG_MACHINE_PASSWORD=Widget76!
COGNITO_ORG_MACHINE_CREDENTIALS='{"username": "widget_machine_user", "password": "Widget76!"}'

# Allow access to services in Network Env
# COGNITO_NETWORK_MACHINE_USER_POOL_ID=ca-central-1_blNZoOj7O
# COGNITO_NETWORK_MACHINE_APP_CLIENT_ID=5p5ur4gs0ipm6qftdu3j0g3sk6
# COGNITO_NETWORK_MACHINE_APP_CLIENT_SECRET=lgu3nsipjtf77vgrad4kto4mm7a7k10abbks4at594uph1o3cgh
# COGNITO_NETWORK_MACHINE_USERNAME=ea29cbab034d205c038c0a93da210b42
# COGNITO_NETWORK_MACHINE_PASSWORD=tYWmsMV0BvMZ29e6VcpRYk5EGguKeA6X8I001kB6CWX=
COGNITO_NETWORK_MACHINE_USER_POOL_ID=ca-central-1_rPnRLwrid
COGNITO_NETWORK_MACHINE_APP_CLIENT_ID=37fo91agquv79k0j7tj66pqffd
COGNITO_NETWORK_MACHINE_APP_CLIENT_SECRET=eeu584gftupjg5r25pj026ev9b48dimq9ijjd7beh3u0e6co9j1
COGNITO_NETWORK_MACHINE_USERNAME=865c6d29e51d2967924884bd9bc5703b
COGNITO_NETWORK_MACHINE_PASSWORD=gX7xXZ4+bxqSYZ/5fQ2Vq7xS1UKPKYB6vp7duadymKm=
COGNITO_NETWORK_MACHINE_CREDENTIALS='{"username": "865c6d29e51d2967924884bd9bc5703b", "password": "gX7xXZ4+bxqSYZ/5fQ2Vq7xS1UKPKYB6vp7duadymKm="}'

WIDGET_ALLOW_COGNITO_ACCESS_IAM_ACCESS_KEY=********************
WIDGET_ALLOW_COGNITO_ACCESS_IAM_SECRET_KEY=QusJO4qqRutfxzzhGYaq6BxDlUH9DWiwPFNu1+XU

# Allow access to services in Individual Env
COGNITO_INDIVIDUAL_MACHINE_USER_POOL_ID=ca-central-1_kQFbifOYL
COGNITO_INDIVIDUAL_MACHINE_APP_CLIENT_ID=1u4ib1n2memof9qcpfmhhi13ot
COGNITO_INDIVIDUAL_MACHINE_APP_CLIENT_SECRET=1getbdgqk0jtclbd37h1qccceiqdr8dopt0dk56m88ji84pccp3n
COGNITO_INDIVIDUAL_MACHINE_CREDENTIALS='{"username": "572e5c6faef3766c2d53c391b5e4cb9b", "password": "gPgqv/soNj/qrMF5MuIcZbOuIBNS3ZN=TcctdWGix9a="}'
COGNITO_INDIVIDUAL_MACHINE_USERNAME=572e5c6faef3766c2d53c391b5e4cb9b
COGNITO_INDIVIDUAL_MACHINE_PASSWORD=gPgqv/soNj/qrMF5MuIcZbOuIBNS3ZN=TcctdWGix9a=

# Cognito Human auth variables
COGNITO_INDIVIDUAL_HUMAN_REGION=ca-central-1
COGNITO_INDIVIUDAL_HUMAN_USER_POOL_ID=ca-central-1_t5XYRZzvT
COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_ID=1o6f912kklrpao3i289a2qbum
COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_SECRET=14ndlopd7e0nd461u1n24kuf9cdis8ab6obqll4o45emjetv3u4t

NEXTAUTH_URL=$NEXT_PUBLIC_BASE_URL
NEXTAUTH_SECRET="RaB2obLvjFGIAWGLSLHBxDjQjMbpESnOxGeyPEJ6w6I=" #same as before
