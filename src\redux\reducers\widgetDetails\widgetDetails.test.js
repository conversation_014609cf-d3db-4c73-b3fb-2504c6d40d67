import {
  WIDGET_DETAILS_RESET,
  WIDGET_DETAILS_ERROR,
  WIDGET_DETAILS_SUCCESS,
  WIDGET_DETAILS_REQUEST,
} from '../../actions/widgetDetails/index';
import * as G<PERSON><PERSON><PERSON><PERSON> from '../globals';
import { widgetDetailsReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isWidgetDetailsFetching: true,
  fetchWidgetDetailsSuccess: false,
  fetchWidgetDetailsError: false,
  widgetDetailsErrorData: null,
};

const successState = {
  isWidgetDetailsFetching: false,
  fetchWidgetDetailsSuccess: true,
  fetchWidgetDetailsError: false,
  widgetDetailsErrorData: null,
  widgetDetailsSuccessData: data,
};

const errorState = {
  isWidgetDetailsFetching: false,
  fetchWidgetDetailsSuccess: false,
  fetchWidgetDetailsError: true,
  widgetDetailsErrorData: data,
};

const resetState = {
  isWidgetDetailsFetching: false,
  fetchWidgetDetailsSuccess: false,
  fetchWidgetDetailsError: false,
  widgetDetailsErrorData: null,
};

describe('Widget Details Reducer', () => {
  it('should return the initial state', () => {
    expect(widgetDetailsReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle WIDGET_DETAILS_REQUEST', () => {
    expect(
      widgetDetailsReducer(initialState, {
        type: WIDGET_DETAILS_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle WIDGET_DETAILS_SUCCESS', () => {
    expect(
      widgetDetailsReducer(initialState, {
        type: WIDGET_DETAILS_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle WIDGET_DETAILS_ERROR', () => {
    expect(
      widgetDetailsReducer(initialState, {
        type: WIDGET_DETAILS_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle WIDGET_DETAILS_RESET', () => {
    expect(
      widgetDetailsReducer(initialState, {
        type: WIDGET_DETAILS_RESET,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
