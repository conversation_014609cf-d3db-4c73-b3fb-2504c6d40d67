/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import {
  SEARCH_QUESTIONNAIRE_REQUEST,
  SEARCH_QUESTIONNAIRE_SUCCESS,
  SEARCH_QUESTIONNAIRE_ERROR,
  SEARCH_QUESTIONNAIRE_RESET,
} from '../../actions/SearchQuestionnaire';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const searchQuestionnaireReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case SEARCH_QUESTIONNAIRE_REQUEST:
      return {
        ...state,
        isSearchQuestionFetching: true,
        isSearchQuestionSuccess: false,
        isSearchQuestionError: false,
        searchQuestionErrorData: null,
      };
    case SEARCH_QUESTIONNAIRE_SUCCESS:
      return {
        ...state,
        isSearchQuestionFetching: false,
        isSearchQuestionSuccess: true,
        isSearchQuestionError: false,
        searchQuestionErrorData: null,
        searchQuestionSuccessData: payload,
      };
    case SEARCH_QUESTIONNAIRE_ERROR:
      return {
        ...state,
        isSearchQuestionFetching: false,
        isSearchQuestionSuccess: false,
        isSearchQuestionError: true,
        searchQuestionErrorData: payload,
      };
    case SEARCH_QUESTIONNAIRE_RESET:
      return {
        ...state,
        isSearchQuestionFetching: false,
        isSearchQuestionSuccess: false,
        isSearchQuestionError: false,
        searchQuestionErrorData: null,
        searchQuestionSuccessData: null,
      };
    default:
      return state;
  }
};
