/*
 * ASSIGN_QUESTIONNAIRE action types
 */

export const START_QUESTIONNAIRE_REQUEST = 'START_QUESTIONNAIRE_REQUEST';
export const START_QUESTIONNAIRE_SUCCESS = 'START_QUESTIONNAIRE_SUCCESS';
export const START_QUESTIONNAIRE_ERROR = 'START_QUESTIONNAIRE_ERROR';
export const START_QUESTIONNAIRE_RESET = 'START_QUESTIONNAIRE_RESET';

/*
 * action creators
 */

export function startQuestionnaire(data) {
  return {
    type: START_QUESTIONNAIRE_REQUEST,
    payload: data,
  };
}

export function startQuestionnaireSuccess(data) {
  return {
    type: START_QUESTIONNAIRE_SUCCESS,
    payload: data,
  };
}

export function startQuestionnaireError() {
  return {
    type: START_QUESTIONNAIRE_ERROR,
  };
}

export function resetStartQuestionnaire() {
  return {
    type: START_QUESTIONNAIRE_RESET,
  };
}
