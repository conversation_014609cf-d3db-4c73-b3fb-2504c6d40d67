import { runSaga } from 'redux-saga';
import * as api from './api';
import { assignQuestionnaire } from './index';
import * as actions from '../../../redux/actions/AssignQuestionnaire/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

/**
 * This function test test case for get  assign queestionnaire details saga
 * Fires get  assign queestionnaire success of api gives success
 * Fires get  assign queestionnaire error of api fails
 */

describe('Assign Questionnaire', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiFetchAssignQuestionnaire = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.assignQuestionnaireRequest(DUMMY_ITEM.data);
    await runSaga(fakeStore, assignQuestionnaire, requestResult).done;

    let successResult = actions.assignQuestionnaireSucess(DUMMY_ITEM.data);

    expect(api.makeApiFetchAssignQuestionnaire.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchAssignQuestionnaire = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.assignQuestionnaireRequest(DUMMY_ITEM.data);
    await runSaga(fakeStore, assignQuestionnaire, requestResult).done;

    expect(api.makeApiFetchAssignQuestionnaire.mock.calls.length).toBe(1);

    let errorResult = actions.assignQuestionnaireError(DUMMY_ITEM.data);
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch reset action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchAssignQuestionnaire = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.assignQuestionnaireRequest(DUMMY_ITEM.data);
    await runSaga(fakeStore, assignQuestionnaire, requestResult).done;

    expect(api.makeApiFetchAssignQuestionnaire.mock.calls.length).toBe(1);

    let resetResult = actions.assignQuestionnaireReset(DUMMY_ITEM.data);
    const expectedAction = {
      type: actions.ASSIGN_QUESTIONNAIRE_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
