export const GET_QUESTIONNAIRE_WIDGET_REQUEST = 'GET_QUESTIONNAIRE_WIDGET_REQUEST';
export const GET_QUESTIONNAIRE_WIDGET_SUCCESS = 'GET_QUESTIONNAIRE_WIDGET_SUCCESS';
export const GET_QUESTIONNAIRE_WIDGET_ERROR = 'GET_QUESTIONNAIRE_WIDGET_ERROR';
export const GET_QUESTIONNAIRE_WIDGET_RESET = 'GET_QUESTIONNAIRE_WIDGET_RESET';

export function getQuestionnaireWidget(data) {
  return {
    type: GET_QUESTIONNAIRE_WIDGET_REQUEST,
    payload: data,
  };
}

export const resetQuestionnaireWidget = () => {
  return {
    type: GET_QUESTIONNAIRE_WIDGET_RESET,
  };
};
