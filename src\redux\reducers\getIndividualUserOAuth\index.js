/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import {
  INDIVIDUAL_USER_OAUTH_REQUEST,
  INDIVIDUAL_USER_OAUTH_SUCCESS,
  INDIVIDUAL_USER_OAUTH_ERROR,
  INDIVIDUAL_USER_OAUTH_RESET,
  INDIVIDUAL_USER_OAUTH_SAVE,
} from '../../actions/getIndividualUserOAuth';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const individualUserOAuthReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;

  switch (type) {
    case INDIVIDUAL_USER_OAUTH_REQUEST:
      return {
        ...state,
        isIndividualUserOAuthFetching: true,
        individualUserOAuthSuccess: false,
        individualUserOAuthError: false,
        individualUserOAuthErrorData: null,
      };
    case INDIVIDUAL_USER_OAUTH_SUCCESS: {
      return {
        ...state,
        isIndividualUserOAuthFetching: false,
        individualUserOAuthSuccess: true,
        individualUserOAuthSuccessData: payload,
        individualUserOAuthError: false,
        individualUserOAuthErrorData: null,
      };
    }
    case INDIVIDUAL_USER_OAUTH_ERROR:
      return {
        ...state,
        isIndividualUserOAuthFetching: false,
        individualUserOAuthSuccess: false,
        individualUserOAuthError: true,
        individualUserOAuthErrorData: payload,
      };
    case INDIVIDUAL_USER_OAUTH_SAVE:
      return {
        ...state,
        isIndividualUserOAuthFetching: false,
        individualUserOAuthSuccess: true,
        individualUserOAuthSuccessData: payload,
        individualUserOAuthError: false,
        individualUserOAuthErrorData: null,
      };
    case INDIVIDUAL_USER_OAUTH_RESET:
      return {
        ...state,
        isIndividualUserOAuthFetching: false,
        individualUserOAuthSuccess: false,
        individualUserOAuthSuccessData: undefined,
        individualUserOAuthError: false,
        individualUserOAuthErrorData: undefined,
      };
    default:
      return state;
  }
};
