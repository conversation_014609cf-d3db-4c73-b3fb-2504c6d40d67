import { RESEND_OTP_SUCCESS, RESEND_OTP_REQUEST, RESEND_OTP_ERROR, RESEND_OTP_RESET } from '../../actions/resendOtp';

import * as GLOBALS from '../globals';
import { resendOtpReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isResendOtpFetching: true,
  resendOtpSuccess: false,
  resendOtpError: false,
  resendOtpErrorData: null,
};

const successState = {
  isResendOtpFetching: false,
  resendOtpSuccess: true,
  resendOtpError: false,
  resendOtpErrorData: null,
  resendOtpSuccessData: data,
};

const errorState = {
  isResendOtpFetching: false,
  resendOtpSuccess: false,
  resendOtpError: true,
  resendOtpErrorData: data,
};

const resetState = {
  isResendOtpFetching: false,
  resendOtpSuccess: false,
  resendOtpError: false,
  resendOtpErrorData: null,
};

describe('Reschedule Reducer', () => {
  it('should return the initial state', () => {
    expect(resendOtpReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle RESEND_OTP_REQUEST', () => {
    expect(
      resendOtpReducer(initialState, {
        type: RESEND_OTP_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle RESEND_OTP_SUCCESS', () => {
    expect(
      resendOtpReducer(initialState, {
        type: RESEND_OTP_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle RESEND_OTP_ERROR', () => {
    expect(
      resendOtpReducer(initialState, {
        type: RESEND_OTP_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle RESEND_OTP_RESET', () => {
    expect(
      resendOtpReducer(initialState, {
        type: RESEND_OTP_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
