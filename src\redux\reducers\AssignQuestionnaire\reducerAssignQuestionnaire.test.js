import {
  ASSIGN_QUESTIONNAIRE_REQUEST,
  ASSIGN_QUESTIONNAIRE_SUCCESS,
  ASSIGN_QUESTIONNAIRE_ERROR,
  ASSIGN_QUESTIONNAIRE_RESET,
} from '../../actions/AssignQuestionnaire';

import * as G<PERSON><PERSON><PERSON><PERSON> from '../globals';
import { assignQuestionnaireReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  assignQuestionnaireFetching: true,
  assignQuestionnaireSuccess: false,
  assignQuestionnaireError: false,
  assignQuestionnaireErrorData: null,
};

const successState = {
  assignQuestionnaireFetching: false,
  assignQuestionnaireSuccess: true,
  assignQuestionnaireError: false,
  assignQuestionnaireErrorData: null,
  assignQuestionnaireSuccessData: data,
};

const errorState = {
  assignQuestionnaireFetching: false,
  assignQuestionnaireSuccess: false,
  assignQuestionnaireError: true,
  assignQuestionnaireErrorData: data,
};

const resetState = {
  assignQuestionnaireFetching: false,
  assignQuestionnaireSuccess: false,
  assignQuestionnaireError: false,
  assignQuestionnaireErrorData: null,
  assignQuestionnaireSuccessData: null,
};

describe('Reschedule Reducer', () => {
  it('should return the initial state', () => {
    expect(assignQuestionnaireReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle ASSIGN_QUESTIONNAIRE_REQUEST', () => {
    expect(
      assignQuestionnaireReducer(initialState, {
        type: ASSIGN_QUESTIONNAIRE_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle ASSIGN_QUESTIONNAIRE_SUCCESS', () => {
    expect(
      assignQuestionnaireReducer(initialState, {
        type: ASSIGN_QUESTIONNAIRE_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle ASSIGN_QUESTIONNAIRE_ERROR', () => {
    expect(
      assignQuestionnaireReducer(initialState, {
        type: ASSIGN_QUESTIONNAIRE_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle ASSIGN_QUESTIONNAIRE_RESET', () => {
    expect(
      assignQuestionnaireReducer(initialState, {
        type: ASSIGN_QUESTIONNAIRE_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
