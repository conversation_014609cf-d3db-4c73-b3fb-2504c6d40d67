import { useEffect, useMemo, useState } from 'react';
import { pages } from '../questionnaireConstants';
import { useDispatch, useSelector } from 'react-redux';
import { Box, Button } from '@mui/material';
import { IDENTIFIED, ORGANIZATION_USER, QUESTIONNAIRE_WIDGET, actions } from '@/containers/commonConstants';
import { Loader, PageNotFound, HeadingAndDescription, Action, LanguageSelector } from '@/components';
import { checkAllRequiredDemographicPresent, getParamFromUrl, scrollToTop } from '@/containers/commonUtility';
import { Questionnaire } from './Questionnaire';
import useNotification from '@/hooks/useNotification';
import { useTranslation } from 'react-i18next';
import { getQuestionnaireWidget } from '@/redux/actions/getQuestionnaireWidget';
import { useParams } from 'next/navigation';

import { Report } from './Report';
import { useRouter } from 'next/navigation';
import { HeadingAndDescriptionPage } from './HeadingAndDescriptionPage';
import { Register } from './Register';
import { resetUpdateConnectionAtClientIndex } from '@/redux/actions/updateConnectionAtClientIndex';
import { SignInSignUp } from '@/containers/auth/SignInSignUp';
import { useSession } from 'next-auth/react';
import { EditDemographic } from '@/containers/Demographics';
import { extractInstrumentScores, getMatchedActionCondition } from '../questionnaireUtility';

export const QuestionnaireWidget = (props) => {
  const { questionnaireWidgetSuccessData } = props;
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const { organizationId, widgetId } = useParams();
  const [, sendNotification] = useNotification();

  const { isIndividualUserInfoFetching } = useSelector((state) => state.individualUserInfoReducer);

  const {
    identification,
    fields,
    flow,
    headingAndDescriptionPage = {
      description: '',
      enabled: false,
      heading: '',
      showDownloadIcon: true,
      showPrintIcon: false,
    },
    isConsentRequired,
    introduction,
    report,
    action,
    showSignIn,
    provinces,
    widgetType,
    clientInformationPageTitle,
    clientInformationPageSubtitle,
    selfRegistration,
    otpVerificationEnabled,
    individualNotFoundPage,
    byRequestOnly,
    requestNotFoundPage,
    defaultLanguage,
    currentLanguage,
    availableLanguages,
    nextButtonText,
    previousButtonText,
    doneButtonText,
    spinnerText,
  } = questionnaireWidgetSuccessData || {};

  const getInitialPage = () => {
    if (introduction?.enabled) {
      return pages.INTRODUCTION;
    } else if (identification === IDENTIFIED) {
      return pages.DEMOGRAPHIC;
    } else {
      return pages.QUESTIONNAIRE;
    }
  };

  const isDemographicFromUrl = getParamFromUrl('demographicPageShow');
  const showDemographicFromUrl = isDemographicFromUrl === 'true';

  const [currentPage, setCurrentPage] = useState(getInitialPage());
  const [demographic, setDemographic] = useState(null);
  const [questionnaireResponse, setQuestionnaireResponse] = useState(null);
  const [isSignInDialogOpen, setIsSignInDialogOpen] = useState(false);
  const [questionnaireStatus, setQuestionnaireStatus] = useState();
  const [isActionPerformed, setIsActionPerformed] = useState(false);

  useEffect(() => {
    if (questionnaireWidgetSuccessData?.currentLanguage) {
      const languageToUse =
        questionnaireWidgetSuccessData.currentLanguage || questionnaireWidgetSuccessData.defaultLanguage;

      if (languageToUse && i18n.language !== languageToUse) {
        i18n.changeLanguage(languageToUse);
      }
    }
  }, [questionnaireWidgetSuccessData]);

  const handleLanguageChange = (lang) => {
    try {
      dispatch(
        getQuestionnaireWidget({
          headers: { 'Content-Type': 'application/json' },
          organizationId,
          widgetId,
          lang,
        }),
      );
      setCurrentPage(getInitialPage());
      setQuestionnaireResponse(null);
      setQuestionnaireStatus(null);
    } catch (error) {
      console.error('Error changing language:', error);
      sendNotification({ msg: t('apiError'), variant: 'error' });
    }
  };

  const matchedAction = useMemo(() => {
    const instrumentScores = extractInstrumentScores(questionnaireResponse);
    const matchedAction = getMatchedActionCondition(action?.metaData?.actionConditions, instrumentScores);

    if (!matchedAction) {
      const defaultAction = action.metaData?.actionConditions?.find((condition) => condition.default);
      return defaultAction;
    }

    return matchedAction;
  }, [questionnaireResponse, action]);

  const handleIntroductionNavigation = (action) => {
    if (action === actions.NEXT) {
      if (flow && flow === ORGANIZATION_USER) {
        // handleSignInAsClinician();
      } else {
        if (isDemographicFromUrl) {
          if (showDemographicFromUrl) {
            setCurrentPage(pages.QUESTIONNAIRE);
            return;
          }
          handleDemographicNavigation(actions.NEXT, demographic);
        } else {
          if (identification === IDENTIFIED) {
            setCurrentPage(pages.DEMOGRAPHIC);
            return;
          }
          setCurrentPage(pages.QUESTIONNAIRE);
        }
      }
    }
  };

  const handleDemographicNavigation = (action, demographic) => {
    setDemographic(demographic);
    if (action === actions.NEXT) {
      dispatch(resetUpdateConnectionAtClientIndex());
      setCurrentPage(pages.QUESTIONNAIRE);
      scrollToTop();
    }
  };

  const handleQuestionnaireNavigation = (action, status, questionnaireResponse) => {
    if (questionnaireResponse) {
      setQuestionnaireResponse(questionnaireResponse);
    }

    const { isSavedForLater = false, isCancelled = false, isQuestionnaireFinished = false } = status || {};
    setQuestionnaireStatus({ isSavedForLater, isCancelled, isQuestionnaireFinished });

    if (action === actions.NEXT) {
      if (isQuestionnaireFinished && report?.enabled) {
        setCurrentPage(pages.RESULT);
        return;
      }
      if (isSavedForLater || isCancelled) {
        setCurrentPage(pages.HEADING_AND_DESCRIPTION);
        return;
      }
      if (headingAndDescriptionPage?.enabled) {
        setCurrentPage(pages.HEADING_AND_DESCRIPTION);
        return;
      }
      setCurrentPage(pages.QUESTIONNAIRE);
    } else if (action === actions.PREVIOUS) {
      setCurrentPage(pages.DEMOGRAPHIC);
    }
  };

  const handleResultNavigation = () => {
    if (headingAndDescriptionPage?.enabled) {
      setCurrentPage(pages.HEADING_AND_DESCRIPTION);
    } else if (action?.enabled) {
      if (action?.metaData?.actionConditions?.length) {
        setCurrentPage(pages.ACTION_PAGE);
      }
    }
  };

  const handleHeadingAndDescriptionPageNavigation = () => {
    setCurrentPage(pages.ACTION_PAGE);
  };

  const handleActionNavigation = (action) => {
    if (action === actions.NEXT) {
      if (headingAndDescriptionPage?.enabled) {
        setCurrentPage(pages.HEADING_AND_DESCRIPTION);
      } else if (report?.enabled) {
        setCurrentPage(pages.RESULT);
      } else {
        return;
      }
    }
  };

  const handleSignInDialogState = (state) => {
    setIsSignInDialogOpen(state);
  };

  const getCurrentPage = () => {
    let page = <></>;

    switch (currentPage) {
      case pages.INTRODUCTION:
        page = (
          <HeadingAndDescription
            headingAndDescriptionData={introduction}
            handleNavigationCallback={handleIntroductionNavigation}
          />
        );
        break;
      case pages.DEMOGRAPHIC:
        page = (
          <EditDemographic
            title={clientInformationPageTitle}
            subtitle={clientInformationPageSubtitle}
            fields={fields}
            demographic={demographic}
            handleDemographicCreationCallback={handleDemographicNavigation}
            isMultipleIndividualEnabled={false}
            isConsentRequired={isConsentRequired}
            widgetType={widgetType}
            provinces={provinces}
            selfRegistration={selfRegistration}
            clientNotFoundPage={individualNotFoundPage}
            otpVerificationEnabled={otpVerificationEnabled}
            byRequestOnly={byRequestOnly}
            requestNotFoundPage={requestNotFoundPage}
          />
        );
        break;
      case pages.QUESTIONNAIRE:
        page = (
          <Questionnaire
            questionnaireWidgetSuccessData={questionnaireWidgetSuccessData}
            handleNavigationCallback={handleQuestionnaireNavigation}
            demographic={demographic}
            setDemographic={setDemographic}
            setIsSignInDialogOpen={handleSignInDialogState}
          />
        );
        break;
      case pages.RESULT:
        page = (
          <Report
            questionnaireWidgetSuccessData={questionnaireWidgetSuccessData}
            handleNavigationCallback={handleResultNavigation}
            demographic={demographic}
            isActionPerformed={isActionPerformed}
            setIsActionPerformed={setIsActionPerformed}
            questionnaireResponse={questionnaireResponse}
            signInModalControl={{ isSignInDialogOpen, handleSignInDialogState }}
          />
        );
        break;
      case pages.HEADING_AND_DESCRIPTION:
        page = (
          <HeadingAndDescriptionPage
            questionnaireWidgetSuccessData={questionnaireWidgetSuccessData}
            demographic={demographic}
            questionnaireStatus={questionnaireStatus}
            questionnaireResponse={questionnaireResponse}
            handleNavigationCallback={handleHeadingAndDescriptionPageNavigation}
            isActionPerformed={isActionPerformed}
            setIsActionPerformed={setIsActionPerformed}
          />
        );
        break;
      case pages.ACTION_PAGE:
        page = (
          <Action
            handleNavigationCallback={handleActionNavigation}
            matchedAction={matchedAction}
            demographic={demographic}
            setIsActionPerformed={setIsActionPerformed}
            questionnaireResponse={questionnaireResponse}
          />
        );
        break;
      default:
        page = <PageNotFound />;
        break;
    }

    return page;
  };

  return (
    <>
      <Loader active={isIndividualUserInfoFetching} message={spinnerText || t('loading')} />

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', px: { xs: 1, md: '4.5%' }, mt: 0.5, gap: 0 }}>
        <LanguageSelector availableLanguages={availableLanguages} onLanguageChange={handleLanguageChange} />

        {showSignIn && (
          <SignInSignUp
            isSignInDialogOpen={isSignInDialogOpen}
            handleSignInDialogState={handleSignInDialogState}
            widgetType={QUESTIONNAIRE_WIDGET}
            identification={identification}
          />
        )}
      </Box>
      <Box sx={{ mx: 'auto', p: 2 }}>{getCurrentPage()}</Box>
    </>
  );
};
