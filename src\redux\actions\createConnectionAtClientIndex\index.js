export const CREATE_CONNECTION_AT_CLIENT_INDEX_REQUEST = 'CREATE_CONNECTION_AT_CLIENT_INDEX_REQUEST';
export const CREATE_CONNECTION_AT_CLIENT_INDEX_SUCCESS = 'CREATE_CONNECTION_AT_CLIENT_INDEX_SUCCESS';
export const CREATE_CONNECTION_AT_CLIENT_INDEX_ERROR = 'CREATE_CONNECTION_AT_CLIENT_INDEX_ERROR';
export const CREATE_CONNECTION_AT_CLIENT_INDEX_RESET = 'CREATE_CONNECTION_AT_CLIENT_INDEX_RESET';

export function createConnectionAtClientIndex(data) {
  return {
    type: CREATE_CONNECTION_AT_CLIENT_INDEX_REQUEST,
    payload: data,
  };
}

export function createConnectionAtClientIndexSuccess(data) {
  return {
    type: CREATE_CONNECTION_AT_CLIENT_INDEX_SUCCESS,
    payload: data,
  };
}

export function createConnectionAtClientIndexError(data) {
  return {
    type: CREATE_CONNECTION_AT_CLIENT_INDEX_ERROR,
    payload: data,
  };
}

export const resetCreateConnectionAtClientIndex = () => {
  return {
    type: CREATE_CONNECTION_AT_CLIENT_INDEX_RESET,
  };
};
