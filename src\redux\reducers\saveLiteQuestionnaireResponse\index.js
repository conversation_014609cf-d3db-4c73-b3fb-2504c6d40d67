import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  SAVE_LITE_QUESTIONNAIRE_RESPONSE_ERROR,
  SAVE_LITE_QUESTIONNAIRE_RESPONSE_SUCCESS,
  SAVE_LITE_QUESTIONNAIRE_RESPONSE_REQUEST,
  SAVE_LITE_QUESTIONNAIRE_RESPONSE_RESET,
} from '../../actions/saveLiteQuestionnaireResponse';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const saveLiteQuestionnaireResponseReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case SAVE_LITE_QUESTIONNAIRE_RESPONSE_REQUEST:
      return {
        ...state,
        isSaveLiteQuestionnaireResponseFetching: true,
        isSaveLiteQuestionnaireResponseSuccess: false,
        isSaveLiteQuestionnaireResponseError: false,
        saveLiteQuestionnaireResponseErrorData: null,
      };
    case SAVE_LITE_QUESTIONNAIRE_RESPONSE_SUCCESS: {
      return {
        ...state,
        isSaveLiteQuestionnaireResponseFetching: false,
        isSaveLiteQuestionnaireResponseSuccess: true,
        isSaveLiteQuestionnaireResponseError: false,
        saveLiteQuestionnaireResponseErrorData: null,
        saveLiteQuestionnaireResponseSuccessData: payload,
      };
    }
    case SAVE_LITE_QUESTIONNAIRE_RESPONSE_ERROR:
      return {
        ...state,
        isSaveLiteQuestionnaireResponseFetching: false,
        isSaveLiteQuestionnaireResponseSuccess: false,
        isSaveLiteQuestionnaireResponseError: true,
        saveLiteQuestionnaireResponseErrorData: payload,
      };
    case SAVE_LITE_QUESTIONNAIRE_RESPONSE_RESET:
      return {
        ...state,
        isSaveLiteQuestionnaireResponseFetching: false,
        isSaveLiteQuestionnaireResponseSuccess: false,
        isSaveLiteQuestionnaireResponseError: false,
        saveLiteQuestionnaireResponseErrorData: null,
        saveLiteQuestionnaireResponseSuccessData: null,
      };
    default:
      return state;
  }
};
