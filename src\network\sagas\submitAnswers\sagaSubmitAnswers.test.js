import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/submitAnswers/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { fetchSubmitAnswers } from './index';

/**
 * This function test test case for get submitAnswers details saga
 * Fires get submitAnswers success of api gives success
 * Fires get submitAnswers error of api fails
 */

describe('submitAnswers', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiFetchSubmitAnswers = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchSubmitAnswers(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchSubmitAnswers, requestResult).done;

    let successResult = actions.fetchSubmitAnswersSuccess(DUMMY_ITEM.data);

    expect(api.makeApiFetchSubmitAnswers.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchSubmitAnswers = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchSubmitAnswers(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchSubmitAnswers, requestResult).done;

    expect(api.makeApiFetchSubmitAnswers.mock.calls.length).toBe(1);

    let errorResult = actions.fetchSubmitAnswersError(DUMMY_ITEM.data);
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchSubmitAnswers = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchSubmitAnswers(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchSubmitAnswers, requestResult).done;

    expect(api.makeApiFetchSubmitAnswers.mock.calls.length).toBe(1);

    let resetResult = actions.fetchSubmitAnswersReset();
    const expectedAction = {
      type: actions.POST_REGISTRATION_QUESTIONS_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
