/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { LOG_OUT_ERROR, LOG_OUT_SUCCESS, LOG_OUT_REQUEST } from '../../../redux/actions/logOut';

import { userLogOutApi } from './api';

/**
 *
 * @param {*} action
 */
export function* userLogOut(action) {
  try {
    const response = yield call(userLogOutApi, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: LOG_OUT_SUCCESS, payload: data });
    } else {
      yield put({ type: LOG_OUT_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: LOG_OUT_ERROR });
  }
}

// Our watcher Saga:
export function* watchUserLogOut() {
  yield takeEvery(LOG_OUT_REQUEST, userLogOut);
}
