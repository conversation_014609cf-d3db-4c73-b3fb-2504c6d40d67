import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  CREATE_CONNECTION_AT_CLIENT_INDEX_REQUEST,
  CREATE_CONNECTION_AT_CLIENT_INDEX_SUCCESS,
  CREATE_CONNECTION_AT_CLIENT_INDEX_ERROR,
  CREATE_CONNECTION_AT_CLIENT_INDEX_RESET,
} from '../../actions/createConnectionAtClientIndex';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const createConnectionAtClientIndexReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case CREATE_CONNECTION_AT_CLIENT_INDEX_REQUEST:
      return {
        ...state,
        isCreateConnectionAtClientIndexFetching: true,
        createConnectionAtClientIndexSuccess: false,
        createConnectionAtClientIndexError: false,
        createConnectionAtClientIndexErrorData: null,
      };
    case CREATE_CONNECTION_AT_CLIENT_INDEX_SUCCESS: {
      return {
        ...state,
        isCreateConnectionAtClientIndexFetching: false,
        createConnectionAtClientIndexSuccess: true,
        createConnectionAtClientIndexError: false,
        createConnectionAtClientIndexErrorData: null,
        createConnectionAtClientIndexSuccessData: payload,
      };
    }
    case CREATE_CONNECTION_AT_CLIENT_INDEX_ERROR:
      return {
        ...state,
        isCreateConnectionAtClientIndexFetching: false,
        createConnectionAtClientIndexSuccess: false,
        createConnectionAtClientIndexSuccessData: null,
        createConnectionAtClientIndexError: true,
        createConnectionAtClientIndexErrorData: payload,
      };
    case CREATE_CONNECTION_AT_CLIENT_INDEX_RESET:
      return {
        ...state,
        isCreateConnectionAtClientIndexFetching: undefined,
        createConnectionAtClientIndexSuccess: undefined,
        createConnectionAtClientIndexSuccessData: undefined,
        createConnectionAtClientIndexError: undefined,
        createConnectionAtClientIndexErrorData: undefined,
      };
    default:
      return state;
  }
};
