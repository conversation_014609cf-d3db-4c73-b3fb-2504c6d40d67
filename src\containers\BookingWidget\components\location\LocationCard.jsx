import { useState } from 'react';
import { Grid, Button, Stack, Typography } from '@mui/material';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import * as BookingUtility from '../../bookingUtility';
import { actions } from '@/containers/commonConstants';
import { useTranslation } from 'react-i18next';

export const LocationCard = (props) => {
  const { location, isNextAvailableSlotFetching, getNextAvailableSlot, getClinicSchedule, handleNavigationCallback } =
    props;
  const { t } = useTranslation();
  const [showClinicDetails, setShowClinicDetails] = useState(false);
  const locationPhoneNumber = BookingUtility.getLocationPhoneNumber(location);

  const handleClinicDetailsToggle = () => {
    setShowClinicDetails(!showClinicDetails);
  };

  return (
    <Grid container key={location.resource.id}>
      <Grid item xs={12} sm={8}>
        <Stack direction="column" justifyContent="flex-start" alignItems="left">
          <Typography variant="h6" sx={{ color: 'text.secondary', fontSize: 18 }}>
            {' '}
            {location.resource.name}{' '}
          </Typography>
          <Stack direction="row">
            <LocationOnIcon sx={{ mt: '3px', fontSize: 'medium' }} />
            <Typography variant="body">{BookingUtility.getAddress(location.resource)}</Typography>
          </Stack>
          {locationPhoneNumber && (
            <Stack direction="row" alignItems="center">
              <Typography variant="body" sx={{ mr: 1 }}>
                {'Clinic Number: '}
              </Typography>
              <Typography variant="body">{locationPhoneNumber}</Typography>
            </Stack>
          )}
          {location.resource.distance && (
            <Typography variant="body" sx={{ color: 'text.secondary' }}>
              {location.resource.distance} km
            </Typography>
          )}
          <Typography variant="body">
            <Stack direction="row">
              {isNextAvailableSlotFetching ? (
                <>
                  <Typography variant="body" sx={{ mr: 1 }}>
                    {'Next Available: '}
                  </Typography>
                  <Typography variant="body" sx={{ color: 'text.secondary' }}>
                    {t('loading')}
                  </Typography>
                </>
              ) : (
                getNextAvailableSlot(location) && (
                  <>
                    <Typography variant="body" sx={{ mr: 1 }}>
                      {'Next Available: '}
                    </Typography>
                    <Typography variant="body" sx={{ color: 'text.secondary' }}>
                      {getNextAvailableSlot(location)}
                    </Typography>
                  </>
                )
              )}
            </Stack>
          </Typography>
          <Stack direction="row" alignItems="center">
            {!showClinicDetails ? (
              <>
                <Typography
                  variant="button"
                  color="primary"
                  sx={{ cursor: 'pointer' }}
                  onClick={handleClinicDetailsToggle}
                >
                  {t('moreDetails')}
                </Typography>
                <KeyboardArrowDownIcon onClick={handleClinicDetailsToggle} color="primary" sx={{ cursor: 'pointer' }} />
              </>
            ) : (
              <>
                <Typography
                  variant="button"
                  color="primary"
                  sx={{ cursor: 'pointer' }}
                  onClick={handleClinicDetailsToggle}
                >
                  {t('lessDetails')}
                </Typography>
                <KeyboardArrowUpIcon onClick={handleClinicDetailsToggle} color="primary" sx={{ cursor: 'pointer' }} />
              </>
            )}
          </Stack>
          {showClinicDetails ? getClinicSchedule(location) : ''}
        </Stack>
      </Grid>
      <Grid
        item
        xs={12}
        sm={4}
        sx={{ display: 'flex', justifyContent: { xs: 'center', sm: 'flex-end' }, alignItems: 'flex-start' }}
      >
        <Button variant="outlined" onClick={() => handleNavigationCallback(actions.NEXT, location)}>
          {t('bookingAppointment')}
        </Button>
      </Grid>
    </Grid>
  );
};
