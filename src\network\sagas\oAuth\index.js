/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { OAUTH_ERROR, OAUTH_REQUEST, OAUTH_SUCCESS } from '../../../redux/actions/oAuth';

import { makeApiOauth } from './api';

/**
 *
 * @param {*} action
 */
/**
 * 

/**
 *
 * @param {*} action
 */

export function* fetchToken(action) {
  try {
    const response = yield call(makeApiOauth, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: OAUTH_SUCCESS, payload: data });
    } else {
      yield put({ type: OAUTH_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: OAUTH_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchOauth() {
  yield takeEvery(OAUTH_REQUEST, fetchToken);
}
