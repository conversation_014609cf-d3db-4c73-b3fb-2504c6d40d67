import React, { Fragment, useEffect, useMemo, useState } from 'react';
import { Button, Grid, Typography, Paper, Box, Divider } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { getBookedAppointmentData, getBookedSlotDetails, getAppointmentAdditionalInfo } from '../bookingUtility';
import { useSelector, useDispatch } from 'react-redux';
import { getOrganizationAndWidgetId } from '@/containers/commonUtility';
import { confirmAppointmentDetails } from '@/redux/actions/confirmAppointment';
import { ActionModal } from '@/components';
import { cancelBooking } from '@/redux/actions/cancelBooking';
import useNotification from '@/hooks/useNotification';
import { Loader } from '@/components';
import { AppointmentConfirmationStatus } from '../bookingConstants';
import * as BookingUtility from '../bookingUtility';

export const BookingSummary = (props) => {
  const {
    demographic,
    selectedLocation,
    selectedService,
    rescheduleAppointmentCallback,
    getBookedAppointmentSuccess,
    getBookedAppointmentSuccessData,
    cancelAppointmentCallback,
    handleBookAgainCallback,
    header,
    subHeader,
    appointmentId,
    isActionEnabled,
    matchedAction,
    isActionPerformed,
    cancelPayload,
    modificationFlag,
  } = props;

  const [appointmentConfirmationStatus, setAppointmentConfirmationStatus] = useState(
    BookingUtility.getAppointmentConfirmationStatus(getBookedAppointmentSuccessData),
  );

  const [appointmentData, setAppointmentData] = useState(null);
  const [bookedSlot, setBookedSlot] = useState(null);
  const { t } = useTranslation();
  const [organizationId] = getOrganizationAndWidgetId();

  const { widgetDetailsSuccessData } = useSelector((state) => state.widgetDetailsReducer);

  let { bookingSummaryDescription, preConfirmationMessage, confirmationMessage, action } =
    widgetDetailsSuccessData || {};

  preConfirmationMessage = preConfirmationMessage || t('preConfirmationMessage');
  confirmationMessage = confirmationMessage || t('confirmationMessage');

  const { additionalText, additionalLink } = useMemo(
    () => getAppointmentAdditionalInfo(appointmentData?.appointmentDetails),
    [appointmentData],
  );
  useEffect(() => {
    if (getBookedAppointmentSuccess && getBookedAppointmentSuccessData) {
      setAppointmentData(getBookedAppointmentData(getBookedAppointmentSuccessData));
    }
  }, [getBookedAppointmentSuccess, getBookedAppointmentSuccessData]);

  useMemo(() => {
    if (appointmentData) {
      setBookedSlot(getBookedSlotDetails(appointmentData?.appointmentDetails));
    }
  }, [appointmentData]);

  const getClinicAddress = () => {
    const { address } = selectedLocation?.resource || {};
    let clinicAddress = '';
    if (selectedLocation) {
      clinicAddress = `${address?.line[0]}, ${address?.city}, ${address?.state}, ${address?.country}, ${address?.postalCode}`;
    }

    return clinicAddress;
  };
  const redirectButtonName = action?.actionButton;

  //**** cancellation code ****
  const {
    cancelBookingFetching,
    cancelBookingSuccess,
    cancelBookingSuccessData,
    cancelBookingError,
    cancelBookingErrorData,
  } = useSelector((state) => state.cancelBookingReducer);
  const dispatch = useDispatch();

  const [isFreshRecordFetched, setIsFreshRecordFetched] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [, sendNotification] = useNotification();

  const handleOpenModal = () => {
    setModalOpen(true);
  };
  const handleCloseModal = () => {
    setModalOpen(false);
  };

  const handleCancelAppointment = () => {
    handleCloseModal();
    //cancelAppointmentCallback(appointmentId);
    dispatch(cancelBooking({ appointmentId, cancelData: cancelPayload, organizationId }));
  };

  useEffect(() => {
    if (isFreshRecordFetched && cancelBookingSuccess && cancelBookingSuccessData) {
      //setHeading(t('cancelSummaryHeading'));
      //setSubHeading(t('cancelSummarySubHeading'));
      //setIsCancelSummaryPage(true);
      cancelAppointmentCallback(appointmentId);
    }
  }, [cancelBookingSuccess, cancelBookingSuccessData]);

  useEffect(() => {
    if (isFreshRecordFetched && cancelBookingError && cancelBookingErrorData) {
      sendNotification({ msg: t('apiError'), variant: 'error' });
    }
  }, [cancelBookingError, cancelBookingErrorData]);

  useEffect(() => {
    if (cancelBookingFetching) {
      setIsFreshRecordFetched(true);
    }
  }, [cancelBookingFetching]);

  /**** confirm appointment ****/
  const {
    confirmAppointmentFetching,
    confirmAppointmentSuccess,
    confirmAppointmentError,
    confirmAppointmentData,
    confirmAppointmentSuccessData,
  } = useSelector((state) => state.confirmAppointmentReducer);

  useEffect(() => {
    if (isFreshRecordFetched && confirmAppointmentSuccess && confirmAppointmentSuccessData) {
      console.log('confirmAppointmentSuccessData: ', confirmAppointmentSuccessData);
      setAppointmentConfirmationStatus(AppointmentConfirmationStatus.ACCEPTED);
    }
  }, [confirmAppointmentSuccess, confirmAppointmentSuccessData]);

  useEffect(() => {
    if (isFreshRecordFetched && confirmAppointmentError && confirmAppointmentData) {
      sendNotification({ msg: t('apiError'), variant: 'error' });
    }
  }, [confirmAppointmentError, confirmAppointmentData]);

  useEffect(() => {
    if (confirmAppointmentFetching) {
      setIsFreshRecordFetched(true);
    }
  }, [confirmAppointmentFetching]);

  const confirmAppointmentHandle = () => {
    dispatch(
      confirmAppointmentDetails({
        appointmentId: appointmentId,
        organizationId,
      }),
    );
  };

  return (
    <Fragment>
      <Loader active={cancelBookingFetching || confirmAppointmentFetching} />
      <Grid sx={{ mt: 2, mx: '2%', px: { xs: 2, md: 4 } }}>
        <Box sx={{ my: 2 }}>
          <Typography variant="h5">{header}</Typography>
        </Box>
        <Typography variant={'body'} dangerouslySetInnerHTML={{ __html: subHeader }}></Typography>

        <Paper sx={{ px: 2, my: 2 }}>
          <Grid container sx={{ my: 2 }}>
            <Grid item xs={12} sm={3} md={3}>
              <Typography>{t('appointment')}</Typography>
            </Grid>
            <Grid item xs={12} sm={5} md={5}>
              <Typography>{selectedService?.cambianReferenceData?.display || ''}</Typography>
            </Grid>
          </Grid>
          <Divider sx={{ my: 2 }} variant="fullWidth" />
          <Grid container sx={{ my: 2 }}>
            <Grid item xs={12} sm={3} md={3}>
              <Typography>{t('individual')}</Typography>
            </Grid>
            <Grid item xs={12} sm={5} md={5}>
              {demographic?.individuals?.map((individual, index) => (
                <Typography
                  variant="body1"
                  key={index}
                >{`${individual?.firstName} ${individual?.lastName}`}</Typography>
              )) || ''}
            </Grid>
          </Grid>
          <Divider sx={{ my: 2 }} variant="fullWidth" />
          <Grid container sx={{ my: 2 }}>
            <Grid item xs={12} sm={3} md={3}>
              <Typography>{t('location')}</Typography>
            </Grid>
            <Grid item xs={12} sm={5} md={5}>
              <Typography>{selectedLocation?.resource?.name}</Typography>
              <Typography>{getClinicAddress()}</Typography>
            </Grid>
          </Grid>
          <Divider sx={{ my: 2 }} variant="fullWidth" />
          <Grid container sx={{ my: 2 }}>
            <Grid item xs={12} sm={3} md={3}>
              <Typography>{t('dateAndTime')}</Typography>
            </Grid>
            <Grid item xs={12} sm={5} md={5}>
              <Typography>{bookedSlot?.date}</Typography>
              <Typography>
                {bookedSlot?.start} - {bookedSlot?.end}{' '}
              </Typography>
              <Typography>({bookedSlot?.numberOfAppointments} Appointment)</Typography>
            </Grid>
          </Grid>
          <Divider sx={{ my: 2 }} variant="fullWidth" />
          <Grid container alignItems="center" sx={{ my: 2 }}>
            <Grid item xs={12} sm={3} md={3}>
              <Typography>{t('addToCalendar')}</Typography>
            </Grid>
            <Grid item xs={12} sm={5} md={5} sx={{ ml: -1 }}>
              {appointmentData &&
                appointmentData.links?.length > 0 &&
                appointmentData.links.map((link, index) => {
                  return (
                    link.relation !== 'self' && (
                      <Button
                        variant="text"
                        href={link.url}
                        target="_blank"
                        key={index}
                        sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' } }}
                      >
                        {link.relation}
                      </Button>
                    )
                  );
                })}
            </Grid>
          </Grid>
          <Divider sx={{ my: 2 }} variant="fullWidth" />

          <Grid container sx={{ my: 2 }}>
            <Grid item xs={12} sm={3} md={3}>
              <Typography>{t('confirmation')}</Typography>
            </Grid>
            <Grid item xs={12} sm={9} md={9}>
              {appointmentConfirmationStatus == AppointmentConfirmationStatus.TENTATIVE ? (
                <>
                  {modificationFlag ? (
                    <Button
                      variant="contained"
                      onClick={() => confirmAppointmentHandle()}
                      sx={{ minWidth: 'fit-content' }}
                    >
                      {t('confirmAppointment')}
                    </Button>
                  ) : (
                    <Typography>{preConfirmationMessage}</Typography>
                  )}
                </>
              ) : (
                <>
                  <Typography>{confirmationMessage}</Typography>
                </>
              )}
            </Grid>
          </Grid>
          <Divider sx={{ my: 2 }} variant="fullWidth" />

          <Grid container sx={{ my: 2 }} alignItems="center">
            <Grid item xs={12} sm={3} md={3}>
              {t('makeChanges')}
            </Grid>
            <Grid
              item
              xs={12}
              sm={5}
              md={5}
              sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}
            >
              <Button
                variant="text"
                sx={{ p: 0, '&:hover': { backgroundColor: 'transparent', color: '#23527c' } }}
                onClick={() => rescheduleAppointmentCallback(appointmentId)}
              >
                {t('rescheduleAppointment')}
              </Button>
              <Button
                variant="text"
                sx={{ p: 0, '&:hover': { backgroundColor: 'transparent', color: '#23527c' } }}
                onClick={handleOpenModal}
              >
                {t('cancelAppointment')}
              </Button>
            </Grid>
          </Grid>

          {(additionalText || additionalLink || bookingSummaryDescription) && (
            <>
              <Divider sx={{ my: 2 }} variant="fullWidth" />
              <Grid container alignItems="center" sx={{ my: 2 }}>
                <Grid item xs={12} sm={3} md={3}>
                  <Typography>{t('additionalInformation')}</Typography>
                </Grid>
                <Grid item xs={12} sm={9} md={9} sx={{ ml: 0 }}>
                  {additionalText && <Typography>{additionalText}</Typography>}
                  {additionalLink && (
                    <Button
                      variant="text"
                      href={additionalLink}
                      target="_blank"
                      sx={{ p: 0, '&:hover': { backgroundColor: 'transparent' } }}
                    >
                      {additionalLink}
                    </Button>
                  )}
                  {bookingSummaryDescription && <Typography>{bookingSummaryDescription}</Typography>}
                </Grid>
              </Grid>
            </>
          )}

          {!modificationFlag && isActionEnabled && !isActionPerformed && Object.keys(matchedAction).length > 0 && (
            <>
              <Divider sx={{ my: 2 }} variant="fullWidth" />
              <Grid container sx={{ my: 2 }} alignItems="center">
                <Grid item xs={12} sm={3} md={3}>
                  {t('nextStep')}
                </Grid>
                <Grid item xs={12} sm={6} md={6}>
                  <Button variant="contained" onClick={() => handleBookAgainCallback()}>
                    {redirectButtonName || t('next')}
                  </Button>
                </Grid>
              </Grid>
            </>
          )}
        </Paper>
      </Grid>
      <ActionModal
        open={modalOpen}
        handleClose={handleCloseModal}
        handleAction={handleCancelAppointment}
        headerText="appointmentCancelModalHeaderText"
        messageText="appointmentCancelModalMessage"
        cancelText="appointmentCancelModalCancelText"
        actionText="appointmentCancelModalActionText"
      />
    </Fragment>
  );
};
