export const CHECK_EXISTING_CONNECTION_INDEX_REQUEST = 'CHECK_EXISTING_CONNECTION_INDEX_REQUEST';
export const CHECK_EXISTING_CONNECTION_INDEX_SUCCESS = 'CHECK_EXISTING_CONNECTION_INDEX_SUCCESS';
export const CHECK_EXISTING_CONNECTION_INDEX_ERROR = 'CHECK_EXISTING_CONNECTION_INDEX_ERROR';
export const CHECK_EXISTING_CONNECTION_INDEX_RESET = 'CHECK_EXISTING_CONNECTION_INDEX_RESET';

export function checkExistingConnectionIndex(data) {
  return {
    type: CHECK_EXISTING_CONNECTION_INDEX_REQUEST,
    payload: data,
  };
}

export function checkExistingConnectionIndexSuccess(data) {
  return {
    type: CHECK_EXISTING_CONNECTION_INDEX_SUCCESS,
    payload: data,
  };
}

export function checkExistingConnectionIndexError(data) {
  return {
    type: CHECK_EXISTING_CONNECTION_INDEX_ERROR,
    payload: data,
  };
}

export const resetCheckExistingConnectionIndex = () => {
  return {
    type: CHECK_EXISTING_CONNECTION_INDEX_RESET,
  };
};
