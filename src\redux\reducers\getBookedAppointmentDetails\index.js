/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import {
  GET_BOOKING_ERROR,
  GET_BOOKING_REQUEST,
  GET_BOOKING_SUCCESS,
  GET_BOOKING_RESET,
} from '../../actions/getBookedAppointmentDetails';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const getBookedAppointmentReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case GET_BOOKING_REQUEST:
      return {
        ...state,
        getBookedAppointmentFetching: true,
        getBookedAppointmentSuccess: false,
        getBookedAppointmentError: false,
        getBookedAppointmentErrorData: null,
      };
    case GET_BOOKING_SUCCESS: {
      return {
        ...state,
        getBookedAppointmentFetching: false,
        getBookedAppointmentSuccess: true,
        getBookedAppointmentError: false,
        getBookedAppointmentErrorData: null,
        getBookedAppointmentSuccessData: payload,
      };
    }
    case GET_BOOKING_ERROR:
      return {
        ...state,
        getBookedAppointmentFetching: false,
        getBookedAppointmentSuccess: false,
        getBookedAppointmentError: true,
        getBookedAppointmentErrorData: payload,
      };
    case GET_BOOKING_RESET:
      return {
        ...state,
        getBookedAppointmentFetching: false,
        getBookedAppointmentSuccess: false,
        getBookedAppointmentError: false,
        getBookedAppointmentErrorData: null,
        getBookedAppointmentSuccessData: null,
      };
    default:
      return state;
  }
};
