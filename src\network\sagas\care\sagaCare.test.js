import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/care/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { fetchTypcode } from './index';
/**
 * This function test test case for get care details saga
 * Fires get care success of api gives success
 * Fires get care error of api fails
 */

describe('care', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiFetchTypCode = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchCare(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchTypcode, requestResult).done;

    let successResult = actions.fetchCareSuccess(DUMMY_ITEM.data);

    expect(api.makeApiFetchTypCode.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchTypCode = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchCare(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchTypcode, requestResult).done;

    expect(api.makeApiFetchTypCode.mock.calls.length).toBe(1);

    let errorResult = actions.fetchCareError();
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.completeBookingApi = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchCare(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchTypcode, requestResult).done;

    expect(api.makeApiFetchTypCode.mock.calls.length).toBe(2);

    let resetResult = actions.resetCare();
    const expectedAction = {
      type: actions.CARE_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
