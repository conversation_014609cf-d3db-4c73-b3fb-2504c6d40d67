/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';

import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  GET_BOOKING_ERROR,
  GET_BOOKING_SUCCESS,
  GET_BOOKING_REQUEST,
} from '../../../redux/actions/getBookedAppointmentDetails';

import { getBookedAppointmentApi } from './api';

/**
 *
 * @param {*} action
 */
export function* getBookedAppointment(action) {
  try {
    const response = yield call(getBookedAppointmentApi, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: GET_BOOKING_SUCCESS, payload: data });
    } else {
      yield put({ type: GET_BOOKING_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: GET_BOOKING_ERROR });
  }
}

// Our watcher Saga:
export function* watchGetBookedAppointment() {
  yield takeEvery(GET_BOOKING_REQUEST, getBookedAppointment);
}
