if (process.env.NODE_ENV === 'development') {
  const dotenvExpand = require('dotenv-expand');
  const dotenvFlow = require('dotenv-flow');
  const path = require('path');

  /**
   * Default dotenv variables flow in Next.js doesn't support custom modes: staging, qa, etc.
   * Here we initialize our own custom dotenv-flow with dotenv-expand.
   * This is only done in development. Other env uses
   */
  const options = {
    path: path.join(process.cwd(), 'environments'),
    files: ['.env', `${process.env.APP_ENV}/.env.${process.env.BUILD_ENV}`],
    purge_dotenv: true, // Disabling Next.js dotenv
    //  debug: true,
  };
  const config = dotenvFlow.config(options);

  dotenvExpand.expand(config);
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config) => {
    config.ignoreWarnings = [
      // https://webpack.js.org/configuration/other-options/#ignorewarnings
      {
        module: /node-fetch/,
        message: /.*Can't resolve 'encoding'.*/,
      },
    ];

    return config;
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? { exclude: ['error'] } : false,
  },
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
};

// Check for Next Output environment variable, required for containerization
if (process.env.NEXT_OUTPUT_STANDALONE === 'true') {
  nextConfig.output = 'standalone';
}

module.exports = nextConfig;
