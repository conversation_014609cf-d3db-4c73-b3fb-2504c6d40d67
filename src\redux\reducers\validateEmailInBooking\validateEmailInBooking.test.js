import {
  VALIDATE_EMAIL_IN_BOOKING_REQUEST,
  VALIDATE_EMAIL_IN_BOOKING_SUCCESS,
} from '../../actions/validateEmailInBooking';
import * as G<PERSON><PERSON>BA<PERSON> from '../globals';
import { validateEmailInBookingReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isValidateEmailInBookingFetching: true,
  validateEmailInBookingSuccess: false,
  validateEmailInBookingError: false,
  validateEmailInBookingErrorData: null,
};

const successState = {
  isValidateEmailInBookingFetching: false,
  validateEmailInBookingSuccess: true,
  validateEmailInBookingError: false,
  validateEmailInBookingErrorData: null,
  validateEmailInBookingSuccessData: data,
};

describe('Validate Email In Booking Reducer', () => {
  it('should return the initial state', () => {
    expect(validateEmailInBookingReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle VALIDATE_EMAIL_IN_BOOKING_REQUEST', () => {
    expect(
      validateEmailInBookingReducer(initialState, {
        type: VALIDATE_EMAIL_IN_BOOKING_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle VALIDATE_EMAIL_IN_BOOKING_SUCCESS', () => {
    expect(
      validateEmailInBookingReducer(initialState, {
        type: VALIDATE_EMAIL_IN_BOOKING_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });
});
