/*
 *
 */
import { makeNetworkCall } from '../..';
import { START_QUESTIONNAIRE, BASE_URL, CAMBIAN_SERVICE_BASE_URL } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiStartQuestionnaire(action) {
  const { payload = {} } = action || {};
  const { headers = {}, data } = payload || {};

  const mainUrl = CAMBIAN_SERVICE_BASE_URL + START_QUESTIONNAIRE;

  const config = {
    method: 'POST',
    url: mainUrl,
    headers: headers,
    data: data,
  };
  return makeNetworkCall(config);
}
