/*
 *
 */
import { makeNetworkCall } from '../..';
import { EMAIL, ORGANIZATION_ID } from '../../../utils/constants';
import { BASE_URL, CAMBIAN_SERVICE_BASE_URL, SEND_CONNECTION_INVITE } from '../../../utils/constants/apiEndpoints';

export function makeApiSendConnectionInvite(action) {
  const { payload = {} } = action || {};
  const { organizationId, email, headers } = payload || {};
  const config = {
    method: 'POST',
    url:
      CAMBIAN_SERVICE_BASE_URL + SEND_CONNECTION_INVITE.replace(ORGANIZATION_ID, organizationId).replace(EMAIL, email),
    headers,
  };
  return makeNetworkCall(config);
}
