/*
 *
 */

import { makeNetworkCall } from '../..';
import { BASE_URL, CAMBIAN_SERVICE_BASE_URL, GET_AUTH_TOKEN } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiOauth(action) {
  const { payload = {} } = action || {};
  let { headers = {}, oAuthData } = payload || {};
  const url = CAMBIAN_SERVICE_BASE_URL + GET_AUTH_TOKEN;
  let formBody = [];
  for (let loginProperty in oAuthData) {
    let encodedKey = encodeURIComponent(loginProperty);
    let encodedValue = encodeURIComponent(oAuthData[loginProperty]);
    formBody.push(encodedKey + '=' + encodedValue);
  }
  formBody = formBody.join('&');
  const config = {
    method: 'POST',
    url: url,
    headers: headers,
    formData: true,
    data: formBody,
  };
  return makeNetworkCall(config);
}
