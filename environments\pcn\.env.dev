# This is for the pcn-dev account

NEXT_PUBLIC_BASE_URL = https://widget.dev.alberta-pcn.ca

# * APIs variables
NEXT_PUBLIC_WIDGET_SERVICES_BASE_URL=https://dev.alberta-pcn.ca/widget-config

# Schedular Booking API Gateway
NEXT_PUBLIC_BOOKING_SERVICES_BASE_URL=https://dev.alberta-pcn.ca/scheduler-booking

# Artifact repository
# Private Artifact Repository
NEXT_PUBLIC_ARTIFACT_PRIVATE_REPOSITORY_BASE_URL=https://dev.alberta-pcn.ca/org-artifact-repo

# Public Artifact Repository
NEXT_PUBLIC_ARTIFACT_PUBLIC_REPOSITORY_BASE_URL=https://dev.cambianservices.ca/net-artifact-repo

# CDRs
NEXT_PUBLIC_INDIVIDUAL_CDR_BASE_URL=https://70e4ltesad.execute-api.ca-central-1.amazonaws.com/dev

NEXT_PUBLIC_ORGANIZATION_CDR_BASE_URL=https://wh54glk4na.execute-api.ca-central-1.amazonaws.com/dev

# Indexes
NEXT_PUBLIC_CONNECTION_INDEX_BASE_URL=https://tt835tu6n3.execute-api.ca-central-1.amazonaws.com/dev

NEXT_PUBLIC_CLIENT_INDEX_BASE_URL=https://48x68y0cpk.execute-api.ca-central-1.amazonaws.com/dev

NEXT_PUBLIC_ORGANIZATION_MESSAGING_SERVICE_BASE_URL=https://dev.alberta-pcn.ca/org-messaging

NEXT_PUBLIC_ORGANIZATION_INDIVIDUAL_SERVICE_BASE_URL=https://fq53ljixj1.execute-api.ca-central-1.amazonaws.com/dev

# Are they necessary?
NEXT_PUBLIC_PRACTITIONER_INDEX_IDENTIFIER=3fd7szhb9f
NEXT_PUBLIC_PRACTITIONER_INDEX_STAGE=dev

NEXT_PUBLIC_LOCATION_INDEX_IDENTIFIER=10zk9zdg9f
NEXT_PUBLIC_LOCATION_INDEX_STAGE=dev

# Doc Gen
NEXT_PUBLIC_PDF_GENERATION_SERVICE_BASE_URL=https://dev.alberta-pcn.ca/org-doc-gen

NEXT_PUBLIC_APPSCOOP_WIDGET_SERVICES_REGION=ca-central-1
NEXT_PUBLIC_CAMBIAN_SERVICES_REGION=ca-central-1

NEXT_PUBLIC_AWS_SERVICES_ENVIRONMENT=dev

# Allow access to services in Org Env
COGNITO_ORG_MACHINE_USER_POOL_ID=ca-central-1_Pvl9zam7m
COGNITO_ORG_MACHINE_APP_CLIENT_ID=3ft2ub4jh5p4sl67n2p4e5dhah
COGNITO_ORG_MACHINE_USERNAME=75ff6f77c47f959798446a80d75dcf17

# Allow access to services in Network Env
COGNITO_NETWORK_MACHINE_USER_POOL_ID=ca-central-1_rPnRLwrid
COGNITO_NETWORK_MACHINE_APP_CLIENT_ID=37fo91agquv79k0j7tj66pqffd

# Allow access to services in Individual Env
COGNITO_INDIVIDUAL_MACHINE_USER_POOL_ID=ca-central-1_kQFbifOYL
COGNITO_INDIVIDUAL_MACHINE_APP_CLIENT_ID=1u4ib1n2memof9qcpfmhhi13ot
COGNITO_INDIVIDUAL_MACHINE_USERNAME=11d302c3ff014aca8873fd9030817291

# Cognito User auth variables
COGNITO_INDIVIDUAL_HUMAN_REGION=ca-central-1
COGNITO_INDIVIUDAL_HUMAN_USER_POOL_ID=ca-central-1_t5XYRZzvT
COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_ID=1o6f912kklrpao3i289a2qbum

NEXTAUTH_URL=$NEXT_PUBLIC_BASE_URL
