'use client';
import { Provider } from 'react-redux';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PersistGate } from 'redux-persist/integration/react';
import { SnackbarProvider } from 'notistack';
import { CambianTheme } from '@/components';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { persistor, store } from '../redux';
import '@/i18n';
import { ErrorOutline, InfoOutlined, TaskAlt, WarningAmberOutlined } from '@mui/icons-material';
import { SessionProvider } from 'next-auth/react';

export default function Wrappers({ children }) {
  const snackbarIconVariants = {
    success: <TaskAlt fontSize="small" />,
    error: <ErrorOutline fontSize="small" />,
    info: <InfoOutlined fontSize="small" />,
    warning: <WarningAmberOutlined fontSize="small" />,
  };

  const queryClient = new QueryClient();

  return (
    <QueryClientProvider client={queryClient}>
      <SessionProvider refetchOnWindowFocus={false}>
        <Provider store={store}>
          {/* <PersistGate loading={null} persistor={persistor}> */}
          <ThemeProvider theme={CambianTheme}>
            <CssBaseline />
            <SnackbarProvider iconVariant={snackbarIconVariants} maxSnack={3}>
              {children}
            </SnackbarProvider>
          </ThemeProvider>
          {/* </PersistGate> */}
        </Provider>
      </SessionProvider>
    </QueryClientProvider>
  );
}
