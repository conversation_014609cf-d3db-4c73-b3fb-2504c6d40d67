'use client';
import { Download, Print, Save } from '@mui/icons-material';
import { getCsrfToken } from 'next-auth/react';
import {
  API_HEADER_AUTHORIZATION_KEY,
  API_HEADER_CAMIBIAN_AUTHORIZATION_KEY,
  API_HEADER_AUTHORIZATION_VALUE,
  API_HEADER_AUTHORIZATION_BASIC,
  API_HEADER_AUTHORIZATION_ACCESS_TOKEN_KEY,
  API_HEADER_CONTENT_TYPE,
  API_HEADER_CONTENT_TYPE_JSON_VALUE,
  API_HEADER_CONTENT_TYPE_MULTIPART_FORM_DATA,
  API_HEADER_FORM_URL_ENCODED,
} from '../utils/constants/apiCodes';
import {
  BOOKING_WIDGET,
  ORGANIZATION_USER,
  QUESTIONNAIRE_WIDGET,
  REGISTRATION_WIDGET,
  CURRENT_DATE,
} from './commonConstants';
import {
  generateAPIHeader,
  generateAPIHeaderForCambianUserInfoEndPoint,
  generateAPIWithCambianHeader,
} from '../utils/constants/common';
import { CODE } from '../utils/constants';
import { userLoggedOut } from '../redux/actions/logOut';
import { resetToken } from '../redux/actions/oAuth';
import { resetUserInfo } from '../redux/actions/userInfo';
import { BASE_URL } from '../utils/constants/apiEndpoints';
import { resetGetIndividualData } from '@/redux/actions/getIndividualData';
import { resetCheckExistingClientIndex } from '@/redux/actions/checkExistingClientIndex';
import { resetCheckExistingConnectionIndex } from '@/redux/actions/checkExistingConnectionIndex';
import { resetCreateConnectionAtClientIndex } from '@/redux/actions/createConnectionAtClientIndex';
import { resetCreateConnectionAtConnectionIndex } from '@/redux/actions/createConnectionAtConnectionIndex';
import { resetUpdateConnectionAtClientIndex } from '@/redux/actions/updateConnectionAtClientIndex';
import { resetSearchIndividualAtClientIndex } from '@/redux/actions/searchIndividualAtClientIndex';

export const getOrganizationAndWidgetId = () => {
  let URL = window.location.href;
  let arrayOfIds = URL.split('/');
  const orgId = arrayOfIds[5];
  const widgetId = arrayOfIds?.[7]?.split('?')?.[0] || '';

  return [orgId, widgetId];
};

//not in use
export function formUrlEncodedAPIHeader() {
  const headers = {
    [API_HEADER_CONTENT_TYPE]: API_HEADER_FORM_URL_ENCODED,
  };
  return headers;
}

//not in use
export function formUrlEncodedAPIHeaderWithToken(accessToken) {
  const headers = {
    [API_HEADER_AUTHORIZATION_KEY]: API_HEADER_AUTHORIZATION_VALUE.replace(
      API_HEADER_AUTHORIZATION_ACCESS_TOKEN_KEY,
      accessToken,
    ),
    [API_HEADER_CONTENT_TYPE]: API_HEADER_FORM_URL_ENCODED,
  };
  return headers;
}

//in use
export function formUrlEncodedAPIHeaderWithBasicToken(accessToken) {
  const headers = {
    [API_HEADER_AUTHORIZATION_KEY]: API_HEADER_AUTHORIZATION_BASIC.replace(
      API_HEADER_AUTHORIZATION_ACCESS_TOKEN_KEY,
      accessToken,
    ),
    [API_HEADER_CONTENT_TYPE]: API_HEADER_FORM_URL_ENCODED,
  };
  return headers;
}

//not in use
export function generateAPIHeaderWithoutAccessToken() {
  const headers = {
    [API_HEADER_CONTENT_TYPE]: API_HEADER_CONTENT_TYPE_JSON_VALUE,
  };
  return headers;
}

export const getHeader = (access_token, flow) => {
  let headers = {};
  if (access_token && flow === ORGANIZATION_USER) {
    headers = generateAPIWithCambianHeader(access_token);
  } else {
    headers = generateAPIHeader();
  }
  return headers;
  // return generateAPIHeader();
};

export function getParamFromUrl(param) {
  const query = new URLSearchParams(window.location.search);
  return query.get(param);
}

export function isParamInUrl(param) {
  const query = new URLSearchParams(window.location.search);
  return query.get(param);
}

export function extractExtension(extensionArray, url) {
  let matchingExtension = null;
  if (extensionArray !== undefined && Array.isArray(extensionArray) && url !== undefined && url !== null) {
    extensionArray.forEach((ext) => {
      if (ext.url.toUpperCase().includes(url.toUpperCase())) {
        matchingExtension = ext;
      }
    });
  }
  return matchingExtension;
}

export function extractAllMatchingExtension(extensionArray, url) {
  let result;
  if (extensionArray !== undefined) {
    result = extensionArray.filter((extension) => url === extension.url);
  }
  return result;
}

export const removeParams = () => {
  const params = new URLSearchParams(window.location.search);
  params.delete(CODE);
  let url = window.location.pathname.split('?')[0];
  var lastChar = url.charAt(url.length - 1);
  if (lastChar === '/') {
    url = url.slice(0, -1);
  }
  window.history.replaceState({}, '', `${url}`);
};

export const removeURLParams = (url) => {
  var urlObject = new URL(url);
  urlObject.search = '';
  return urlObject.toString();
};

export const handleLogout = ({ access_token, dispatch, setDemographic, currentURL }) => {
  if (access_token) {
    const headers = generateAPIHeaderForCambianUserInfoEndPoint(access_token);
    dispatch(userLoggedOut({ headers }));
    dispatch(resetToken());
    dispatch(resetUserInfo());
    dispatch(resetGetIndividualData());
    setDemographic(null);
    window.location.href = currentURL;
  }
};

export const getIsApplicationInIframe = () => {
  try {
    return window.self !== window.top;
  } catch (e) {
    return true;
  }
};

export const downloadPDF = (pdf, filename) => {
  const fileSource = `data:application/pdf;base64,${pdf}`;
  const downloadLink = document.createElement('a');
  const fileName = filename;

  downloadLink.href = fileSource;
  downloadLink.download = fileName;
  downloadLink.click();
};

export const getBrowserName = () => {
  if ((navigator.userAgent.indexOf('Opera') || navigator.userAgent.indexOf('OPR')) != -1) {
    return 'Opera';
  } else if (navigator.userAgent.indexOf('Edg') != -1) {
    return 'Edge';
  } else if (navigator.userAgent.indexOf('Chrome') != -1) {
    return 'Chrome';
  } else if (navigator.userAgent.indexOf('Safari') != -1) {
    return 'Safari';
  } else if (navigator.userAgent.indexOf('Firefox') != -1) {
    return 'Firefox';
  } else if (navigator.userAgent.indexOf('MSIE') != -1 || !!document.documentMode == true) {
    //IF IE > 10
    return 'IE';
  } else {
    return 'unknown';
  }
};

export const getPrintDownloadIconsConfiguration = () => {
  const browserName = getBrowserName();
  //default chrome
  let icons = {
    PrintIcon: Print,
    DownloadIcon: Download,
    downloadTooltip: 'Download',
    printTooltip: 'Print',
    order: 'download,print',
  };

  if (browserName === 'Edge') {
    icons.DownloadIcon = Save;
    icons.downloadTooltip = 'Save';
    icons.printTooltip = 'Print';
    icons.order = 'print,download';
  } else if (browserName === 'Firefox') {
    icons.DownloadIcon = Save;
    icons.downloadTooltip = 'Save';
    icons.order = 'print,download';
  }

  return icons;
};

export const removeHtmlLoader = () => {
  const element = document.querySelector('#html-loader');
  if (element) {
    element.remove();
  }
};

export const getWidgetUrl = (widgetType, widgetId) => {
  const [organizationId] = getOrganizationAndWidgetId();
  let URL = '';
  if (widgetType === QUESTIONNAIRE_WIDGET) {
    URL = `${BASE_URL}/widget/organizations/${organizationId}/questionnaireWidget/${widgetId}`;
  } else if (widgetType === BOOKING_WIDGET) {
    URL = `${BASE_URL}/widget/organizations/${organizationId}/bookingWidget/${widgetId}`;
  } else if (widgetType === REGISTRATION_WIDGET) {
    URL = `${BASE_URL}/widget/organizations/${organizationId}/registrationWidget/${widgetId}`;
  }

  return URL;
};

export const openUrlInNewTab = (url, params) => {
  let URL = `${url}${params || ''}`;
  setTimeout(() => {
    let actionUrl = URL.includes('http') ? URL : `https://${URL}`;
    if (url) {
      window.open(actionUrl);
    }
  }, 200);
};

export const openUrlInSameTab = (url, params) => {
  let URL = `${url}${params || ''}`;

  setTimeout(() => {
    let actionUrl = URL.includes('http') ? URL : `https://${URL}`;
    if (url) {
      window.open(actionUrl, '_parent', 'status=yes');
    }
  }, 200);
};

export const openInSameIFrame = (url, params) => {
  let URL = `${url}${params || ''}`;
  setTimeout(() => {
    if (url) {
      window.open(URL, '_self', 'status=yes');
    }
  }, 200);
};

export const openInNewWindow = (url, params) => {
  const width = window.innerWidth; // Current window width
  const height = window.innerHeight; // Current window height
  const left = window.screenX; // Current window's X position
  const top = window.screenY; // Current window's Y position

  const URL = `${url}${params || ''}`;

  setTimeout(() => {
    window.open(
      URL,
      '_blank',
      `width=${width},height=${height},top=${top},left=${left},resizable=yes,scrollbars=yes`,
      // `width=${width},height=${height},resizable=yes,scrollbars=yes`,
    );
  }, 200);
};

export const appendIncomingParametersToUrl = (url) => {
  let URL = url;

  const sParam = getParamFromUrl('s');
  const rParam = getParamFromUrl('r');
  const lParam = getParamFromUrl('l');

  if (sParam && sParam !== 'null' && sParam !== 'undefined') {
    URL += (URL.includes('?') ? '&' : '?') + `s=${encodeURIComponent(sParam)}`;
  }

  if (rParam && rParam !== 'null' && rParam !== 'undefined') {
    URL += (URL.includes('?') ? '&' : '?') + `r=${encodeURIComponent(rParam)}`;
  }

  if (lParam && lParam !== 'null' && lParam !== 'undefined') {
    URL += (URL.includes('?') ? '&' : '?') + `l=${encodeURIComponent(lParam)}`;
  }

  return URL;
};

export const performBackgroundServiceAction = (backgroundServiceEndpoint, appendIncomingParameters) => {
  if (!backgroundServiceEndpoint) return;

  let URL = backgroundServiceEndpoint;
  if (appendIncomingParameters) {
    URL = appendIncomingParametersToUrl(URL);
  }

  fetch(URL, { method: 'POST' }).catch((error) => error);
};

export const handleCrossOriginPrint = (reportPageUrl, title) => {
  var p = window.open(reportPageUrl);
  p.onload = function () {
    p.document.title = title;

    const closePopup = () => {
      p.close();
    };
    if ('onafterprint' in p) {
      // FF and IE
      p.onafterprint = closePopup;
    } else {
      // webkit don't support onafterprint
      var mediaQueryList = p.matchMedia('print');
      mediaQueryList.addEventListener('change', mqlListener);
      const mqlListener = (mql) => {
        if (!mql.matches) {
          closePopup();
          mediaQueryList.removeEventListener('change', mqlListener);
        }
      };
    }
  };
  setTimeout(() => {
    p.print();
  }, 2700);
};

export const createClientInfoForAppointment = (demographic) => {
  return {
    preferredContactMechanism: demographic?.preferredContactMethod,
    subscribeToNotifications:
      demographic?.subscribeToNotifications !== null ? demographic?.subscribeToNotifications : true,
    emailAddresses: demographic?.contact.emailAddresses || [],
    phoneNumbers: demographic?.contact.phoneNumbers || [],
    clientIdentifiers: demographic?.clientIdentifiers || [],
  };
};

export const getPdfBlob = (pdf) => {
  const base64str = pdf;
  // decode base64 string, remove space for IE compatibility
  const binary = window.atob(base64str.replace(/\s/g, ''));
  const len = binary.length;
  const buffer = new ArrayBuffer(len);
  const view = new Uint8Array(buffer);
  for (let index = 0; index < len; index++) {
    view[index] = binary.charCodeAt(index);
  }

  const blob = new Blob([view], { type: 'application/pdf' });
  const pdfUrl = URL.createObjectURL(blob);

  return pdfUrl;
};

export const checkAllRequiredDemographicPresent = (fields, demographics) => {
  if (!demographics?.individuals || !demographics?.individuals?.length) return false;

  const allFields = {
    FIRST_NAME: 'firstName',
    MIDDLE_NAME: 'middleName',
    LAST_NAME: 'lastName',
    DATE_OF_BIRTH: 'dateOfBirth',
    GENDER: 'gender',
    IDENTIFICATION: 'healthCareIds',
    EMAIL: 'email',
    PHONE: 'phone',
    PREFERRED_CONTACT_METHOD: 'preferredContactMechanism',
    ADDRESS: 'address',
    NOTIFICATIONS: 'subscribeToNotifications',
  };

  for (const field of fields) {
    if (field?.isMandatory) {
      const fieldName = allFields[field.code];
      if (!fieldName) {
        continue;
      }

      if (!demographics.individuals[0][fieldName] && !demographics.contact[fieldName]) {
        return false;
      }
    }
  }
  return true;
};

export const checkRequiredDemographicsAtClientIndex = (fields, clientIndexDetails) => {
  if (!clientIndexDetails?.clientID) return false;

  const possibleFields = {
    FIRST_NAME: 'firstName',
    MIDDLE_NAME: 'middleName',
    LAST_NAME: 'lastName',
    DATE_OF_BIRTH: 'dateOfBirth',
    GENDER: 'gender',
    IDENTIFICATION: 'healthCareIds',
    EMAIL: 'emailAddresses',
    PHONE: 'phoneNumbers',
    PREFERRED_CONTACT_METHOD: 'preferredContactMechanism',
    ADDRESS: 'addresses',
    NOTIFICATIONS: 'subscribeToNotifications',
  };

  const requiredFields = fields.filter((field) => field.isMandatory).map((field) => field.code);
  const fieldsLabel = requiredFields.map((field) => possibleFields[field]);

  let allRequiredValuesAvailable = false;
  for (let field of fieldsLabel) {
    if (field === 'healthCareIds') {
      if (clientIndexDetails[field] && clientIndexDetails[possibleFields.IDENTIFICATION]) {
        allRequiredValuesAvailable = true;
      } else {
        allRequiredValuesAvailable = false;
        break;
      }
    } else {
      if (clientIndexDetails[field]) {
        allRequiredValuesAvailable = true;
      } else {
        allRequiredValuesAvailable = false;
        break;
      }
    }
  }

  return allRequiredValuesAvailable;
};

export const createUpdateExistingConnectionAtClientIndexData = (demographics, existingData, clientId) => {
  const { individuals = [], contact = {} } = demographics || {};
  const [organizationId] = getOrganizationAndWidgetId();

  const data = {
    ...existingData,
    clientId: clientId,
    dateOfBirth: individuals[0]?.dateOfBirth || existingData?.dateOfBirth || null,
    firstName: individuals[0]?.firstName || existingData?.firstName || null,
    lastName: individuals[0]?.lastName || existingData?.lastName || null,
    middleName: individuals[0]?.middleName || existingData?.middleName || null,
    gender: individuals[0]?.gender || existingData?.gender || null,
    addresses: contact?.addresses ? contact.addresses : existingData.addresses || [],
    dataSource: existingData?.dataSource || 'WIDGET',
    healthCareIds: individuals[0]?.healthCareIds ? individuals[0].healthCareIds : existingData.healthCareIds || [],
    consentAgreementDate: existingData?.consentAgreementDate || CURRENT_DATE || '',
    clientGroups: existingData?.clientGroups || [],
    preferredContactMechanism: demographics?.preferredContactMethod || existingData?.preferredContactMechanism || '',
    emailAddresses: contact?.emailAddresses ? contact.emailAddresses : existingData.emailAddresses || [],
    phoneNumbers: contact?.phoneNumbers ? contact.phoneNumbers : existingData.phoneNumbers || [],
    subscribeToNotifications: demographics?.subscribeToNotifications || existingData?.subscribeToNotifications || true,
  };

  return data;
};

export const resetConnectionAndClientReducers = (dispatch) => {
  dispatch(resetCheckExistingClientIndex());
  dispatch(resetCheckExistingConnectionIndex());
  dispatch(resetCreateConnectionAtClientIndex());
  dispatch(resetCreateConnectionAtConnectionIndex());
  dispatch(resetUpdateConnectionAtClientIndex());
  dispatch(resetSearchIndividualAtClientIndex());
};

export async function fetchWithCsrfToken(url, data) {
  const token = await getCsrfToken();
  if (!token) {
    throw new Error('CSRF token not found');
  }

  const headers = {
    'Content-Type': 'application/json',
    'X-CSRF-Token': token,
  };

  const options = {
    method: 'POST',
    headers,
    body: JSON.stringify(data),
    timeout: process.env.NODE_ENV === 'production' ? 60000 : 0,
  };

  return fetch(url, options);
}

export const createClientData = (demographic, existingData = []) => {
  const [organizationId] = getOrganizationAndWidgetId();

  const data = {
    clientId: demographic?.id || existingData?.clientId,
    cambianId: demographic?.id || existingData?.clientId,
    dateOfBirth: demographic?.individuals[0]?.dateOfBirth || existingData?.dateOfBirth || null,
    firstName: demographic?.individuals[0]?.firstName || existingData?.firstName || null,
    lastName: demographic?.individuals[0]?.lastName || existingData?.lastName || null,
    middleName: demographic?.individuals[0]?.middleName || null,
    gender: demographic?.individuals[0]?.gender || existingData?.gender || null,
    addresses: demographic?.contact?.addresses ? demographic.contact.addresses : existingData.addresses || [],
    dataSource: existingData?.dataSource || 'WIDGET',
    consentAgreementDate: existingData?.consentAgreementDate || CURRENT_DATE || '',
    clientGroups: existingData?.clientGroups || [],
    preferredContactMechanism: demographic?.preferredContactMethod || existingData?.preferredContactMechanism || '',
    subscribeToNotifications: demographic?.subscribeToNotifications || existingData?.subscribeToNotifications || true,
    emailAddresses: demographic?.contact?.emailAddresses
      ? demographic.contact.emailAddresses
      : existingData.emailAddresses || [],
    phoneNumbers: demographic?.contact?.phoneNumbers
      ? demographic.contact.phoneNumbers
      : existingData.phoneNumbers || [],
    healthCareIds: demographic?.individuals[0]?.healthCareIds
      ? demographic.individuals[0].healthCareIds
      : existingData.healthCareIds || [],
  };
  return data;
};

export const getFullName = (firstName, lastName) => {
  if (!firstName && !lastName) throw new Error('Either of the first and last name is required');

  return [firstName, lastName].filter((el) => !!el).join(' ');
};

export const convertStringToDate = (dateStr) => {
  // this function expects date string to be in format:yyyy-MM-dd
  if (!dateStr) return null;

  const [year, month, day] = dateStr?.split('-').map(Number) || ['', '', ''];
  if (!year || !month || !day) return answer;

  return new Date(year, month - 1, day);
};
export const scrollToTop = () => {
  if (getIsApplicationInIframe()) {
    window.parent.postMessage('ScrollIntoView', '*');
  } else {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }
};
