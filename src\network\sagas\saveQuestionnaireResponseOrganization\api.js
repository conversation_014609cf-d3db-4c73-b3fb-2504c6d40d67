import { makeNetworkCall } from '../..';
import { ORGANIZATION_ID } from '../../../utils/constants';
import { SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION } from '../../../utils/constants/awsApiEndpoints';

export function makeApiSaveQuestionnaireResponseOrganization(action) {
  const { payload = {} } = action || {};
  const { headers = {}, data, organizationId } = payload || {};

  const mainUrl = SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION.replace(ORGANIZATION_ID, organizationId);

  const config = {
    method: 'POST',
    url: mainUrl,
    headers: headers,
    data: data,
  };
  return makeNetworkCall(config);
}
