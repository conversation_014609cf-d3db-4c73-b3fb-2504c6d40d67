import { GET_LOCATIONS_REQUEST, GET_LOCATIONS_SUCCESS, GET_LOCATION_ERROR } from '../../actions/getLocations/index';
import * as GLOBALS from '../globals';
import { getLocationsReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isLocationsFetching: true,
  isLocationsSuccess: false,
  isLocationsError: false,
  locationsErrorData: null,
};

const successState = {
  isLocationsFetching: false,
  isLocationsSuccess: true,
  isLocationsError: false,
  locationsErrorData: null,
  locationsSuccessData: data,
};

const errorState = {
  isLocationsFetching: false,
  isLocationsSuccess: false,
  isLocationsError: true,
  locationsErrorData: data,
};

describe('Locations Details Reducer', () => {
  it('should return the initial state', () => {
    expect(getLocationsReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle GET_LOCATIONS_REQUEST', () => {
    const result = getLocationsReducer(initialState, {
      type: GET_LOCATIONS_REQUEST,
    });
    expect({ ...result, ...requestState }).toEqual({
      ...initialState,
      ...requestState,
    });
  });

  it('should handle GET_LOCATIONS_SUCCESS', () => {
    const result = getLocationsReducer(initialState, {
      type: GET_LOCATIONS_SUCCESS,
      payload: data,
    });
    expect({ ...result, ...successState }).toEqual({
      ...initialState,
      ...successState,
    });
  });

  it('should handle GET_LOCATION_ERROR', () => {
    const result = getLocationsReducer(initialState, {
      type: GET_LOCATION_ERROR,
      payload: data,
    });
    expect({ ...result, ...errorState }).toEqual({
      ...initialState,
      ...errorState,
    });
  });
});
