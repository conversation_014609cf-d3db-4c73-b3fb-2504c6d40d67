import React from 'react';
import { useTheme } from '@mui/material/styles';

function BorderedSection({ title, children }) {
  const theme = useTheme();

  const styles = {
    mainContainer: {
      display: 'flex',
      flexDirection: 'column',
      maxWidth: '500px',
      borderLeft: `1px solid ${theme.palette.divider}`,
      borderBottom: `1px solid ${theme.palette.divider}`,
      borderRight: `1px solid ${theme.palette.divider}`,
      borderRadius: theme.shape.borderRadius,
      marginTop: theme.spacing(1),
      backgroundColor: theme.palette.background.paper,
    },
    header: {
      display: 'flex',
      flexDirection: 'row',
      width: '100%',
    },
    headerBorderBefore: {
      borderTop: `1px solid ${theme.palette.divider}`,
      width: theme.spacing(1),
      marginRight: '2px',
      borderTopLeftRadius: theme.shape.borderRadius,
    },
    headerTitle: {
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'nowrap',
      alignItems: 'center',
      gap: theme.spacing(1),
      width: 'fit-content',
      height: theme.spacing(4),
      margin: `-${theme.spacing(2)} ${theme.spacing(0.5)} 0 ${theme.spacing(0.3)}`,
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      color: '#808080',
      fontSize: 13,
      fontWeight: theme.typography.fontWeightRegular,
    },
    headerBorderAfter: {
      borderTop: `1px solid ${theme.palette.divider}`,
      width: theme.spacing(2),
      flexGrow: 2,
      borderTopRightRadius: theme.shape.borderRadius,
    },
    childrenContainer: {
      padding: `0 ${theme.spacing(2)} ${theme.spacing(2)} ${theme.spacing(2)}`,
    },
  };

  return (
    <div style={styles.mainContainer}>
      <div style={styles.header}>
        <div style={styles.headerBorderBefore}></div>
        {title && <div style={styles.headerTitle}>{title && <span>{title}</span>}</div>}
        <div style={styles.headerBorderAfter}></div>
      </div>
      <div style={styles.childrenContainer}>{children}</div>
    </div>
  );
}

export default BorderedSection;
