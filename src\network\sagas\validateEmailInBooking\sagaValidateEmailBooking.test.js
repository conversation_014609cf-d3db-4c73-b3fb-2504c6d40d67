import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/validateEmailInBooking/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { validateEmailInBooking } from './index';
/**
 * This function test test case for get validateEmailInBooking details saga
 * Fires get validateEmailInBooking success of api gives success
 * Fires get validateEmailInBooking error of api fails
 */

describe('validateEmailInBooking', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiValidateEmailInBooking = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.validateEmailInBooking(DUMMY_ITEM.data);
    await runSaga(fakeStore, validateEmailInBooking, requestResult).done;

    let successResult = actions.validateActionEmailInBookingSuccess(DUMMY_ITEM.data);

    expect(api.makeApiValidateEmailInBooking.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiValidateEmailInBooking = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.validateEmailInBooking(DUMMY_ITEM.data);
    await runSaga(fakeStore, validateEmailInBooking, requestResult).done;

    expect(api.makeApiValidateEmailInBooking.mock.calls.length).toBe(1);

    let errorResult = actions.validateActionEmailInBookingError(DUMMY_ITEM.data);
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiValidateEmailInBooking = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.validateEmailInBooking(DUMMY_ITEM.data);
    await runSaga(fakeStore, validateEmailInBooking, requestResult).done;

    expect(api.makeApiValidateEmailInBooking.mock.calls.length).toBe(1);

    let resetResult = actions.resetValidateEmail();
    const expectedAction = {
      type: actions.VALIDATE_EMAIL_IN_BOOKING_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
