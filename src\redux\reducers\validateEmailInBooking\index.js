import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  VALIDATE_EMAIL_IN_BOOKING_REQUEST,
  VALIDATE_EMAIL_IN_BOOKING_SUCCESS,
  VALIDATE_EMAIL_IN_BOOKING_ERROR,
  VALIDATE_EMAIL_IN_BOOKING_RESET,
} from '../../actions/validateEmailInBooking';
export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};
export const validateEmailInBookingReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case VALIDATE_EMAIL_IN_BOOKING_REQUEST:
      return {
        ...state,
        isValidateEmailInBookingFetching: true,
        validateEmailInBookingSuccess: false,
        validateEmailInBookingError: false,
        validateEmailInBookingErrorData: null,
      };
    case VALIDATE_EMAIL_IN_BOOKING_SUCCESS: {
      return {
        ...state,
        isValidateEmailInBookingFetching: false,
        validateEmailInBookingSuccess: true,
        validateEmailInBookingError: false,
        validateEmailInBookingErrorData: null,
        validateEmailInBookingSuccessData: payload,
      };
    }
    case VALIDATE_EMAIL_IN_BOOKING_ERROR:
      return {
        ...state,
        isValidateEmailInBookingFetching: false,
        validateEmailInBookingSuccess: false,
        validateEmailInBookingError: true,
        validateEmailInBookingErrorData: payload,
      };
    case VALIDATE_EMAIL_IN_BOOKING_RESET:
      return {
        ...state,
        isValidateEmailInBookingFetching: false,
        validateEmailInBookingSuccess: false,
        validateEmailInBookingError: false,
        validateEmailInBookingErrorData: null,
      };
    default:
      return state;
  }
};
