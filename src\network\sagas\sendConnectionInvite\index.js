/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import {
  SEND_CONNECTION_INVITE_REQUEST,
  SEND_CONNECTION_INVITE_SUCCESS,
  SEND_CONNECTION_INVITE_ERROR,
} from '../../../redux/actions/sendConnectionInvite';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiSendConnectionInvite } from './api';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function* fetchSendConnectionInvite(action) {
  try {
    const response = yield call(makeApiSendConnectionInvite, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: SEND_CONNECTION_INVITE_SUCCESS, payload: data });
    } else {
      yield put({ type: SEND_CONNECTION_INVITE_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: SEND_CONNECTION_INVITE_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchSendConnectionInvite() {
  yield takeEvery(SEND_CONNECTION_INVITE_REQUEST, fetchSendConnectionInvite);
}
