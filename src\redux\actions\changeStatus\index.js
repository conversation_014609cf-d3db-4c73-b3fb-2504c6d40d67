/*
 * CHANGE_STATUS action types
 */

export const CHANGE_STATUS_REQUEST = 'CHANGE_STATUS_REQUEST';
export const CHANGE_STATUS_SUCCESS = 'CHANGE_STATUS_SUCCESS';
export const CHANGE_STATUS_ERROR = 'CHANGE_STATUS_ERROR';
export const CHANGE_STATUS_RESET = 'CHANGE_STATUS_RESET';

/*
 * action creators
 */
export function changeStatus(data) {
  return {
    type: CHANGE_STATUS_REQUEST,
    payload: data,
  };
}

export function changeStatusSuccess(data) {
  return {
    type: CHANGE_STATUS_SUCCESS,
    payload: data,
  };
}

export function changeStatusError() {
  return {
    type: CHANGE_STATUS_ERROR,
  };
}

export const resetChangeStatus = () => {
  return {
    type: CHANGE_STATUS_RESET,
  };
};
