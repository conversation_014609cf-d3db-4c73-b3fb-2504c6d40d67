export const SAVE_WIDGET_AUTH_REQUEST = 'SAVE_WIDGET_AUTH_REQUEST';
export const SAVE_WIDGET_AUTH_SUCCESS = 'SAVE_WIDGET_AUTH_SUCCESS';
export const SAVE_WIDGET_AUTH_ERROR = 'SAVE_WIDGET_AUTH_ERROR';
export const SAVE_WIDGET_AUTH_RESET = 'SAVE_WIDGET_AUTH_RESET';
/*
 * action creators
 */

export function saveWidgetAuth(success, data) {
  console.log('save auth', data);
  return {
    type: success ? SAVE_WIDGET_AUTH_SUCCESS : SAVE_WIDGET_AUTH_ERROR,
    payload: data,
  };
}

export function saveWidgetAuthReset() {
  console.log('reset auth');
  return {
    type: SAVE_WIDGET_AUTH_RESET,
  };
}
