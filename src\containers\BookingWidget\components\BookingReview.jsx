import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import useNotification from '../../../hooks/useNotification';
import { Stack, Button, Typography, Paper, Grid } from '@mui/material';
import { rescheduleAppointmentDetails, resetRescheduleAppointment } from '../../../redux/actions/rescheduleAppointment';
import { completeBooking, resetBooking } from '../../../redux/actions/completeBooking';
import { HeaderSummary } from '../HeaderSummary';
import { Loader } from '../../../components';
import * as CommonUtility from '../../commonUtility';
import { useTranslation } from 'react-i18next';
import { useQueryClient } from '@tanstack/react-query';
import { APPOINTMENT_QUERY_KEY } from '@/containers/commonConstants';

export const BookingReview = (props) => {
  const {
    headerSummary,
    handleEditNavigationCallback,
    callSummaryAfterBookingReviewCallback,
    callSummaryAfterRescheduleReviewCallback,
    appointmentPayload,
    appointmentId,
    isRescheduleAppointment,
    demographic,
    locationId,
  } = props;

  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const [msg, sendNotification] = useNotification();
  const [isFreshRecordFetched, setIsFreshRecordFetched] = useState(false);
  const { t } = useTranslation();
  const [organizationId] = CommonUtility.getOrganizationAndWidgetId();

  const {
    completeBookingFetching,
    completeBookingError,
    completeBookingErrorData,
    completeBookingSuccess,
    completeBookingSuccessData,
  } = useSelector((state) => state.completeBookingReducer);

  const {
    rescheduleAppointmentFetching,
    rescheduleAppointmentSuccess,
    rescheduleAppointmentError,
    rescheduleAppointmentData,
    rescheduleAppointmentSuccessData,
  } = useSelector((state) => state.rescheduleAppointmentReducer);

  const rescheduleAppointment = () => {
    queryClient.removeQueries({ queryKey: APPOINTMENT_QUERY_KEY });
    dispatch(
      rescheduleAppointmentDetails({
        appointmentId: appointmentId,
        rescheduleData: appointmentPayload,
        locationId,
        organizationId,
      }),
    );
  };

  useEffect(() => {
    if (isFreshRecordFetched && rescheduleAppointmentSuccess && rescheduleAppointmentSuccessData) {
      const { id: appointmentId } = rescheduleAppointmentSuccessData || [];
      //go back to booking widget and call summary page with reschedule message
      callSummaryAfterRescheduleReviewCallback(appointmentId);
      dispatch(resetRescheduleAppointment());
    }
  }, [rescheduleAppointmentSuccess, rescheduleAppointmentSuccessData]);

  useEffect(() => {
    if (isFreshRecordFetched && rescheduleAppointmentError && rescheduleAppointmentData) {
      sendNotification({ msg: t('apiError'), variant: 'error' });
    }
  }, [rescheduleAppointmentError, rescheduleAppointmentData]);

  useEffect(() => {
    if (rescheduleAppointmentFetching) {
      setIsFreshRecordFetched(true);
    }
  }, [rescheduleAppointmentFetching]);

  // create new booking request
  const completeBookAppointment = () => {
    dispatch(
      completeBooking({
        bookingData: appointmentPayload,
        organizationId,
        locationId,
      }),
    );
  };

  useEffect(() => {
    if (isFreshRecordFetched && completeBookingSuccess && completeBookingSuccessData) {
      const appointmentId = completeBookingSuccessData.id;

      //go back to booking widget and call summary page with booking message
      callSummaryAfterBookingReviewCallback(appointmentId);
      dispatch(resetBooking());
    } else if (isFreshRecordFetched && completeBookingError && completeBookingErrorData) {
      sendNotification({ msg: t('apiError'), variant: 'error' });
    }
  }, [completeBookingSuccess, completeBookingSuccessData, completeBookingError, completeBookingErrorData]);

  useEffect(() => {
    if (completeBookingFetching) {
      setIsFreshRecordFetched(true);
    }
  }, [completeBookingFetching]);

  const getBookingOrRescheduleButton = () => {
    if (isRescheduleAppointment) {
      return (
        <Button variant="contained" onClick={rescheduleAppointment}>
          {t('rescheduleNow')}
        </Button>
      );
    } else {
      return (
        <Button variant="contained" onClick={completeBookAppointment}>
          {t('bookNow')}
        </Button>
      );
    }
  };

  return (
    <>
      <Loader active={completeBookingFetching || rescheduleAppointmentFetching} />
      <Grid sx={{ mt: 2, mx: '4%' }}>
        <Typography variant="h5">{t('reviewDetails')} </Typography>
        <Paper sx={{ px: 2, my: 2 }}>
          <HeaderSummary
            headerSummary={headerSummary}
            handleNavigationCallback={(action, editType, demographic, isRescheduleAppointmentFlag) =>
              handleEditNavigationCallback(action, editType, demographic, isRescheduleAppointmentFlag)
            }
            demographic={demographic}
            isRescheduleAppointment={isRescheduleAppointment}
          />
        </Paper>
        <Stack direction="column" alignItems="center" sx={{ pt: 0, px: 2, pb: 2, mt: 3 }}>
          {getBookingOrRescheduleButton()}
        </Stack>
      </Grid>
    </>
  );
};
