import React from 'react';
import { Paper, Grid, Box, Stack, Typography } from '@mui/material';

export const Description = (props) => {
  console.log('TRACE: Description');
  const { heading, description } = props;

  return (
    <Box
      sx={{
        px: '4%',
        pt: '8px',
        pb: '48px',
      }}
    >
      <Box sx={{ pt: 3, height: '100%' }}>
        <Grid container>
          <Grid item xs={12} sx={{ mb: 2 }}>
            <Typography variant="h5"> {heading} </Typography>
          </Grid>
          <Grid item xs={12} justifyContent="center" alignItems="center">
            <Paper sx={{ minHeight: '300px', p: 2 }}>
              <Stack>
                <Typography variant="body" dangerouslySetInnerHTML={{ __html: description }}></Typography>
              </Stack>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};
