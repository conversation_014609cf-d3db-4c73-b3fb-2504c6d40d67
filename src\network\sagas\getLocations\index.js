/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { GET_LOCATION_ERROR, GET_LOCATION_SUCCESS, GET_LOCATION_REQUEST } from '../../../redux/actions/getLocations';

import { makeApiFetchLocations } from './api';
/**
 *
 * @param {*} action
 */

export function* fetchLocations(action) {
  try {
    const response = yield call(makeApiFetchLocations, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: GET_LOCATION_SUCCESS, payload: data });
    } else {
      yield put({ type: GET_LOCATION_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: GET_LOCATION_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchLocations() {
  yield takeEvery(GET_LOCATION_REQUEST, fetchLocations);
}
