import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/validateEmail/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { validateEmail } from './index';
/**
 * This function test test case for get validateEmail details saga
 * Fires get validateEmail success of api gives success
 * Fires get validateEmail error of api fails
 */

describe('validateEmail', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiValidateEmail = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.validateEmail(DUMMY_ITEM.data);
    await runSaga(fakeStore, validateEmail, requestResult).done;

    let successResult = actions.validateRegistraionEmailSuccess(DUMMY_ITEM.data);

    expect(api.makeApiValidateEmail.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiValidateEmail = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.validateEmail(DUMMY_ITEM.data);
    await runSaga(fakeStore, validateEmail, requestResult).done;

    expect(api.makeApiValidateEmail.mock.calls.length).toBe(1);

    let errorResult = actions.validateRegistraionEmailError(DUMMY_ITEM.data);
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiValidateEmail = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.validateEmail(DUMMY_ITEM.data);
    await runSaga(fakeStore, validateEmail, requestResult).done;

    expect(api.makeApiValidateEmail.mock.calls.length).toBe(1);

    let resetResult = actions.resetValidateEmail();
    const expectedAction = {
      type: actions.VALIDATE_EMAIL_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
