export const SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_REQUEST = 'SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_REQUEST';
export const SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_SUCCESS = 'SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_SUCCESS';
export const SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_ERROR = 'SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_ERROR';
export const SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_RESET = 'SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_RESET';

export function saveQuestionnaireResponseOrganization(data) {
  return {
    type: SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_REQUEST,
    payload: data,
  };
}

export function saveQuestionnaireResponseOrganizationSuccess(data) {
  return {
    type: SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_SUCCESS,
    payload: data,
  };
}

export function saveQuestionnaireResponseOrganizationError() {
  return {
    type: SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_ERROR,
  };
}

export function resetSaveQuestionnaireResponseOrganization() {
  return {
    type: SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_RESET,
  };
}
