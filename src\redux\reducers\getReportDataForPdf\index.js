import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  GET_REPORT_DATA_FOR_PDF_REQUEST,
  GET_REPORT_DATA_FOR_PDF_SUCCESS,
  GET_REPORT_DATA_FOR_PDF_ERROR,
  GET_REPORT_DATA_FOR_PDF_RESET,
} from '../../actions/getReportDataForPdf';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const getReportDataForPdfReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case GET_REPORT_DATA_FOR_PDF_REQUEST:
      return {
        ...state,
        getReportDataForPdfFetching: true,
        getReportDataForPdfSuccess: false,
        getReportDataForPdfError: false,
        getReportDataForPdfErrorData: null,
      };
    case GET_REPORT_DATA_FOR_PDF_SUCCESS: {
      return {
        ...state,
        getReportDataForPdfFetching: false,
        getReportDataForPdfSuccess: true,
        getReportDataForPdfError: false,
        getReportDataForPdfErrorData: null,
        getReportDataForPdfSuccessData: payload,
      };
    }
    case GET_REPORT_DATA_FOR_PDF_ERROR:
      return {
        ...state,
        getReportDataForPdfFetching: false,
        getReportDataForPdfSuccess: false,
        getReportDataForPdfError: true,
        getReportDataForPdfErrorData: payload,
      };
    case GET_REPORT_DATA_FOR_PDF_RESET:
      return {
        ...state,
        getReportDataForPdfFetching: false,
        getReportDataForPdfSuccess: false,
        getReportDataForPdfError: false,
        getReportDataForPdfErrorData: null,
        getReportDataForPdfSuccessData: null,
      };
    default:
      return state;
  }
};
