import {
  ACTIVATE_PATIENT_REQUEST,
  ACTIVATE_PATIENT_SUCCESS,
  ACTIVATE_PATIENT_ERROR,
  ACTIVATE_PATIENT_RESET,
} from '../../actions/activatePatient';

import * as G<PERSON><PERSON>BA<PERSON> from '../globals';
import { activatePatientReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isActivatePatientFetching: true,
  activatePatientSuccess: false,
  activatePatientError: false,
  activatePatientErrorData: null,
};

const successState = {
  isActivatePatientFetching: false,
  activatePatientSuccess: true,
  activatePatientError: false,
  activatePatientErrorData: null,
  activatePatientSuccessData: data,
};

const errorState = {
  isActivatePatientFetching: false,
  activatePatientSuccess: false,
  activatePatientError: true,
  activatePatientErrorData: data,
};

const resetState = {
  isActivatePatientFetching: false,
  activatePatientSuccess: false,
  activatePatientError: false,
  activatePatientErrorData: null,
};

describe('Reschedule Reducer', () => {
  it('should return the initial state', () => {
    expect(activatePatientReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle ACTIVATE_PATIENT_REQUEST', () => {
    expect(
      activatePatientReducer(initialState, {
        type: ACTIVATE_PATIENT_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle ACTIVATE_PATIENT_SUCCESS', () => {
    expect(
      activatePatientReducer(initialState, {
        type: ACTIVATE_PATIENT_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle ACTIVATE_PATIENT_ERROR', () => {
    expect(
      activatePatientReducer(initialState, {
        type: ACTIVATE_PATIENT_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle ACTIVATE_PATIENT_RESET', () => {
    expect(
      activatePatientReducer(initialState, {
        type: ACTIVATE_PATIENT_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
