/*
 * ASSIGN_QUESTIONNAIRE action types
 */

export const ASSIGN_QUESTIONNAIRE_REQUEST = 'ASSIGN_QUESTIONNAIRE_REQUEST';
export const ASSIGN_QUESTIONNAIRE_SUCCESS = 'ASSIGN_QUESTIONNAIRE_SUCCESS';
export const ASSIGN_QUESTIONNAIRE_ERROR = 'ASSIGN_QUESTIONNAIRE_ERROR';
export const ASSIGN_QUESTIONNAIRE_RESET = 'ASSIGN_QUESTIONNAIRE_RESET';

/*
 * action creators
 */

export function assignQuestionnaireRequest(data) {
  return {
    type: ASSIGN_QUESTIONNAIRE_REQUEST,
    payload: data,
  };
}

export function assignQuestionnaireSucess(data) {
  return {
    type: ASSIGN_QUESTIONNAIRE_SUCCESS,
    payload: data,
  };
}

export function assignQuestionnaireError() {
  return {
    type: ASSIGN_QUESTIONNAIRE_ERROR,
  };
}

export function assignQuestionnaireReset() {
  return {
    type: ASSIGN_QUESTIONNAIRE_RESET,
  };
}
