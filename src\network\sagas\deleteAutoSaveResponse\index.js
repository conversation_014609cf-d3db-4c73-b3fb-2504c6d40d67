/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  DELETE_AUTO_SAVE_RESPONSE_REQUEST,
  DELETE_AUTO_SAVE_RESPONSE_SUCCESS,
  DELETE_AUTO_SAVE_RESPONSE_ERROR,
} from '../../../redux/actions/deleteAutoSaveResponse';
import { makeApiFetchDeleteAutoSaveResponse } from './api';

/**
 *
 * @param {*} action
 */

export function* fetchDeleteAutoSaveResponse(action) {
  try {
    const response = yield call(makeApiFetchDeleteAutoSaveResponse, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: DELETE_AUTO_SAVE_RESPONSE_SUCCESS, payload: data });
    } else {
      yield put({ type: DELETE_AUTO_SAVE_RESPONSE_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: DELETE_AUTO_SAVE_RESPONSE_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchDeleteAutoSaveResponse() {
  yield takeEvery(DELETE_AUTO_SAVE_RESPONSE_REQUEST, fetchDeleteAutoSaveResponse);
}
