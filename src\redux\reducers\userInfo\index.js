/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import { USER_INFO_RESET, USER_INFO_ERROR, USER_INFO_SUCCESS, USER_INFO_REQUEST } from '../../actions/userInfo';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const userInfoReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case USER_INFO_REQUEST:
      return {
        ...state,
        isUserInfoFetching: true,
        fetchUserInfoSuccess: false,
        fetchUserInfoError: false,
        userInfoErrorData: null,
      };
    case USER_INFO_SUCCESS: {
      return {
        ...state,
        isUserInfoFetching: false,
        fetchUserInfoSuccess: true,
        fetchUserInfoError: false,
        userInfoErrorData: null,
        userInfoSuccessData: payload,
      };
    }
    case USER_INFO_ERROR:
      return {
        ...state,
        isUserInfoFetching: false,
        fetchUserInfoSuccess: false,
        fetchUserInfoError: true,
        userInfoErrorData: payload,
      };
    case USER_INFO_RESET:
      return {
        ...state,
        isUserInfoFetching: false,
        fetchUserInfoSuccess: false,
        fetchUserInfoError: false,
        userInfoSuccessData: null,
      };
    default:
      return state;
  }
};
