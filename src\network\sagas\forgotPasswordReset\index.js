/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  FORGOT_PASSWORD_REQUEST,
  FORGOT_PASSWORD_SUCCESS,
  FORGOT_PASSWORD_ERROR,
} from '../../../redux/actions/forgotPassword';
import { makeApiForgotPassword } from './api';

/**
 *
 * @param {*} action
 */

export function* fetchForgotPassword(action) {
  try {
    const response = yield call(makeApiForgotPassword, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: FORGOT_PASSWORD_SUCCESS, payload: data });
    } else {
      yield put({ type: FORGOT_PASSWORD_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: FORGOT_PASSWORD_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchForgotPassword() {
  yield takeEvery(FORGOT_PASSWORD_REQUEST, fetchForgotPassword);
}
