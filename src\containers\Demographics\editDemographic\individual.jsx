import React, { useEffect, useState } from 'react';
import {
  FormControl,
  TextField,
  Grid,
  Stack,
  Select,
  InputLabel,
  MenuItem,
  FormHelperText,
  Typography,
  Box,
} from '@mui/material';
import ClearIcon from '@mui/icons-material/Clear';
import { CambianDatePicker } from '@/components';
import { sortFields } from '../demographicUtility';
import dataValidation from '../../../utils/dataValidation/dataValidation';
import { individualFieldValidation } from '../../../utils/helpers/validation';
import { parseISO } from 'date-fns';
import { convertInDateFormat } from '../../../utils/helpers/date-time';
import { HealthcareIdFields } from './HealthcareIdFields';
import { useTranslation } from 'react-i18next';

const genderOptions = [
  { displayName: 'Male', value: 'male' },
  { displayName: 'Female', value: 'female' },
  { displayName: 'Unknown', value: 'unknown' },
];

export const Individual = ({
  index,
  demographic,
  demographicFields,
  individual,
  handleRemoveIndividual,
  handleIndividualChangeCallback,
  fields,
  isClientSummary,
  validationData,
  setTriggerUseEffect,
  setShowValidationErrors,
}) => {
  const { t } = useTranslation();

  const [gender, setGender] = useState(demographicFields ? individual?.gender?.toLowerCase() : '');
  const [date, setDate] = useState(demographicFields && individual?.dateOfBirth ? individual?.dateOfBirth : null);
  const [healthcareIds, setHealthcareIds] = useState(
    individual?.healthCareIds[0]?.value ? individual?.healthCareIds : [],
  );

  useEffect(() => {
    setGender(individual?.gender?.toLowerCase());
    setDate(individual?.dateOfBirth || null);
    setHealthcareIds(individual?.healthCareIds[0]?.value ? individual?.healthCareIds : []);
  }, [individual]);

  const handleIndividualChange = (value, fieldName, isRequired) => {
    if (fieldName === 'gender') {
      setGender(value);
    }
    if (fieldName === 'dateOfBirth') {
      setDate(value);
    }
    if (fieldName === 'healthCareIds') {
      demographicFields.individuals[index].healthCareIds = [...value];
    } else {
      individual[fieldName] = value;
      demographicFields.individuals[index][fieldName] = value;
    }
    handleIndividualChangeCallback(
      individual,
      index,
      individualFieldValidation(value, fieldName, index, isRequired, individual),
    );
  };

  const getFirstName = (item) => (
    <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 2, pl: { xs: 1, sm: 3 }, pr: 1 }}>
      <FormControl fullWidth variant="outlined">
        <TextField
          value={individual?.firstName}
          onChange={(e) => handleIndividualChange(e.target.value, 'firstName', item.isMandatory)}
          label={item.display}
          name="firstName"
          size="small"
          required={item.isMandatory}
          inputProps={{
            readOnly: isClientSummary,
          }}
          error={validationData?.individuals ? !!validationData?.individuals[index]?.firstName : false}
          helperText={validationData?.individuals ? validationData?.individuals[index]?.firstName : ''}
          autoComplete="off"
        />
      </FormControl>
    </Grid>
  );

  const getLastName = (item) => (
    <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 2, pl: { xs: 1, sm: 3 }, pr: 1 }}>
      <FormControl fullWidth variant="outlined">
        <TextField
          value={individual?.lastName}
          onChange={(e) => handleIndividualChange(e.target.value, 'lastName', item.isMandatory)}
          label={item.display}
          name="lastName"
          size="small"
          required={item.isMandatory}
          inputProps={{
            readOnly: isClientSummary,
          }}
          error={validationData?.individuals ? !!validationData?.individuals[index]?.lastName : false}
          helperText={validationData?.individuals ? validationData?.individuals[index]?.lastName : ''}
          autoComplete="off"
        />
      </FormControl>
    </Grid>
  );

  const getMiddleName = (item) => (
    <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 2, pl: { xs: 1, sm: 3 }, pr: 1 }}>
      <FormControl fullWidth variant="outlined">
        <TextField
          value={individual?.middleName}
          onChange={(e) => handleIndividualChange(e.target.value, 'middleName', item.isMandatory)}
          label={item.display}
          name="middleName"
          size="small"
          required={item.isMandatory}
          inputProps={{
            readOnly: isClientSummary,
          }}
          error={validationData?.individuals ? !!validationData?.individuals[index]?.middleName : false}
          helperText={validationData?.individuals ? validationData?.individuals[index]?.middleName : ''}
          autoComplete="off"
        />
      </FormControl>
    </Grid>
  );

  const getGender = (item) => (
    <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 2, pl: { xs: 1, sm: 3 }, pr: 1 }}>
      <FormControl fullWidth variant="outlined">
        <InputLabel
          size="small"
          error={validationData?.individuals ? !!validationData?.individuals[index]?.gender : false}
          required={item.isMandatory}
        >
          {item.display}
        </InputLabel>
        <Select
          value={gender}
          label={item.display}
          name="gender"
          size="small"
          onChange={(e) => handleIndividualChange(e.target.value, 'gender', item.isMandatory)}
          inputProps={{ readOnly: isClientSummary }}
          error={validationData?.individuals ? !!validationData?.individuals[index]?.gender : false}
          required={item.isMandatory}
        >
          {genderOptions.map((gender, index) => (
            <MenuItem key={index} value={gender.value}>
              {gender.displayName}
            </MenuItem>
          ))}
        </Select>
        <FormHelperText error>
          {validationData?.individuals ? validationData?.individuals[index]?.gender : ''}
        </FormHelperText>
      </FormControl>
    </Grid>
  );

  const getDateOfBirth = (item) => (
    <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 2, pl: { xs: 1, sm: 3 }, pr: 1 }}>
      <FormControl fullWidth variant="outlined">
        <CambianDatePicker
          label={item.display}
          required={item.isMandatory}
          name="dateOfBirth"
          size="small"
          value={date === null ? date : parseISO(date)}
          isReadOnly={isClientSummary}
          format="yyyy-MM-dd"
          inputFormat="yyyy-MM-dd"
          mask="____-__-__"
          disableFuture={true}
          fullWidth={true}
          onChange={(newValue) =>
            handleIndividualChange(convertInDateFormat(newValue), 'dateOfBirth', item.isMandatory)
          }
          convertDateFormat={convertInDateFormat}
          error={validationData?.individuals && !!validationData?.individuals[index]?.dateOfBirth}
          requiredFieldText={t('requiredField')}
          invalidFieldText={t('dateValid')}
        />
        <FormHelperText error>
          {validationData?.individuals ? validationData?.individuals[index]?.dateOfBirth : ''}
        </FormHelperText>
      </FormControl>
    </Grid>
  );

  const getHealthcareId = (item) => (
    <HealthcareIdFields
      demographicFields={demographicFields}
      item={item}
      handleIndividualChange={handleIndividualChange}
      healthcareIds={healthcareIds}
      setHealthcareIds={setHealthcareIds}
      validationData={validationData}
      isClientSummary={isClientSummary}
      index={index}
      t={t}
      setShowValidationErrors={setShowValidationErrors}
    />
  );

  return (
    <Stack direction="column">
      <Grid container alignItems="center" sx={{ py: 1, marginTop: 1 }}>
        {
          <Grid item xs={6} sm={4} md={4} lg={4} sx={{ pl: 1, marginLeft: 2 }}>
            <Typography variant="h6">
              {t('individual')}{' '}
              {' - ' +
                demographicFields?.individuals[index]?.firstName +
                ' ' +
                demographicFields?.individuals[index]?.middleName +
                ' ' +
                demographicFields?.individuals[index]?.lastName}
            </Typography>
          </Grid>
        }
        {demographicFields?.individuals.length > 1 && (
          <Grid item xs={5.5} sm={5.8} md={1.8} lg={0.9} sx={{ textAlign: 'right', p: 1 }}>
            <ClearIcon sx={{ cursor: 'pointer' }} onClick={() => handleRemoveIndividual(index)} />
          </Grid>
        )}
      </Grid>
      <Box>
        {!dataValidation.isDataEmpty(fields) &&
          fields.sort(sortFields).map((item, index) => {
            return (
              <Grid container key={index}>
                {item.code === 'FIRST_NAME' && getFirstName(item)}
                {item.code === 'LAST_NAME' && getLastName(item)}
                {item.code === 'MIDDLE_NAME' && getMiddleName(item)}
                {item.code === 'DATE_OF_BIRTH' && getDateOfBirth(item)}
                {item.code === 'GENDER' && getGender(item)}
                {item.code === 'IDENTIFICATION' && getHealthcareId(item)}
              </Grid>
            );
          })}
      </Box>
    </Stack>
  );
};
