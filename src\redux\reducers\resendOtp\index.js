import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import { RESEND_OTP_REQUEST, RESEND_OTP_SUCCESS, RESEND_OTP_ERROR, RESEND_OTP_RESET } from '../../actions/resendOtp';
export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};
export const resendOtpReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case RESEND_OTP_REQUEST:
      return {
        ...state,
        isResendOtpFetching: true,
        resendOtpSuccess: false,
        resendOtpError: false,
        resendOtpErrorData: null,
      };
    case RESEND_OTP_SUCCESS: {
      return {
        ...state,
        isResendOtpFetching: false,
        resendOtpSuccess: true,
        resendOtpError: false,
        resendOtpErrorData: null,
        resendOtpSuccessData: payload,
      };
    }
    case RESEND_OTP_ERROR:
      return {
        ...state,
        isResendOtpFetching: false,
        resendOtpSuccess: false,
        resendOtpError: true,
        resendOtpErrorData: payload,
      };
    case RESEND_OTP_RESET:
      return {
        ...state,
        isResendOtpFetching: false,
        resendOtpSuccess: false,
        resendOtpError: false,
        resendOtpErrorData: null,
      };
    default:
      return state;
  }
};
