/*
 *
 */

import { makeNetworkCall } from '../..';
import { BASE_URL, SUBMIT_ANSWERS } from '../../../utils/constants/apiEndpoints';
import { PATIENT_ID } from '../../../utils/constants';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiFetchSubmitAnswers(action) {
  const { payload = {} } = action || {};
  const { headers = {}, patientId, registrationData } = payload || {};
  const mainUrl = BASE_URL + SUBMIT_ANSWERS.replace(PATIENT_ID, patientId);
  const config = {
    method: 'POST',
    url: mainUrl,
    headers: headers,
    data: registrationData,
  };
  return makeNetworkCall(config);
}
