import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/widgetDetails/index';
import { API_RESPONSE_SUCCESS, API_RESPONSE_400_ERROR } from '../../../utils/constants/apiCodes';

import { fetchWidgetDetails } from './index';
/**
 * This function test test case for get widgetDetails details saga
 * Fires get widgetDetails success of api gives success
 * Fires get widgetDetails error of api fails
 */

describe('widgetDetails', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiFetchWidgetDetails = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchWidgetDetails(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchWidgetDetails, requestResult).done;

    let successResult = actions.fetchWidgetDetailsSuccess(DUMMY_ITEM.data);

    expect(api.makeApiFetchWidgetDetails.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_400_ERROR,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchWidgetDetails = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchWidgetDetails(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchWidgetDetails, requestResult).done;

    expect(api.makeApiFetchWidgetDetails.mock.calls.length).toBe(1);

    let errorResult = actions.fetchWidgetDetailsError(DUMMY_ITEM.data);
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  // resetWidgetDetails

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchWidgetDetails = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchWidgetDetails(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchWidgetDetails, requestResult).done;

    expect(api.makeApiFetchWidgetDetails.mock.calls.length).toBe(1);

    let resetResult = actions.resetWidgetDetails();
    const expectedAction = {
      type: actions.WIDGET_DETAILS_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
