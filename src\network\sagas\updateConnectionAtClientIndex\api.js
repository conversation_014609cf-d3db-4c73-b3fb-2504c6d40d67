import { CHECK_EXISTING_CLIENT_INDEX } from '@/utils/constants/awsApiEndpoints';
import { makeNetworkCall } from '../..';
import { INDIVIDUAL_ID, ORGANIZATION_ID, REGISTRY } from '@/utils/constants';

export function updateConnectionAtClientIndex(action) {
  const { payload = {} } = action || {};
  const { headers = {}, organizationId, connectionData, individualId } = payload || {};
  const URL = CHECK_EXISTING_CLIENT_INDEX.replace(ORGANIZATION_ID, organizationId).replace(INDIVIDUAL_ID, individualId);

  const config = {
    method: 'PUT',
    url: URL,
    headers: headers,
    data: connectionData,
  };

  return makeNetworkCall(config);
}
