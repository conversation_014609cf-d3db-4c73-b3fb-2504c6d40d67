import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  POST_QUESTIONNAIRE_QUESTIONS_ERROR,
  POST_QUESTIONNAIRE_QUESTIONS_SUCCESS,
  POST_QUESTIONNAIRE_QUESTIONS_REQUEST,
} from '../../actions/SubmitQuestionnaire';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const submitQuestionnaireAnswersReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case POST_QUESTIONNAIRE_QUESTIONS_REQUEST:
      return {
        ...state,
        isQuestionnaireSubmitAnswersFetching: true,
        isQuestionnaireSubmitAnswersSuccess: false,
        isQuestionnaireSubmitAnswersError: false,
        questionnaireSubmitAnswersErrorData: null,
      };
    case POST_QUESTIONNAIRE_QUESTIONS_SUCCESS: {
      return {
        ...state,
        isQuestionnaireSubmitAnswersFetching: false,
        isQuestionnaireSubmitAnswersSuccess: true,
        isQuestionnaireSubmitAnswersError: false,
        questionnaireSubmitAnswersErrorData: null,
        questionnaireSubmitAnswersSuccessData: payload,
      };
    }
    case POST_QUESTIONNAIRE_QUESTIONS_ERROR:
      return {
        ...state,
        isQuestionnaireSubmitAnswersFetching: false,
        isQuestionnaireSubmitAnswersSuccess: false,
        isQuestionnaireSubmitAnswersError: true,
        questionnaireSubmitAnswersErrorData: payload,
      };
    default:
      return state;
  }
};
