/*
 * POST Questions action types
 */

export const GENERATE_HTML_REPORT_AS_PDF_REQUEST = 'GENERATE_HTML_REPORT_AS_PDF_REQUEST';
export const GENERATE_HTML_REPORT_AS_PDF_SUCCESS = 'GENERATE_HTML_REPORT_AS_PDF_SUCCESS';
export const GENERATE_HTML_REPORT_AS_PDF_ERROR = 'GENERATE_HTML_REPORT_AS_PDF_ERROR';
export const GENERATE_HTML_REPORT_AS_PDF_RESET = 'GENERATE_HTML_REPORT_AS_PDF_RESET';
/*
 * action creators
 */

export function generateHtmlReportAsPdf(data) {
  return {
    type: GENERATE_HTML_REPORT_AS_PDF_REQUEST,
    payload: data,
  };
}

export function generateHtmlReportAsPdfSuccess(data) {
  return {
    type: GENERATE_HTML_REPORT_AS_PDF_SUCCESS,
    payload: data,
  };
}

export function generateHtmlReportAsPdfError() {
  return {
    type: GENERATE_HTML_REPORT_AS_PDF_ERROR,
  };
}

export function resetGenerateHtmlReportAsPdf() {
  return {
    type: GENERATE_HTML_REPORT_AS_PDF_RESET,
  };
}
