import { ORGANIZATION_ID } from '@/utils/constants';
import { makeNetworkCall } from '../..';
import { GET_NEXT_AVAILABLE_TIME_SLOT } from '@/utils/constants/awsApiEndpoints';

export function makeApiFetchNextAvailableSlot(action) {
  const { payload = {} } = action || {};
  const { headers = {}, reqData, organizationId } = payload || {};

  const mainUrl = GET_NEXT_AVAILABLE_TIME_SLOT.replace(ORGANIZATION_ID, organizationId);

  const config = {
    method: 'POST',
    url: mainUrl,
    headers: headers,
    data: reqData,
  };

  return makeNetworkCall(config);
}
