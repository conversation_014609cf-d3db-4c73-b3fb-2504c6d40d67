import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/getBookedAppointmentDetails/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { getBookedAppointment } from './index';

/**
 * This function test test case for get booked appointment details saga
 * Fires get booked appointment success of api gives success
 * Fires get booked appointment error of api fails
 */

describe('getBookedAppointment', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.getBookedAppointmentApi = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.getBookedAppointmentDetails(DUMMY_ITEM.data);
    await runSaga(fakeStore, getBookedAppointment, requestResult).done;

    let successResult = actions.getBookedAppointmentSuccess(DUMMY_ITEM.data);

    expect(api.getBookedAppointmentApi.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.getBookedAppointmentApi = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.getBookedAppointmentDetails(DUMMY_ITEM.data);
    await runSaga(fakeStore, getBookedAppointment, requestResult).done;

    expect(api.getBookedAppointmentApi.mock.calls.length).toBe(1);

    let errorResult = actions.getBookedAppointmentError();
    expect(dispatchedActions).toContainEqual(errorResult);
  });
});
