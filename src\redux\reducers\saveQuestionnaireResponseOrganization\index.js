import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_REQUEST,
  SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_SUCCESS,
  SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_ERROR,
  SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_RESET,
} from '../../actions/saveQuestionnaireResponseOrganization';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const saveQuestionnaireResponseOrganizationReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;

  switch (type) {
    case SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_REQUEST:
      return {
        ...state,
        isSaveQuestionnaireResponseOrganizationFetching: true,
        isSaveQuestionnaireResponseOrganizationSuccess: false,
        saveQuestionnaireResponseOrganizationSuccessData: false,
        isSaveQuestionnaireResponseOrganizationError: null,
      };
    case SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_SUCCESS: {
      return {
        ...state,
        isSaveQuestionnaireResponseOrganizationFetching: false,
        isSaveQuestionnaireResponseOrganizationSuccess: true,
        saveQuestionnaireResponseOrganizationSuccessData: payload,
        isSaveQuestionnaireResponseOrganizationError: false,
        saveQuestionnaireResponseOrganizationErrorData: null,
      };
    }
    case SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_ERROR:
      return {
        ...state,
        isSaveQuestionnaireResponseOrganizationFetching: false,
        isSaveQuestionnaireResponseOrganizationSuccess: false,
        isSaveQuestionnaireResponseOrganizationError: true,
        saveQuestionnaireResponseOrganizationErrorData: payload,
      };
    case SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_RESET:
      return {
        ...state,
        isSaveQuestionnaireResponseOrganizationFetching: undefined,
        isSaveQuestionnaireResponseOrganizationSuccess: undefined,
        saveQuestionnaireResponseOrganizationSuccessData: undefined,
        isSaveQuestionnaireResponseOrganizationError: undefined,
        saveQuestionnaireResponseOrganizationErrorData: undefined,
      };
    default:
      return state;
  }
};
