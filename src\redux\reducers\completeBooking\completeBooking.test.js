import {
  COMPLETE_BOOKING_ERROR,
  COMPLETE_BOOKING_REQUEST,
  COMPLETE_BOOKING_RESET,
  COMPLETE_BOOKING_SUCCESS,
} from '../../actions/completeBooking/index';
import * as G<PERSON><PERSON><PERSON><PERSON> from '../globals';
import { completeBookingReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  completeBookingFetching: true,
  completeBookingSuccess: false,
  completeBookingError: false,
  completeBookingErrorData: null,
};

const errorState = {
  completeBookingFetching: false,
  completeBookingSuccess: false,
  completeBookingError: true,
  completeBookingErrorData: data,
};

const successState = {
  completeBookingFetching: false,
  completeBookingSuccess: true,
  completeBookingError: false,
  completeBookingErrorData: null,
  completeBookingSuccessData: data,
};

const resetState = {
  completeBookingFetching: false,
  completeBookingSuccess: false,
  completeBookingError: false,
  completeBookingErrorData: null,
  completeBookingSuccessData: null,
};
describe('Complete Booking Details Reducer', () => {
  it('should return the initial state', () => {
    expect(completeBookingReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle COMPLETE_BOOKING_REQUEST', () => {
    expect(
      completeBookingReducer(initialState, {
        type: COMPLETE_BOOKING_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle COMPLETE_BOOKING_SUCCESS', () => {
    let result = completeBookingReducer(initialState, {
      type: COMPLETE_BOOKING_SUCCESS,
      payload: data,
    });
    expect(result).toEqual({ ...initialState, ...successState });
  });

  it('should handle COMPLETE_BOOKING_ERROR', () => {
    expect(
      completeBookingReducer(initialState, {
        type: COMPLETE_BOOKING_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle COMPLETE_BOOKING_RESET', () => {
    expect(
      completeBookingReducer(initialState, {
        type: COMPLETE_BOOKING_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
