import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import { GET_SCORE_REQUEST, GET_SCORE_SUCCESS, GET_SCORE_ERROR, GET_SCORE_RESET } from '../../actions/getScore';
export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};
export const getScoreReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case GET_SCORE_REQUEST:
      return {
        ...state,
        getScoreFetching: true,
        getScoreSuccess: false,
        getScoreError: false,
        getScoreErrorData: null,
      };
    case GET_SCORE_SUCCESS: {
      return {
        ...state,
        getScoreFetching: false,
        getScoreSuccess: true,
        getScoreError: false,
        getScoreErrorData: null,
        getScoreSuccessData: payload,
      };
    }
    case GET_SCORE_ERROR:
      return {
        ...state,
        getScoreFetching: false,
        getScoreSuccess: false,
        getScoreError: true,
        getScoreErrorData: payload,
      };
    case GET_SCORE_RESET:
      return {
        ...state,
        getScoreFetching: false,
        getScoreSuccess: false,
        getScoreError: false,
        getScoreErrorData: null,
      };
    default:
      return state;
  }
};
