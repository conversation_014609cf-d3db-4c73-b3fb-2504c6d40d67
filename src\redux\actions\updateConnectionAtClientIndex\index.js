export const UPDATE_CONNECTION_AT_CLIENT_INDEX_REQUEST = 'UPDATE_CONNECTION_AT_CLIENT_INDEX_REQUEST';
export const UPDATE_CONNECTION_AT_CLIENT_INDEX_SUCCESS = 'UPDATE_CONNECTION_AT_CLIENT_INDEX_SUCCESS';
export const UPDATE_CONNECTION_AT_CLIENT_INDEX_ERROR = 'UPDATE_CONNECTION_AT_CLIENT_INDEX_ERROR';
export const UPDATE_CONNECTION_AT_CLIENT_INDEX_RESET = 'UPDATE_CONNECTION_AT_CLIENT_INDEX_RESET';

export function updateConnectionAtClientIndex(data) {
  return {
    type: UPDATE_CONNECTION_AT_CLIENT_INDEX_REQUEST,
    payload: data,
  };
}

export function updateConnectionAtClientIndexSuccess(data) {
  return {
    type: UPDATE_CONNECTION_AT_CLIENT_INDEX_SUCCESS,
    payload: data,
  };
}

export function updateConnectionAtClientIndexError(data) {
  return {
    type: UPDATE_CONNECTION_AT_CLIENT_INDEX_ERROR,
    payload: data,
  };
}

export const resetUpdateConnectionAtClientIndex = () => {
  return {
    type: UPDATE_CONNECTION_AT_CLIENT_INDEX_RESET,
  };
};
