/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  GET_NEXT_AVAILABLE_SLOT_ERROR,
  GET_NEXT_AVAILABLE_SLOT_SUCCESS,
  GET_NEXT_AVAILABLE_SLOT_REQUEST,
} from '../../../redux/actions/getNextAvailableSlot';

import { makeApiFetchNextAvailableSlot } from './api';
/**
 *
 * @param {*} action
 */

export function* fetchNextAvailableSlot(action) {
  try {
    const response = yield call(makeApiFetchNextAvailableSlot, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: GET_NEXT_AVAILABLE_SLOT_SUCCESS, payload: data });
    } else {
      yield put({ type: GET_NEXT_AVAILABLE_SLOT_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: GET_NEXT_AVAILABLE_SLOT_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchNextAvailableSlot() {
  yield takeEvery(GET_NEXT_AVAILABLE_SLOT_REQUEST, fetchNextAvailableSlot);
}
