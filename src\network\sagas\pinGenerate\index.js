/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import { PIN_GENERATE_REQUEST, PIN_GENERATE_SUCCESS, PIN_GENERATE_ERROR } from '../../../redux/actions/pinGenerate';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiFetchPinGenerate } from './api';
/**
 *
 * @param {*} action
 */
/**
 
/**
 *
 * @param {*} action
 */

export function* pinGenerate(action) {
  try {
    const response = yield call(makeApiFetchPinGenerate, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: PIN_GENERATE_SUCCESS, payload: data });
    } else {
      yield put({ type: PIN_GENERATE_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: PIN_GENERATE_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchPinGenerate() {
  yield takeEvery(PIN_GENERATE_REQUEST, pinGenerate);
}
