/*
 *
 */

import { makeNetworkCall } from '../..';
import { BASE_URL, GET_REGISTRATION_QUESTIONS } from '../../../utils/constants/apiEndpoints';
import { PATIENT_ID, SERVICE_ID } from '../../../utils/constants';
/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiFetchRegistrationQuestions(action) {
  const { payload = {} } = action || {};
  const { headers = {}, patientId, serviceId } = payload || {};
  const mainUrl = BASE_URL + GET_REGISTRATION_QUESTIONS.replace(PATIENT_ID, patientId).replace(SERVICE_ID, serviceId);
  const config = {
    method: 'GET',
    url: mainUrl,
    headers: headers,
    formData: false,
  };
  return makeNetworkCall(config);
}
