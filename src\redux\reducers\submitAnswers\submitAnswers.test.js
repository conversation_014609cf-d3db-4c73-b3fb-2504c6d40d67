import {
  POST_REGISTRATION_QUESTIONS_SUCCESS,
  POST_REGISTRATION_QUESTIONS_REQUEST,
  POST_REGISTRATION_QUESTIONS_ERROR,
} from '../../actions/submitAnswers';

import * as G<PERSON><PERSON><PERSON><PERSON> from '../globals';
import { submitAnswersReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isSubmitAnswersFetching: true,
  isSubmitAnswersSuccess: false,
  isSubmitAnswersError: false,
  submitAnswersErrorData: null,
};

const successState = {
  isSubmitAnswersFetching: false,
  isSubmitAnswersSuccess: true,
  isSubmitAnswersError: false,
  submitAnswersErrorData: null,
  submitAnswersSuccessData: data,
};

const errorState = {
  isSubmitAnswersFetching: false,
  isSubmitAnswersSuccess: false,
  isSubmitAnswersError: true,
  submitAnswersErrorData: data,
};

describe('Submit Answers Reducer', () => {
  it('should return the initial state', () => {
    expect(submitAnswersReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle POST_REGISTRATION_QUESTIONS_REQUEST', () => {
    expect(
      submitAnswersReducer(initialState, {
        type: POST_REGISTRATION_QUESTIONS_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle POST_REGISTRATION_QUESTIONS_SUCCESS', () => {
    expect(
      submitAnswersReducer(initialState, {
        type: POST_REGISTRATION_QUESTIONS_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle POST_REGISTRATION_QUESTIONS_ERROR', () => {
    expect(
      submitAnswersReducer(initialState, {
        type: POST_REGISTRATION_QUESTIONS_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });
});
