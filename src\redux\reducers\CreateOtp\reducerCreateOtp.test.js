import { CREATE_OTP_REQUEST, CREATE_OTP_SUCCESS, CREATE_OTP_ERROR, CREATE_OTP_RESET } from '../../actions/CreateOtp';
import * as GLOBALS from '../globals';
import { createOtpReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isCreateOtpFetching: true,
  createOtpSuccess: false,
  createOtpError: false,
  createOtpErrorData: null,
};

const successState = {
  isCreateOtpFetching: false,
  createOtpSuccess: true,
  createOtpError: false,
  createOtpErrorData: null,
  createOtpSuccessData: data,
};

const errorState = {
  isCreateOtpFetching: false,
  createOtpSuccess: false,
  createOtpError: true,
  createOtpErrorData: data,
};

const resetState = {
  isCreateOtpFetching: false,
  createOtpSuccess: false,
  createOtpError: false,
  createOtpErrorData: null,
};

describe('Reschedule Reducer', () => {
  it('should return the initial state', () => {
    expect(createOtpReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle CREATE_OTP_REQUEST', () => {
    expect(
      createOtpReducer(initialState, {
        type: CREATE_OTP_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle CREATE_OTP_SUCCESS', () => {
    expect(
      createOtpReducer(initialState, {
        type: CREATE_OTP_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle CREATE_OTP_ERROR', () => {
    expect(
      createOtpReducer(initialState, {
        type: CREATE_OTP_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle CREATE_OTP_RESET', () => {
    expect(
      createOtpReducer(initialState, {
        type: CREATE_OTP_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
