import { Box, Paper } from '@mui/material';
import { RegisterScreen } from '@/components/RegisterScreen';
import { useSelector } from 'react-redux';
import { useSession } from 'next-auth/react';

export const Register = (props) => {
  const { questionnaireWidgetSuccessData } = props;
  const { data: session } = useSession();
  const { individualUserInfoSuccessData } = useSelector((state) => state.individualUserInfoReducer);

  const { registerPage } = questionnaireWidgetSuccessData;
  const { firstName, lastName, email } = session?.user || {};
  const userDetails = { firstName, lastName, email };

  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
      <Paper sx={{ width: { xs: '100%', sm: '60%', md: '42%', lg: '30%' }, p: 2 }}>
        <RegisterScreen
          heading={registerPage?.heading || ''}
          buttonText={registerPage?.buttonText || ''}
          userDetails={userDetails}
          {...props}
        />
      </Paper>
    </Box>
  );
};
