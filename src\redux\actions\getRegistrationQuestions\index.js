/*
 * Get Questions action types
 */

export const GET_REGISTRATION_QUESTIONS_REQUEST = 'GET_REGISTRATION_QUESTIONS_REQUEST';
export const GET_REGISTRATION_QUESTIONS_SUCCESS = 'GET_REGISTRATION_QUESTIONS_SUCCESS';
export const GET_REGISTRATION_QUESTIONS_ERROR = 'GET_REGISTRATION_QUESTIONS_ERROR';
export const GET_REGISTRATION_QUESTIONS_RESET = 'GET_REGISTRATION_QUESTIONS_RESET';
/*
 * action creators
 */

export function fetchRegistrationQuestions(data) {
  return {
    type: GET_REGISTRATION_QUESTIONS_REQUEST,
    payload: data,
  };
}

export function fetchRegistrationQuestionsSuccess(data) {
  return {
    type: GET_REGISTRATION_QUESTIONS_SUCCESS,
    payload: data,
  };
}

export function fetchRegistrationQuestionsError() {
  return {
    type: GET_REGISTRATION_QUESTIONS_ERROR,
  };
}

export function fetchRegistrationQuestionsReset() {
  return {
    type: GET_REGISTRATION_QUESTIONS_RESET,
  };
}
