/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import { USER_INFO_ERROR, USER_INFO_REQUEST, USER_INFO_SUCCESS } from '../../../redux/actions/userInfo';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiFetchUserInfo } from './api';

/**
 *
 * @param {*} action
 */

export function* fetchUserInfo(action) {
  try {
    const response = yield call(makeApiFetchUserInfo, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: USER_INFO_SUCCESS, payload: data });
    } else {
      yield put({ type: USER_INFO_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: USER_INFO_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchUserInfo() {
  yield takeEvery(USER_INFO_REQUEST, fetchUserInfo);
}
