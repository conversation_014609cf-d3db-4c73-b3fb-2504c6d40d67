/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  GENERATE_HTML_REPORT_AS_PDF_REQUEST,
  GENERATE_HTML_REPORT_AS_PDF_SUCCESS,
  GENERATE_HTML_REPORT_AS_PDF_ERROR,
} from '../../../redux/actions/generateHtmlReportAsPdf';
import { makeApiGenerateHtmlReportAsPdf } from './api';

/**
 *
 * @param {*} action
 */

export function* fetchGenerateHtmlReportAsPdf(action) {
  try {
    const response = yield call(makeApiGenerateHtmlReportAsPdf, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: GENERATE_HTML_REPORT_AS_PDF_SUCCESS, payload: data });
    } else {
      yield put({ type: GENERATE_HTML_REPORT_AS_PDF_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: GENERATE_HTML_REPORT_AS_PDF_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchGenerateHtmlReportAsPdf() {
  yield takeEvery(GENERATE_HTML_REPORT_AS_PDF_REQUEST, fetchGenerateHtmlReportAsPdf);
}
