# Widget V2 

## Running Project
1. Use Node.js 20
2. `yarn install`
3. `yarn dev:{your org or aws account}`

## Env var setup
* Removed .env files in root directory.
* environments/[APP_ENV]/.env.(developmenmt|staging|dev|etc) : Org or Aws account specific env var
* environments/shared/.env.* : Main shared cambian AWS account
* environments/pcn/.env.* : For pcn aws account (Not ready)
* environments/cambian/.env.* : For cambian-org-dev aws account (Not ready)

* environments/.env : shared environment for all env like header test

Secrets still need to be moved out.

package.json has scripts for each org account. For development, `next.config.js` is configured so it reads the environment var from the new env file locations using third party libraries related to dot-env.
For every other environments, `amplify.yml` is configured to copy the .env files from environments to the root diretory of the project so Next.js can pick up then env vars with its built in mechanism. The difference is so there so that amplify does not need to download third party library. Copying over the file in development could cause confusion as the developer will see 2 same env file in our file directory. 

* `yarn dev:shared`: Uses `environments/.env` and `environments/shared/.env.development`. This is the command to use for our main shared account.

#### The caveat is that updating env var will not auto update the env var values in the running Next.js instance. In development, you need to r updating env var will not auto update the env var values in the running Next.js instance. In development, you need to restart the app every time an .env.* files are changed.. 