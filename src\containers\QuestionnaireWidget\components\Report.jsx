import React, { useState, useEffect, useRef } from 'react';
import ReactDOMServer from 'react-dom/server';
import { useSelector } from 'react-redux';
import { Box, IconButton, ListItemIcon, ListItemText, Menu, MenuItem, Tooltip } from '@mui/material';
import { QuestionnaireReportViewerV2, BannerV2 } from '@/components';
import * as QuestionnaireUtility from '@/containers/QuestionnaireWidget/questionnaireUtility';
import * as CommonUtility from '@/containers/commonUtility';
import { Loader } from '@/components';
import useNotification from '@/hooks/useNotification';
import { getDemographicFromUrl } from '@/containers/Demographics/demographicUtility';
import { useAPIs } from '../useAPIs';
import { v4 as uuidv4 } from 'uuid';
import {
  CALL_BACKGROUND_SERVICE,
  OPEN_IN_NEW_TAB,
  REDIRECT_TO_URL,
  REDIRECT_URL,
  open_in_new_tab,
  DOWNLOAD_HTML_REPORT_QUERY_KEY,
  ORGANIZATION_QUERY_KEY,
  REPORT_SETTINGS_QUERY_KEY,
} from '@/containers/commonConstants';
import { useSession } from 'next-auth/react';
import { useReactToPrint } from 'react-to-print';
import cambianLogo from '@/asset/images/cambian_logo_new.png';
import { MoreVert } from '@mui/icons-material';
import Image from 'next/image';
import { useQuery, useQueryClient } from '@tanstack/react-query';

export const Report = (props) => {
  console.log('TRACE: Report');
  const {
    questionnaireWidgetSuccessData,
    handleNavigationCallback,
    questionnaireResponse,
    demographic,
    printRequest,
    isActionPerformed,
    setIsActionPerformed,
    signInModalControl,
  } = props;

  const queryClient = useQueryClient();

  const { data: authSession } = useSession();
  const {
    fetchGenerateHtmlReportAsPdf,
    saveQuestionnaireResponseAtIndividual,
    fetchGeneratePdfReport,
    fetchDownloadHtmlReport,
    fetchOrganizationDetails,
    fetchReportSettings,
  } = useAPIs();

  const reportPageUrl =
    window.location.href + '/printReport' + QuestionnaireUtility.createDemographicUrlParams(demographic);

  const [showResult, setShowResult] = useState(false);
  const [reportDemographics, setReportDemographics] = useState(null);
  const [, sendNotification] = useNotification();
  const [isPrintScreen, setIsPrintScreen] = useState(window.location.href.includes('printReport') ? true : false);
  const [pdfData, setPdfData] = useState(null);
  const [downloadPdfCalled, setDownloadPdfCalled] = useState(false);
  const [saveReportCalled, setSaveReportCalled] = useState(false);
  const [matchedActionCondition, setMatchedActionCondition] = useState();

  const [bannerTemplate, setBannerTemplate] = useState();
  const [organization, setOrganization] = useState();
  const [loading, setLoading] = useState(false);
  //const [htmlString, setHtmlString] = useState('<div>No data Available</>');

  const reportRef = useRef(null);

  const { handleSignInDialogState } = signInModalControl || {};

  useEffect(() => {
    if (window.location.href.includes('printReport')) {
      setIsPrintScreen(true);
    } else {
      setIsPrintScreen(false);
    }
  }, [isPrintScreen, setIsPrintScreen]);

  const { generateHtmlReportAsPdfFetching, generateHtmlReportAsPdfSuccess, generateHtmlReportAsPdfSuccessData } =
    useSelector((state) => state.generateHtmlReportAsPdfReducer);

  const {
    isSaveQuestionnaireResponseIndividualFetching,
    isSaveQuestionnaireResponseIndividualSuccess,
    isSaveQuestionnaireResponseIndividualError,
  } = useSelector((state) => state.saveQuestionnaireResponseIndividualReducer);

  const questionnaireQuestionsSuccessData = useSelector(
    (state) => state.getQuestionnaireQuestionRedux.questionnaireQuestionsSuccessData,
  );

  const { checkExistingClientIndexSuccessData } = useSelector((state) => state.checkExistingClientIndexReducer) || {};
  const { createConnectionAtClientIndexSuccessData } =
    useSelector((state) => state.createConnectionAtClientIndexReducer) || {};

  const { checkExistingConnectionIndexSuccessData } =
    useSelector((state) => state.checkExistingConnectionIndexReducer) || {};
  // Pdf
  const { getPdfFetching, getPdfSuccess, getPdfSuccessData, getPdfError } = useSelector((state) => state.getPdfReducer);

  const { questionnaire } = questionnaireQuestionsSuccessData || {};
  const {
    report = {},
    headingAndDescriptionPage = {},
    action,
    showSignIn,
    widgetTitle,
  } = questionnaireWidgetSuccessData || {};

  const htmlReportActiveExtension = CommonUtility.extractExtension(questionnaire?.extension, 'htmltemplate-base64');
  const htmlReportActive = htmlReportActiveExtension?.valueString || false;

  let demographicSection, demographicFields;

  if (!htmlReportActive) {
    demographicSection = QuestionnaireUtility.extractResultPageSection(questionnaire, '/result-page', 'Demographics');

    demographicFields = demographic ? demographic : getDemographicFromUrl(demographicSection?.fields);
  }

  const clientData = CommonUtility.createClientData(demographic, checkExistingClientIndexSuccessData);

  useEffect(() => {
    if (getPdfSuccess || getPdfError) return;

    if (!questionnaireQuestionsSuccessData?.pdfTemplate) return;

    const pdfReportData = {
      questionnaire_response_data: questionnaireResponse,
      client_data: clientData || {},
      binary_data: questionnaireQuestionsSuccessData?.pdfTemplate,
    };
    fetchGeneratePdfReport(pdfReportData);
  }, []);

  useEffect(() => {
    if (getPdfSuccess && getPdfSuccessData) {
      setPdfData(getPdfSuccessData.content);
    }
  }, [getPdfSuccessData, getPdfSuccessData]);

  useEffect(() => {
    if (questionnaireResponse) {
      checkResultData();
    }
  }, [questionnaireResponse]);

  useEffect(() => {
    if (authSession?.user?.cambianId && saveReportCalled) {
      if (saveReportCalled) {
        saveQuestionnaireResponseAtIndividual(questionnaireResponse);
        setSaveReportCalled(false);
      }
    }
  }, [saveReportCalled, authSession]);

  useEffect(() => {
    if (isSaveQuestionnaireResponseIndividualSuccess && saveReportCalled) {
      sendNotification({ variant: 'success', msg: 'Report successfully saved' });
      setSaveReportCalled(false);
    }
    // else if (isSaveQuestionnaireResponseIndividualError) {
    //   sendNotification({ variant: 'error', msg: 'Unable to save report, please try again later' });
    //   setSaveReportCalled(false);
    // }
  }, [isSaveQuestionnaireResponseIndividualSuccess, isSaveQuestionnaireResponseIndividualError]);

  useEffect(() => {
    if (generateHtmlReportAsPdfSuccess && generateHtmlReportAsPdfSuccessData && downloadPdfCalled) {
      const filename = questionnaire?.title?.trim().replaceAll(/\s+/g, '_');
      CommonUtility.downloadPDF(generateHtmlReportAsPdfSuccessData?.base64Pdf, filename);
      setDownloadPdfCalled(false);
    }
  }, [generateHtmlReportAsPdfSuccess, generateHtmlReportAsPdfSuccessData]);

  const checkResultData = () => {
    let resultData = QuestionnaireUtility.getResultData(questionnaire?.extension);

    const instrumentScores = QuestionnaireUtility.extractInstrumentScores(questionnaireResponse);
    const matchedCondition = QuestionnaireUtility.getMatchedActionCondition(
      action?.metaData?.actionConditions,
      instrumentScores,
    );
    const defaultCondition = action?.metaData?.actionConditions?.find((condition) => condition.default);
    setMatchedActionCondition(matchedCondition || defaultCondition);

    if (resultData || htmlReportActive) {
      resultData = JSON.parse(resultData);
      setShowResult(true);
      if (resultData) {
        if (demographicSection) {
          setReportDemographics(
            QuestionnaireUtility.buildDemographicByParameter(demographicSection, demographicFields),
          );
        }
      } else {
        if (getPdfError) {
          handleResultNavigation();
        }
      }
    } else {
      if (getPdfError) {
        handleResultNavigation();
      }
    }
  };

  const handlePrint = useReactToPrint({
    content: () => document.querySelector('#QuestionnaireReportComponent') || reportRef.current,
    documentTitle: questionnaire?.title?.trim()?.replace(/\s+/g, '_'),
    onAfterPrint: () => (document.title = widgetTitle || 'Widget'),
    removeAfterPrint: false,
    copyStyles: true,
    pageStyle: `
      @page {
        margin: 2in;
        size: auto;
      }
      @media print {
        body {
          font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif !important;
        }
        #questionnaireReport * {
          font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif !important;
          color: black !important;
        }
        #print-banner {
          display: block !important;
        }
      }
    `,
  });

  const onClickOfPrint = async () => {
    const title = questionnaire?.title?.trim()?.replace(/\s+/g, '_');

    setLoading(true);
    try {
      const [reportSettingsResponse, organizationResponse] = await Promise.all([
        callReportSettings(queryClient),
        callOrganizationDetails(queryClient),
      ]);

      const bannerTemplate = reportSettingsResponse?.data?.reportHeader;
      const organization = organizationResponse?.data;

      if (bannerTemplate && organization && demographic) {
        const bannerHtml = ReactDOMServer.renderToString(
          <BannerV2
            demographic={{ ...(demographic?.individuals?.[0] || {}), ...(demographic?.contact || {}) }}
            organization={organization}
            bannerTemplate={bannerTemplate}
          />,
        );

        const reportComponent = document.querySelector('#QuestionnaireReportComponent');
        if (reportComponent) {
          const tempBanner = document.createElement('div');
          tempBanner.id = 'print-banner';
          tempBanner.style.display = 'none';
          tempBanner.innerHTML = bannerHtml;
          reportComponent.insertBefore(tempBanner, reportComponent.firstChild);
        }
      }

      document.title = window.parent.document.title = title;

      handlePrint();

      // Clean up the banner after printing otherwise it will append same banner again
      setTimeout(() => {
        const printBanner = document.getElementById('print-banner');
        if (printBanner) printBanner.remove();
      }, 1000);
    } catch (err) {
      CommonUtility.handleCrossOriginPrint(reportPageUrl, title);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async () => {
    var htmlString = '<div>No data Available</>';
    if (reportRef.current) {
      htmlString =
        reportRef.current.querySelector('#QuestionnaireReportComponent').outerHTML || reportRef.current.innerHTML;
    }

    //const appUrl ='https://test.d2yc83ebf3vw6y.amplifyapp.com/widget/organizations/190e87aa-a3ed-4041-b37d-00e9122aa26f/questionnaireWidget/183a86ea-07e7-4710-99df-a1fd203ad056';
    const appUrl = CommonUtility.removeURLParams(window.location.href);

    setLoading(true);
    try {
      const [reportSettingsResponse, organizationResponse] = await Promise.all([
        callReportSettings(queryClient),
        callOrganizationDetails(queryClient),
      ]);

      let bannerTemplate = reportSettingsResponse?.data?.reportHeader;
      let organization = organizationResponse?.data;

      if (bannerTemplate && organization && demographic) {
        const bannerHtml = ReactDOMServer.renderToString(
          <BannerV2
            demographic={{ ...(demographic?.individuals?.[0] || {}), ...(demographic?.contact || {}) }}
            organization={organization}
            bannerTemplate={bannerTemplate}
          />,
        );
        htmlString = bannerHtml + htmlString;
      }

      await fetchDownloadReport(appUrl, htmlString);
    } catch (error) {
      console.error('Error:', error);
      await fetchDownloadReport(appUrl, htmlString);
    } finally {
      setLoading(false);
    }
  };

  const fetchDownloadReport = async (appUrl, htmlString) => {
    const response = await queryClient.fetchQuery({
      queryKey: DOWNLOAD_HTML_REPORT_QUERY_KEY,
      queryFn: () => fetchDownloadHtmlReport(appUrl, htmlString),
    });

    if (response?.data) {
      const filename = questionnaire?.title?.trim().replaceAll(/\s+/g, '_');
      CommonUtility.downloadPDF(response.data.base64Pdf, filename);
    } else {
      console.error('No data found from server');
    }
  };

  const callOrganizationDetails = async (queryClient) => {
    return await queryClient.fetchQuery({
      queryKey: ORGANIZATION_QUERY_KEY,
      queryFn: fetchOrganizationDetails,
    });
  };

  const callReportSettings = async (queryClient) => {
    return await queryClient.fetchQuery({
      queryKey: REPORT_SETTINGS_QUERY_KEY,
      queryFn: fetchReportSettings,
    });
  };

  const handleSaveToCambianAccount = () => {
    setSaveReportCalled(true);
    if (authSession?.user?.cambianId) {
      saveQuestionnaireResponseAtIndividual(questionnaireResponse);
      setSaveReportCalled(false);
    } else {
      setSaveReportCalled(true);
      handleSignInDialogState(true);
    }
  };

  const handleResultNavigation = () => {
    console.log('TRACE: Report.handleResultNavigation');
    if (!handleNavigationCallback) return;
    handleNavigationCallback(QuestionnaireUtility.getQuestionnaireType(questionnaire?.extension));
  };

  const ReportActionMenu = () => {
    const [anchorEl, setAnchorEl] = React.useState(null);
    const open = Boolean(anchorEl);
    const iconsAndConfiguration = CommonUtility.getPrintDownloadIconsConfiguration();
    const { DownloadIcon, PrintIcon } = iconsAndConfiguration;

    const handleClick = (event) => {
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const reportMenuItems = [
      {
        condition: showSignIn && report?.showSaveIcon,
        onClick: () => handleSaveToCambianAccount(),
        icon: <Image src={cambianLogo} alt="" priority={true} width="23" height="23" />,
        tooltip: 'Save to your Cambian account',
        text: 'Save',
      },
      {
        condition: report?.showPrintIcon,
        onClick: () => onClickOfPrint(),
        icon: <PrintIcon color="primary" />,
        tooltip: 'Print report',
        text: 'Print',
      },
      {
        condition: report?.showDownloadIcon,
        onClick: () => handleDownload(),
        icon: <DownloadIcon color="primary" />,
        tooltip: 'Download report PDF',
        text: 'Download',
      },
    ];

    return (
      <>
        {(report?.showPrintIcon || report?.showDownloadIcon || report?.showSaveIcon) && (
          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <IconButton
              id="basic-button"
              aria-controls={open ? 'basic-menu' : undefined}
              aria-haspopup="true"
              aria-expanded={open ? 'true' : undefined}
              onClick={handleClick}
            >
              <MoreVert />
            </IconButton>
            <Menu
              id="basic-menu"
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              MenuListProps={{
                'aria-labelledby': 'basic-button',
              }}
            >
              {reportMenuItems.map(
                ({ condition, onClick, icon, tooltip, text }, index) =>
                  condition && (
                    <MenuItem
                      key={index}
                      onClick={() => {
                        onClick();
                        handleClose();
                      }}
                    >
                      <ListItemIcon>{icon}</ListItemIcon>
                      <ListItemText>
                        <Tooltip title={tooltip}>{text}</Tooltip>
                      </ListItemText>
                    </MenuItem>
                  ),
              )}
            </Menu>
          </Box>
        )}
      </>
    );
  };

  const getResult = () => {
    CommonUtility.removeHtmlLoader();
    if (questionnaireResponse) {
      return (
        <Box id="questionnaireReport">
          <QuestionnaireReportViewerV2
            demographic={{
              ...(demographic?.individuals?.[0] || {}),
              ...(demographic?.contact || {}),
            }}
            fhirQuestionnaire={questionnaire}
            fhirResponse={questionnaireResponse}
            demographics={reportDemographics}
            resultNextActionCallback={
              (action?.enabled && !isActionPerformed && matchedActionCondition) || headingAndDescriptionPage?.enabled
                ? handleResultNavigation
                : undefined
            }
            nextButtonText={action?.actionButton}
            isCallbackPrint={isPrintScreen || printRequest}
            reportPageUrl={reportPageUrl}
            pdf={printRequest ? undefined : pdfData}
            isWebReportAvailable={showResult}
            height={showResult ? '75vh' : '85vh'}
          />
        </Box>
      );
    } else {
      <></>;
    }
  };

  return (
    <>
      <Loader
        active={
          generateHtmlReportAsPdfFetching || isSaveQuestionnaireResponseIndividualFetching || getPdfFetching || loading
        }
      />
      <Box sx={{ px: 'calc(4% - 14px)', pt: '24px' }}>
        <ReportActionMenu />
      </Box>
      <Box sx={{ px: '4%', pb: '36px' }} className="report" ref={reportRef}>
        {getResult()}
      </Box>
    </>
  );
};
