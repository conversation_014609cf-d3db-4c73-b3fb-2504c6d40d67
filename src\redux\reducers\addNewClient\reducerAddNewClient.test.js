import {
  ADD_NEW_CLIENT_REQUEST,
  ADD_NEW_CLIENT_SUCCESS,
  ADD_NEW_CLIENT_ERROR,
  ADD_NEW_CLIENT_RESET,
} from '../../actions/addNewClient';

import * as G<PERSON><PERSON>BA<PERSON> from '../globals';
import { addNewPatientReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  addNewPatientFetching: true,
  addNewPatientSuccess: false,
  addNewPatientError: false,
  addNewPatientErrorData: null,
};

const successState = {
  addNewPatientFetching: false,
  addNewPatientSuccess: true,
  addNewPatientError: false,
  addNewPatientErrorData: null,
  addNewPatientSuccessData: data,
};

const errorState = {
  addNewPatientFetching: false,
  addNewPatientSuccess: false,
  addNewPatientError: true,
  addNewPatientErrorData: data,
};

const resetState = {
  addNewPatientFetching: false,
  addNewPatientSuccess: false,
  addNewPatientError: false,
  addNewPatientErrorData: null,
  addNewPatientSuccessData: null,
};

describe('Reschedule Reducer', () => {
  it('should return the initial state', () => {
    expect(addNewPatientReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle ADD_NEW_CLIENT_REQUEST', () => {
    expect(
      addNewPatientReducer(initialState, {
        type: ADD_NEW_CLIENT_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle ADD_NEW_CLIENT_SUCCESS', () => {
    expect(
      addNewPatientReducer(initialState, {
        type: ADD_NEW_CLIENT_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle ADD_NEW_CLIENT_ERROR', () => {
    expect(
      addNewPatientReducer(initialState, {
        type: ADD_NEW_CLIENT_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle ADD_NEW_CLIENT_RESET', () => {
    expect(
      addNewPatientReducer(initialState, {
        type: ADD_NEW_CLIENT_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
