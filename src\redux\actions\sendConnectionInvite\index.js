export const SEND_CONNECTION_INVITE_REQUEST = 'SEND_CONNECTION_INVITE_REQUEST';
export const SEND_CONNECTION_INVITE_SUCCESS = 'SEND_CONNECTION_INVITE_SUCCESS';
export const SEND_CONNECTION_INVITE_ERROR = 'SEND_CONNECTION_INVITE_ERROR';
export const SEND_CONNECTION_INVITE_RESET = 'SEND_CONNECTION_INVITE_RESET';

/*
 * action creators
 */

export function sendConnectionInvite(data) {
  return {
    type: SEND_CONNECTION_INVITE_REQUEST,
    payload: data,
  };
}

export function sendConnectionInviteSuccess(data) {
  return {
    type: SEND_CONNECTION_INVITE_SUCCESS,
    payload: data,
  };
}

export function sendConnectionInviteError() {
  return {
    type: SEND_CONNECTION_INVITE_ERROR,
  };
}

export const sendConnectionInviteDetails = () => {
  return {
    type: SEND_CONNECTION_INVITE_RESET,
  };
};
