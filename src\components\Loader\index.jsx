import React from 'react';
import { Stack, Typography, Backdrop, CircularProgress } from '@mui/material';
import { useSelector } from 'react-redux';

export const Loader = ({ active = false }) => {
  const { isQuestionnaireWidgetSuccess, questionnaireWidgetSuccessData } = useSelector(
    (state) => state.questionnaireWidgetReducer,
  );

  return (
    <Backdrop
      sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1, backgroundColor: 'rgba(0,0,0,0.5)' }}
      open={active}
    >
      <Stack direction="column" alignItems="center" spacing={2} justifyContent="center">
        <CircularProgress color="inherit" />
        {isQuestionnaireWidgetSuccess &&
          questionnaireWidgetSuccessData &&
          questionnaireWidgetSuccessData.spinnerText && (
            <Typography>{questionnaireWidgetSuccessData.spinnerText}</Typography>
          )}
      </Stack>
    </Backdrop>
  );
};
