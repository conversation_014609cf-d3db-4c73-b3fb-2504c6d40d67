/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import {
  GET_BOOKING_SLOTS_ERROR,
  GET_BOOKING_SLOTS_SUCCESS,
  GET_BOOKING_SLOTS_REQUEST,
} from '../../actions/getBookingSlots';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const bookingSlotsReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case GET_BOOKING_SLOTS_REQUEST:
      return {
        ...state,
        isBookingSlotsFetching: true,
        fetchBookingSlotsSuccess: false,
        fetchBookingSlotsError: false,
        bookingSlotsErrorData: null,
      };
    case GET_BOOKING_SLOTS_SUCCESS: {
      return {
        ...state,
        isBookingSlotsFetching: false,
        fetchBookingSlotsSuccess: true,
        fetchBookingSlotsError: false,
        bookingSlotsErrorData: null,
        bookingSlotsSuccessData: payload,
      };
    }
    case GET_BOOKING_SLOTS_ERROR:
      return {
        ...state,
        isBookingSlotsFetching: false,
        fetchBookingSlotsSuccess: false,
        fetchBookingSlotsError: true,
        bookingSlotsErrorData: payload,
      };
    default:
      return state;
  }
};
