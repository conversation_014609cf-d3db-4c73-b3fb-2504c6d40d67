import { Grid } from '@mui/material';
import { CambianCalendar } from '@/components';
import { isValid, min } from 'date-fns';

const getMaxAllowedDate = (maxDate, maxAllowedDateForSelectedService) => {
  const date1 = maxDate;
  const date2 = maxAllowedDateForSelectedService;

  const isDate1Valid = isValid(date1);
  const isDate2Valid = isValid(date2);

  if (!isDate1Valid && !isDate2Valid) {
    return null;
  }

  if (isDate1Valid && !isDate2Valid) {
    return date1;
  }

  if (!isDate1Valid && isDate2Valid) {
    return date2;
  }

  return min([date1, date2]);
};

export const Calendar = (props) => {
  const { date, handleSetDate, minDate, maxDate, maxAllowedDateForSelectedService } = props;
  const maxAllowedDate = getMaxAllowedDate(maxDate, maxAllowedDateForSelectedService);

  return (
    <Grid container>
      <Grid item md={6} lg={7} xl={4} sx={{ ml: { sm: -3 } }}>
        <CambianCalendar disablePast minDate={minDate} maxDate={maxAllowedDate} date={date} onChange={handleSetDate} />
      </Grid>
    </Grid>
  );
};
