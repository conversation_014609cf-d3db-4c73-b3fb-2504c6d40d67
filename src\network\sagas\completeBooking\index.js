/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';

import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  COMPLETE_BOOKING_ERROR,
  COMPLETE_BOOKING_SUCCESS,
  COMPLETE_BOOKING_REQUEST,
} from '../../../redux/actions/completeBooking';

import { completeBookingApi } from './api';

/**
 *
 * @param {*} action
 */
export function* completeBooking(action) {
  try {
    const response = yield call(completeBookingApi, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: COMPLETE_BOOKING_SUCCESS, payload: data });
    } else {
      yield put({ type: COMPLETE_BOOKING_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: COMPLETE_BOOKING_ERROR });
  }
}

// Our watcher Saga:
export function* watchCompleteBooking() {
  yield takeEvery(COMPLETE_BOOKING_REQUEST, completeBooking);
}
