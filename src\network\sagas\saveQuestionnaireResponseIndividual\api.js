import { makeNetworkCall } from '../..';
import { INDIVIDUAL, INDIVIDUAL_ID } from '../../../utils/constants';
import { SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL } from '../../../utils/constants/awsApiEndpoints';

export function makeApiSaveQuestionnaireResponseIndividual(action) {
  const { payload = {} } = action || {};
  const { headers = {}, data, individualId } = payload || {};

  const mainUrl = SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL.replace(INDIVIDUAL_ID, individualId);

  const config = {
    targetAwsEnv: INDIVIDUAL,
    method: 'POST',
    url: mainUrl,
    headers: headers,
    data: data,
  };

  return makeNetworkCall(config);
}
