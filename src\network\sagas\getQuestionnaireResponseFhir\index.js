/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  GET_QUESTIONNAIRE_RESPONSE_FHIR_ERROR,
  GET_QUESTIONNAIRE_RESPONSE_FHIR_SUCCESS,
  GET_QUESTIONNAIRE_RESPONSE_FHIR_REQUEST,
} from '../../../redux/actions/getQuestionnaireResponseFhir';

import { makeApiFetchQuestionnaireResponseFhir } from './api';

/**
 *
 * @param {*} action
 */

export function* fetchQuestionnaireResponseFhir(action) {
  try {
    const response = yield call(makeApiFetchQuestionnaireResponseFhir, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: GET_QUESTIONNAIRE_RESPONSE_FHIR_SUCCESS, payload: data });
    } else {
      yield put({ type: GET_QUESTIONNAIRE_RESPONSE_FHIR_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: GET_QUESTIONNAIRE_RESPONSE_FHIR_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchGetQuestionnaireResponseFhir() {
  yield takeEvery(GET_QUESTIONNAIRE_RESPONSE_FHIR_REQUEST, fetchQuestionnaireResponseFhir);
}
