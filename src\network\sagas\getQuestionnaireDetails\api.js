import { PRIVATE, PUBLIC } from '@/containers/QuestionnaireWidget/questionnaireConstants';
import { makeNetworkCall } from '../..';
import { QUESTIONNAIRE_ID, ORGANIZATION_ID, NETWORK, ORGANIZATION } from '@/utils/constants';
import { GET_PRIVATE_QUESTIONNAIRE, GET_PUBLIC_QUESTIONNAIRE } from '@/utils/constants/awsApiEndpoints';

export function makeApiFetchQuestionnaireQuestions(action) {
  const { payload = {} } = action || {};
  const { headers = {}, questionnaireId = null, repository = PRIVATE, organizationId = '' } = payload || {};

  let questionnaireUrl;
  let targetAwsEnv;
  if (repository === PUBLIC) {
    questionnaireUrl = GET_PUBLIC_QUESTIONNAIRE;
    targetAwsEnv = NETWORK;
  } else {
    questionnaireUrl = GET_PRIVATE_QUESTIONNAIRE;
    targetAwsEnv = ORGANIZATION;
  }

  questionnaireUrl = questionnaireUrl
    .replace(ORGANIZATION_ID, organizationId)
    .replace(QUESTIONNAIRE_ID, questionnaireId);

  const config = {
    targetAwsEnv,
    method: 'GET',
    url: questionnaireUrl,
    headers: headers,
    formData: false,
  };
  return makeNetworkCall(config);
}
