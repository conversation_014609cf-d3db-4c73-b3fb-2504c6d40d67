import { SEARCH_CREATE_INDIVIDUAL_AT_CLIENT_INDEX } from '@/utils/constants/awsApiEndpoints';
import { makeNetworkCall } from '../..';
import { ORGANIZATION_ID, REGISTRY } from '@/utils/constants';

export function searchIndividualAtClientIndex(action) {
  const { payload = {} } = action || {};
  const { headers = {}, organizationId, searchBody } = payload || {};

  const URL = SEARCH_CREATE_INDIVIDUAL_AT_CLIENT_INDEX.replace(ORGANIZATION_ID, organizationId);

  const config = {
    method: 'POST',
    url: URL,
    headers: headers,
    data: searchBody,
  };

  return makeNetworkCall(config);
}
