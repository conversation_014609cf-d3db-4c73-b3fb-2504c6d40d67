import { GET_REPORT_DATA_FOR_PDF } from '@/utils/constants/awsApiEndpoints';
import { DOWNLOAD_REPORT_ID } from '@/utils/constants';
import { makeNetworkCall } from '@/network';

export function makeApiGetReportDataForPdf(action) {
  const { payload = {} } = action || {};
  const { headers = {}, questionnaireReportById } = payload || {};

  const mainUrl = GET_REPORT_DATA_FOR_PDF.replace(DOWNLOAD_REPORT_ID, questionnaireReportById);

  const config = {
    method: 'GET',
    url: mainUrl,
    headers: headers,
    // data: data,
  };
  return makeNetworkCall(config);
}
