/*
 * CREATE_OTP action types
 */

export const CREATE_OTP_REQUEST = 'CREATE_OTP_REQUEST';
export const CREATE_OTP_SUCCESS = 'CREATE_OTP_SUCCESS';
export const CREATE_OTP_ERROR = 'CREATE_OTP_ERROR';
export const CREATE_OTP_RESET = 'CREATE_OTP_RESET';

/*
 * action creators
 */
export function createOtp(data) {
  return {
    type: CREATE_OTP_REQUEST,
    payload: data,
  };
}

export function createOtpSuccess(data) {
  return {
    type: CREATE_OTP_SUCCESS,
    payload: data,
  };
}

export function createOtpError() {
  return {
    type: CREATE_OTP_ERROR,
  };
}

export const resetOtp = () => {
  return {
    type: CREATE_OTP_RESET,
  };
};
