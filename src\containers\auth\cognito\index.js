import {
  CognitoIdentityProviderClient,
  InitiateAuthCommand,
  SignUpCommand,
  // It's not being used right now, but it will probably be needed later.
  // AdminGetUserCommand,
  // AdminDeleteUserCommand,
} from '@aws-sdk/client-cognito-identity-provider';
import { createHmac } from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import { AwsError, CustomError } from '../index';
import {
  FIRST_NAME_COGNITO_ATTRIBUTE_NAME,
  INDIVIDUAL,
  LAST_NAME_COGNITO_ATTRIBUTE_NAME,
  MACHINE_ACCESS_TOKEN,
  NETWORK,
  ORGANIZATION,
} from '@/utils/constants';
import { cookies } from 'next/headers';

const {
  COGNITO_INDIVIDUAL_HUMAN_REGION,
  COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_ID,
  COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_SECRET,
} = process.env;

const cognitoClient = new CognitoIdentityProviderClient({
  region: COGNITO_INDIVIDUAL_HUMAN_REGION,
});

const AUTH_FLOW = 'USER_PASSWORD_AUTH';

export const initiateSignIn = async ({
  username,
  password,
  appClientId = COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_ID,
  appClientSecret = COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_SECRET,
}) => {
  console.log(username, password, appClientId, appClientSecret);
  if (username.includes('@')) {
    username = sanitizeEmailAddressForCognito(username);
  }
  const params = {
    AuthFlow: AUTH_FLOW,
    ClientId: appClientId,
    AuthParameters: {
      USERNAME: username,
      PASSWORD: password,
      SECRET_HASH: generateSecretHash(username, appClientId, appClientSecret),
    },
  };

  try {
    const initiateAuthCommand = new InitiateAuthCommand(params);
    const response = await cognitoClient.send(initiateAuthCommand);

    const status = response.$metadata.httpStatusCode;

    if (status === 200) {
      return [response, null];
    }
    return [null, new CustomError({ status, message: response.toString() })];
  } catch (cognitoError) {
    return [null, new AwsError(cognitoError)];
  }
};

export const parseIdToken = (idToken) => {
  const cognitoIDTokenParts = idToken.split('.');
  const claimsBuff = Buffer.from(cognitoIDTokenParts[1], 'base64');
  const cognitoIDTokenClaims = JSON.parse(claimsBuff.toString('utf8'));

  return {
    id: cognitoIDTokenClaims.sub,
    cambianId: cognitoIDTokenClaims['custom:cambian_id'],
    patientId: cognitoIDTokenClaims['custom:patient_id'],
    given_name: cognitoIDTokenClaims.given_name,
    family_name: cognitoIDTokenClaims.family_name,
    email: cognitoIDTokenClaims.email,
    idToken: idToken,
  };
};

/**
 * @async
 * @param {Object} param0
 * @param {string} param0.email
 * @param {string} param0.password
 * @param {string} param0.firstName
 * @param {string} param0.lastName
 * @returns {Promise<[Object, Object]>} - A Promise that resolves to an array containing the result and errorObject.
 */
export const signUp = async ({ email, password, firstName, lastName, cambianId }) => {
  if (!email || !password || !firstName || !lastName) {
    return [
      null,
      new CustomError({
        status: 400,
        message: 'email, password, firstName, and lastName need to be provided',
      }),
    ];
  }

  // TODO: Need to explore this
  // // Sanitize the username before sending it to Cognito
  // const sanitizedUsername = sanitizeEmailAddressForCognito(username);

  //const cambianId = uuidv4();

  // TODO: This is just to support legacy service. Should be removed in the future.
  const patientId = uuidv4();

  const params = {
    ClientId: COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_ID,
    SecretHash: generateSecretHash(email),
    Password: password,
    Username: email,
    UserAttributes: [
      {
        Name: LAST_NAME_COGNITO_ATTRIBUTE_NAME,
        Value: lastName,
      },
      {
        Name: FIRST_NAME_COGNITO_ATTRIBUTE_NAME,
        Value: firstName,
      },
      {
        Name: 'custom:cambian_id',
        Value: cambianId,
      },
      {
        Name: 'custom:patient_id',
        Value: patientId,
      },
    ],
  };

  const cognitoCommand = new SignUpCommand(params);

  try {
    const res = await cognitoClient.send(cognitoCommand);

    return [
      {
        status: res['$metadata'].httpStatusCode,
      },
      null,
    ];
  } catch (cognitoError) {
    return [null, new AwsError(cognitoError)];
  }
};
export const setOrgMachineAccessTokenInCookie = async (env) => {
  if (env !== NETWORK && env !== ORGANIZATION && env !== INDIVIDUAL) {
    return [null, new CustomError({ status: 399, message: 'env is either network, individual, or organization' })];
  }

  let params = {};

  if (env === NETWORK) {
    params = {
      username: process.env.COGNITO_NETWORK_MACHINE_USERNAME,
      password: process.env.COGNITO_NETWORK_MACHINE_PASSWORD,
      userPoolId: process.env.COGNITO_NETWORK_MACHINE_USER_POOL_ID,
      appClientId: process.env.COGNITO_NETWORK_MACHINE_APP_CLIENT_ID,
      appClientSecret: process.env.COGNITO_NETWORK_MACHINE_APP_CLIENT_SECRET,
    };
  } else if (env == ORGANIZATION) {
    params = {
      username: process.env.COGNITO_ORG_MACHINE_USERNAME,
      password: process.env.COGNITO_ORG_MACHINE_PASSWORD,
      userPoolId: process.env.COGNITO_ORG_MACHINE_USER_POOL_ID,
      appClientId: process.env.COGNITO_ORG_MACHINE_APP_CLIENT_ID,
      appClientSecret: process.env.COGNITO_ORG_MACHINE_APP_CLIENT_SECRET,
    };
  } else {
    params = {
      username: process.env.COGNITO_INDIVIDUAL_MACHINE_USERNAME,
      password: process.env.COGNITO_INDIVIDUAL_MACHINE_PASSWORD,
      userPoolId: process.env.COGNITO_INDIVIDUAL_MACHINE_USER_POOL_ID,
      appClientId: process.env.COGNITO_INDIVIDUAL_MACHINE_APP_CLIENT_ID,
      appClientSecret: process.env.COGNITO_INDIVIDUAL_MACHINE_APP_CLIENT_SECRET,
    };
  }
  console.log('SIGN IN PARAM ', params);
  console.log('ENV', env);

  const [res, error] = await initiateSignIn(params);
  if (error) {
    console.log('Failed authenticating to userpool', env);
    console.log(error);
    return [null, error];
  }
  const { AccessToken: accessToken, ExpiresIn: expiresIn } = res?.AuthenticationResult;

  cookies().set(MACHINE_ACCESS_TOKEN(env), accessToken, {
    path: '/',
    httpOnly: false, // Enable to get this cookie value from client (browser) with javascript like document.cookie
    secure: true,
    maxAge: expiresIn,
    sameSite: 'none',
  });
  return [{ accessToken, expiresIn }, null];
};

// **It's not being used right now, but it will probably be needed later. For that, we need to create an IAM user for Widget.**
// export const adminGetUser = async ({ username }) => {
//   const params = {
//     UserPoolId: COGNITO_INDIVIUDAL_HUMAN_USER_POOL_ID,
//     Username: username,
//   };
//   const command = new AdminGetUserCommand(params);
//   try {
//     const res = await cognitoClientWithIAM.send(command);

//     return [
//       {
//         userAttributes: res.UserAttributes,
//         status: res['$metadata'].httpStatusCode,
//       },
//       null,
//     ];
//   } catch (cognitoError) {
//     return [null, new AwsError(cognitoError)];
//   }
// };

export const getAttributeValue = (attributes, attributeName) => {
  const attribute = attributes.find((attr) => attr.Name === attributeName);
  return attribute ? attribute.Value : null;
};

// **It's not being used right now, but it will probably be needed later. For that, we need to create an IAM user for Widget.**
// export const adminDeleteUser = async ({ username }) => {
//   const params = {
//     UserPoolId: COGNITO_INDIVIUDAL_HUMAN_USER_POOL_ID,
//     Username: username,
//   };
//   const command = new AdminDeleteUserCommand(params);
//   try {
//     const res = await cognitoClientWithIAM.send(command);

//     return [
//       {
//         status: res['$metadata'].httpStatusCode,
//       },
//       null,
//     ];
//   } catch (cognitoError) {
//     return [null, new AwsError(cognitoError)];
//   }
// };

export const sanitizeEmailAddressForCognito = (emailAddress) => {
  return emailAddress.trim().toLowerCase();
};

/**
 * @param email - User email
 */
export const generateSecretHash = (
  username,
  appClientId = COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_ID,
  appClientSecret = COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_SECRET,
) => {
  console.log('HASH');

  const hasher = createHmac('sha256', appClientSecret);
  hasher.update(`${username}${appClientId}`);
  return hasher.digest('base64');
};
