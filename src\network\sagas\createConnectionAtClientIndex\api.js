import { CREATE_CONNECTION_AT_CLIENT_INDEX } from '@/utils/constants/awsApiEndpoints';
import { makeNetworkCall } from '../..';
import { ORGANIZATION_ID } from '@/utils/constants';

export function createConnectionAtClientIndex(action) {
  const { payload = {} } = action || {};
  const { headers = {}, organizationId, connectionData } = payload || {};

  const URL = CREATE_CONNECTION_AT_CLIENT_INDEX.replace(ORGANIZATION_ID, organizationId);

  console.log('headers', headers);
  const config = {
    method: 'POST',
    url: URL,
    headers: headers,
    data: connectionData,
  };

  return makeNetworkCall(config);
}
