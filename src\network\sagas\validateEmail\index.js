/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import {
  VALIDATE_EMAIL_ERROR,
  VALIDATE_EMAIL_REQUEST,
  VALIDATE_EMAIL_SUCCESS,
} from '../../../redux/actions/validateEmail';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { makeApiValidateEmail } from './api';
/**
 *
 * @param {*} action
 */

export function* validateEmail(action) {
  try {
    const response = yield call(makeApiValidateEmail, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: VALIDATE_EMAIL_SUCCESS, payload: data });
    } else {
      yield put({ type: VALIDATE_EMAIL_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: VALIDATE_EMAIL_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchValidateEmail() {
  yield takeEvery(VALIDATE_EMAIL_REQUEST, validateEmail);
}
