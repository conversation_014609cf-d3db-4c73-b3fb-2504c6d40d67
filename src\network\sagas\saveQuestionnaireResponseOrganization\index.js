import { call, put, takeEvery } from 'redux-saga/effects';
import {
  SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_REQUEST,
  SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_SUCCESS,
  SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_ERROR,
} from '../../../redux/actions/saveQuestionnaireResponseOrganization';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { makeApiSaveQuestionnaireResponseOrganization } from './api';

export function* saveQuestionnaireResponseOrganization(action) {
  try {
    const response = yield call(makeApiSaveQuestionnaireResponseOrganization, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_SUCCESS, payload: data });
    } else {
      yield put({ type: SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_ERROR });
  }
}

export function* watchSaveQuestionnaireResponseOrganization() {
  yield takeEvery(SAVE_QUESTIONNAIRE_RESPONSE_ORGANIZATION_REQUEST, saveQuestionnaireResponseOrganization);
}
