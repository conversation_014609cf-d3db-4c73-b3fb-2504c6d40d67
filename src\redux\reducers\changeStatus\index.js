import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  CHANGE_STATUS_REQUEST,
  CHANGE_STATUS_SUCCESS,
  CHANGE_STATUS_ERROR,
  CHANGE_STATUS_RESET,
} from '../../actions/changeStatus';
export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};
export const changeStatusReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case CHANGE_STATUS_REQUEST:
      return {
        ...state,
        isChangeStatusFetching: true,
        changeStatusSuccess: false,
        changeStatusError: false,
        changeStatusErrorData: null,
      };
    case CHANGE_STATUS_SUCCESS: {
      return {
        ...state,
        isChangeStatusFetching: false,
        changeStatusSuccess: true,
        changeStatusError: false,
        changeStatusErrorData: null,
        changeStatusSuccessData: payload,
      };
    }
    case CHANGE_STATUS_ERROR:
      return {
        ...state,
        isChangeStatusFetching: false,
        changeStatusSuccess: false,
        changeStatusError: true,
        changeStatusErrorData: payload,
      };
    case CHANGE_STATUS_RESET:
      return {
        ...state,
        isChangeStatusFetching: false,
        changeStatusSuccess: false,
        changeStatusError: false,
        changeStatusErrorData: null,
      };
    default:
      return state;
  }
};
