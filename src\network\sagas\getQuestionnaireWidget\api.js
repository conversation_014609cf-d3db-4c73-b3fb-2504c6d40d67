import { makeNetworkCall } from '@/network';
import { ORGANIZATION_ID, WIDGET_ID } from '@/utils/constants';
import { GET_QUESTIONNAIRE_WIDGET } from '@/utils/constants/awsApiEndpoints';

export function makeApiFetchQuestionnaireWidget(action) {
  const { payload = {} } = action || {};
  const { organizationId, widgetId, lang, headers } = payload || {};

  let URL = GET_QUESTIONNAIRE_WIDGET.replace(ORGANIZATION_ID, organizationId).replace(WIDGET_ID, widgetId);

  // Add language parameter if specified
  if (lang) {
    URL = `${URL}?lang=${lang}`;
  }

  const config = {
    method: 'GET',
    url: URL,
    // headers: { 'Content-Type': 'application/json' },
    formData: false,
  };

  return makeNetworkCall(config);
}
