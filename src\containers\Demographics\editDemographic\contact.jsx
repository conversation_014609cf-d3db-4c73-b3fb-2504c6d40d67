import React, { useEffect, useState } from 'react';
import {
  Typo<PERSON>,
  FormControl,
  Box,
  Grid,
  Stack,
  Select,
  InputLabel,
  MenuItem,
  FormHelperText,
  Button,
} from '@mui/material';
import { Add } from '@mui/icons-material';
import dataValidation from '../../../utils/dataValidation/dataValidation';
import { contactFieldValidation } from '../../../utils/helpers/validation';
import { sortFields } from '../demographicUtility';
import { ContactField, CambianTooltip, Loader } from '@/components';
import { AddressFields } from './AddressFields';
import { MaskTextField } from '../../../components/MaskTextField';
import { useTranslation } from 'react-i18next';

const genderOptions = [
  { displayName: 'Male', value: 'male' },
  { displayName: 'Female', value: 'female' },
  { displayName: 'Unknown', value: 'unknown' },
];
const allowNotificationsOptions = [
  { displayName: 'Yes', value: true, disabled: false },
  { displayName: 'No', value: false, disabled: false },
];
export const Contact = (props) => {
  const {
    demographicFields,
    contact,
    setTriggerUseEffect,
    preferredContactMethod,
    handlePreferredContactMethodCallback,
    handleContactChangeCallback,
    fields,
    isClientSummary,
    validationData,
    otpVerificationEnabled,
    setShowValidationErrors,
    onVerificationSuccess,
    primaryPhoneVerified,
    primaryEmailVerified,
    verifiedValues,
    handleVerificationClick,
    handleVerify,
    sendOtpError,
    isSendingOtp,
    isVerifying,
    contactFieldTexts,
  } = props;
  const { t, i18n } = useTranslation();

  const [contactMethodOptions, setContactMethodOptions] = useState([
    {
      id: 'EMAIL',
      value: t('email'),
      disabled: false,
    },
    {
      id: 'PHONE',
      value: t('phone'),
      disabled: false,
    },
  ]);
  useEffect(() => {
    setContactMethodOptions([
      {
        id: 'EMAIL',
        value: t('email'),
        disabled: false,
      },
      {
        id: 'PHONE',
        value: t('phone'),
        disabled: false,
      },
    ]);
  }, [i18n.language]);

  const [emails, setEmails] = useState(
    contact?.emailAddresses?.length ? contact?.emailAddresses : [{ emailAddress: '', primary: true }],
  );

  const [phones, setPhones] = useState(
    contact?.phoneNumbers?.length ? contact?.phoneNumbers : [{ phoneNumber: '', primary: true }],
  );

  const [addresses, setAddresses] = useState(contact?.addresses || []);

  let primaryPhoneIndex = contact?.phoneNumbers?.length
    ? contact?.phoneNumbers.findIndex((phone) => phone.primary === true)
    : 0;
  let primaryEmailIndex = contact?.emailAddresses?.length
    ? contact?.emailAddresses.findIndex((email) => email.primary === true)
    : 0;

  useEffect(() => {
    setEmails(contact?.emailAddresses?.length ? contact?.emailAddresses : [{ emailAddress: '', primary: true }]);
    setPhones(contact?.phoneNumbers?.length ? contact?.phoneNumbers : [{ phoneNumber: '', primary: true }]);
    setAddresses(contact?.addresses || []);
    primaryPhoneIndex = contact?.phoneNumbers?.length
      ? contact?.phoneNumbers.findIndex((phone) => phone.primary === true)
      : 0;
    primaryEmailIndex = contact?.emailAddresses?.length
      ? contact?.emailAddresses.findIndex((email) => email.primary === true)
      : 0;
    setTriggerUseEffect((oldValue) => !oldValue);
  }, [contact]);

  const getEmail = (item) => {
    if (!item.allowMultiple) {
      return (
        <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 1, pl: { xs: 1, sm: 3 }, pr: 1 }}>
          <ContactField
            contact={emails}
            index={0}
            type="email"
            label={item.display}
            required={item.isMandatory}
            value={emails[primaryEmailIndex]?.emailAddress || ''}
            onChange={({ value: updatedEmail, validationError, validationHelperText }) => {
              setShowValidationErrors(true);
              const updatedEmailObj = {
                ...emails[primaryEmailIndex],
                emailAddress: updatedEmail,
                primary: true,
              };
              const updatedEmails = [updatedEmailObj];
              setEmails(updatedEmails);
              handleContactChange(updatedEmails, 'emailAddresses', item.isMandatory, [
                { error: validationError, helperText: validationHelperText },
              ]);
            }}
            isReadOnly={isClientSummary}
            error={!!validationData?.contact?.emailAddresses[primaryEmailIndex]}
            helperText={validationData?.contact?.emailAddresses[primaryEmailIndex]}
            isPrimary={true}
            isVerified={primaryEmailVerified && true}
            otpVerificationEnabled={otpVerificationEnabled}
            handleVerificationClick={handleVerificationClick}
            handleVerify={handleVerify}
            sendOtpError={sendOtpError}
            isSendingOtp={isSendingOtp}
            isVerifying={isVerifying}
            texts={contactFieldTexts}
            CambianTooltip={CambianTooltip}
            Loader={Loader}
            preferredContactMethod={demographicFields?.preferredContactMethod}
          />
        </Grid>
      );
    }

    return (
      <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 1, pl: { xs: 1, sm: 3 }, pr: 1 }}>
        {emails.map((email, emailIndex) => (
          <ContactField
            key={`email-${emailIndex}`}
            contact={emails}
            index={emailIndex}
            type="email"
            label={item.display}
            allowMultiple={item.allowMultiple}
            required={item.isMandatory}
            value={email.emailAddress}
            onChange={({ value: updatedEmail, validationError, validationHelperText }) => {
              setShowValidationErrors(true);
              setEmails((prevEmails) => {
                const updatedEmails = [...prevEmails];
                updatedEmails[emailIndex].emailAddress = updatedEmail;
                const validationMeta = updatedEmails.map((email) => ({
                  error: email.emailAddress === updatedEmail ? validationError : false,
                  helperText: email.emailAddress === updatedEmail ? validationHelperText : '',
                }));
                handleContactChange(updatedEmails, 'emailAddresses', item.isMandatory, validationMeta);
                return updatedEmails;
              });
            }}
            onSetPrimary={() => {
              const updatedEmails = emails.map((email, i) => ({
                ...email,
                primary: i === emailIndex,
              }));
              setEmails(updatedEmails);
              handleContactChange(updatedEmails, 'emailAddresses', item.isMandatory);
              const isThisEmailVerified = verifiedValues.has(emails[emailIndex].emailAddress);
              onVerificationSuccess('email', isThisEmailVerified, emails[emailIndex].emailAddress);
            }}
            onDelete={() => {
              const updatedEmails = emails.filter((_, i) => i !== emailIndex);
              if (!updatedEmails.some((email) => email.primary) && updatedEmails.length > 0) {
                updatedEmails[0].primary = true;
              }
              setEmails(updatedEmails);
              handleContactChange(updatedEmails, 'emailAddresses', item.isMandatory);
            }}
            isReadOnly={isClientSummary}
            error={!!validationData?.contact?.emailAddresses[emailIndex]}
            helperText={validationData?.contact?.emailAddresses[emailIndex]}
            isPrimary={email.primary}
            isVerified={email.primary ? primaryEmailVerified : verifiedValues.has(email.emailAddress)}
            otpVerificationEnabled={otpVerificationEnabled}
            handleVerificationClick={handleVerificationClick}
            handleVerify={handleVerify}
            sendOtpError={sendOtpError}
            isSendingOtp={isSendingOtp}
            isVerifying={isVerifying}
            texts={contactFieldTexts}
            CambianTooltip={CambianTooltip}
            Loader={Loader}
            preferredContactMethod={demographicFields?.preferredContactMethod}
          />
        ))}
        <Button
          variant="text"
          startIcon={<Add />}
          onClick={() => {
            const newEmail = { emailAddress: '', primary: emails.length === 0 };
            setEmails((prevEmails) => [...prevEmails, newEmail]);
          }}
          sx={{
            p: '15px 1px',
            cursor: 'pointer',
            fontSize: '15px',
            '&:hover': { backgroundColor: 'transparent', color: '#23527c' },
          }}
        >
          {t('addEmail')}
        </Button>
      </Grid>
    );
  };

  const getPhoneNumber = (item) => {
    if (!item.allowMultiple) {
      return (
        <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 1, pl: { xs: 1, sm: 3 }, pr: 1 }}>
          <ContactField
            contact={phones}
            index={0}
            type="phone"
            label={item.display}
            required={item.isMandatory}
            value={phones[primaryPhoneIndex]?.phoneNumber || ''}
            onChange={({ value: updatedPhone, validationError, validationHelperText }) => {
              setShowValidationErrors(true);
              const updatedPhoneObj = {
                ...phones[primaryPhoneIndex],
                phoneNumber: updatedPhone,
                primary: true,
              };
              const updatedPhones = [updatedPhoneObj];
              setPhones(updatedPhones);
              handleContactChange(updatedPhones, 'phoneNumbers', item.isMandatory, [
                { error: validationError, helperText: validationHelperText },
              ]);
            }}
            isReadOnly={isClientSummary}
            error={!!validationData?.contact?.phoneNumbers[primaryPhoneIndex]}
            helperText={validationData?.contact?.phoneNumbers[primaryPhoneIndex]}
            isPrimary={true}
            isVerified={primaryPhoneVerified && true}
            otpVerificationEnabled={otpVerificationEnabled}
            handleVerificationClick={handleVerificationClick}
            handleVerify={handleVerify}
            sendOtpError={sendOtpError}
            isSendingOtp={isSendingOtp}
            isVerifying={isVerifying}
            texts={contactFieldTexts}
            CambianTooltip={CambianTooltip}
            Loader={Loader}
            InputProps={{
              inputComponent: MaskTextField,
              inputProps: {
                mask: '(###) ###-####',
                definitions: {
                  '#': /[0-9]/,
                },
              },
            }}
            preferredContactMethod={demographicFields?.preferredContactMethod}
          />
        </Grid>
      );
    }

    return (
      <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 1, pl: { xs: 1, sm: 3 }, pr: 1 }}>
        {phones.map((phone, phoneIndex) => (
          <ContactField
            key={`phone-${phoneIndex}`}
            contact={phones}
            index={phoneIndex}
            type="phone"
            label={item.display}
            required={item.isMandatory}
            allowMultiple={item.allowMultiple}
            value={phone.phoneNumber}
            onChange={({ value: updatedPhone, validationError, validationHelperText }) => {
              setShowValidationErrors(true);
              setPhones((prevPhones) => {
                const updatedPhones = [...prevPhones];
                updatedPhones[phoneIndex] = {
                  ...updatedPhones[phoneIndex],
                  phoneNumber: updatedPhone,
                };
                const validationMeta = updatedPhones.map((phone) => ({
                  error: phone.phoneNumber === updatedPhone ? validationError : false,
                  helperText: phone.phoneNumber === updatedPhone ? validationHelperText : '',
                }));
                handleContactChange(updatedPhones, 'phoneNumbers', item.isMandatory, validationMeta);
                return updatedPhones;
              });
            }}
            onSetPrimary={() => {
              const updatedPhones = phones.map((phone, i) => ({
                ...phone,
                primary: i === phoneIndex,
              }));
              setPhones(updatedPhones);
              handleContactChange(updatedPhones, 'phoneNumbers', item.isMandatory);
              const isThisPhoneVerified = verifiedValues.has(phones[phoneIndex].phoneNumber);
              onVerificationSuccess('phone', isThisPhoneVerified, phones[phoneIndex].phoneNumber);
            }}
            onDelete={() => {
              const updatedPhones = phones.filter((_, i) => i !== phoneIndex);
              if (!updatedPhones.some((phone) => phone.primary) && updatedPhones.length > 0) {
                updatedPhones[0].primary = true;
              }
              setPhones(updatedPhones);
              handleContactChange(updatedPhones, 'phoneNumbers', item.isMandatory);
            }}
            isReadOnly={isClientSummary}
            error={!!validationData?.contact?.phoneNumbers[phoneIndex]}
            helperText={validationData?.contact?.phoneNumbers[phoneIndex]}
            isPrimary={phone.primary}
            isVerified={phone.primary ? primaryPhoneVerified : verifiedValues.has(phone.phoneNumber)}
            otpVerificationEnabled={otpVerificationEnabled}
            handleVerificationClick={handleVerificationClick}
            handleVerify={handleVerify}
            sendOtpError={sendOtpError}
            isSendingOtp={isSendingOtp}
            isVerifying={isVerifying}
            texts={contactFieldTexts}
            CambianTooltip={CambianTooltip}
            Loader={Loader}
            InputProps={{
              inputComponent: MaskTextField,
              inputProps: {
                mask: '(###) ###-####',
                definitions: {
                  '#': /[0-9]/,
                },
              },
            }}
            preferredContactMethod={demographicFields?.preferredContactMethod}
          />
        ))}
        <Button
          variant="text"
          startIcon={<Add />}
          onClick={() => {
            const newPhone = { phoneNumber: '', primary: phones.length === 0 };
            setPhones((prevPhones) => [...prevPhones, newPhone]);
          }}
          sx={{
            p: '15px 1px',
            cursor: 'pointer',
            fontSize: '15px',
            '&:hover': { backgroundColor: 'transparent', color: '#23527c' },
          }}
        >
          {t('addPhone')}
        </Button>
      </Grid>
    );
  };

  const getNotification = (item) => {
    return (
      <Grid container>
        <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 2, pl: { xs: 1, sm: 3 }, pr: 1 }}>
          <FormControl fullWidth variant="outlined">
            <InputLabel size="small" required={item.isMandatory} error={!!validationData?.subscribeToNotifications}>
              {item.display}
            </InputLabel>
            <Select
              value={demographicFields.subscribeToNotifications ?? true}
              defaultValue={true}
              label={item.display}
              name="subscribeToNotifications"
              size="small"
              onChange={(e) => handleIndividualChange(e.target.value, 'subscribeToNotifications', item.isMandatory)}
              inputProps={{ readOnly: isClientSummary }}
              required={item.isMandatory}
              error={!!validationData?.subscribeToNotifications}
            >
              {allowNotificationsOptions.map((allowNotificationsOption, index) => (
                <MenuItem
                  key={index}
                  value={allowNotificationsOption.value}
                  disabled={allowNotificationsOption.disabled}
                >
                  {allowNotificationsOption.displayName}
                </MenuItem>
              ))}
            </Select>
            <FormHelperText error>{validationData?.subscribeToNotifications || ''}</FormHelperText>{' '}
          </FormControl>
        </Grid>
      </Grid>
    );
  };

  const findPreferredContactMethodValue = () => {
    if (!preferredContactMethod) {
      return '';
    }
    if (preferredContactMethod === 'Email') {
      return t('email');
    } else if (preferredContactMethod === 'Phone') {
      return t('phone');
    }
    const preferredMethod = preferredContactMethod.toLowerCase();
    for (const option of contactMethodOptions) {
      if (option.value.toLowerCase() === preferredMethod) {
        return option.value;
      }
    }
    if (preferredMethod.includes('email')) {
      const emailOption = contactMethodOptions.find((option) => option.id === 'EMAIL');
      if (emailOption) {
        return emailOption.value;
      }
    }
    if (preferredMethod.includes('phone')) {
      const phoneOption = contactMethodOptions.find((option) => option.id === 'PHONE');
      if (phoneOption) {
        return phoneOption.value;
      }
    }
    return preferredContactMethod;
  };

  const getPreferredContactMethod = (item) => {
    return (
      <Grid container>
        <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 2, pl: { xs: 1, sm: 3 }, pr: 1 }}>
          <FormControl fullWidth variant="outlined">
            <InputLabel size="small" error={!!validationData?.preferredContactMethod} required={item.isMandatory}>
              {t('preferredContactMethod')}
            </InputLabel>
            <Select
              label={t('preferredContactMethod')}
              value={findPreferredContactMethodValue()}
              onChange={(e) => {
                let internalValue = e.target.value;
                if (internalValue.toLowerCase() === t('email').toLowerCase()) {
                  internalValue = 'Email';
                } else if (internalValue.toLowerCase() === t('phone').toLowerCase()) {
                  internalValue = 'Phone';
                }
                handlePreferredContactMethodCallback(internalValue);
              }}
              name="preferredContactMethod"
              size="small"
              required={item.isMandatory}
              error={!!validationData?.preferredContactMethod}
            >
              {preferredContactMethod && (
                <MenuItem value="">
                  <em>{t('none')}</em>
                </MenuItem>
              )}
              {contactMethodOptions.map(
                (option) =>
                  fields.find((field) => field.code === option.id) && (
                    <MenuItem key={option.id} value={option.value} disabled={option.disabled}>
                      {option.value}
                    </MenuItem>
                  ),
              )}
            </Select>
            <FormHelperText error>{validationData?.preferredContactMethod}</FormHelperText>
          </FormControl>
        </Grid>
      </Grid>
    );
  };

  const getAddress = (item) => {
    return (
      <AddressFields
        demographicFields={demographicFields}
        item={item}
        addresses={addresses}
        setAddresses={setAddresses}
        validationData={validationData}
        isClientSummary={isClientSummary}
        handleContactChange={handleContactChange}
        setShowValidationErrors={setShowValidationErrors}
        t={t}
      />
    );
  };

  const handleContactChange = (value, fieldName, isRequired, validationMeta = []) => {
    demographicFields.contact[fieldName] = value;
    if (!validationData.contact) {
      validationData.contact = {};
    }
    if (Array.isArray(value)) {
      validationData.contact[fieldName] = value.map((_, idx) => {
        const meta = validationMeta[idx];
        return meta && meta.error ? meta.helperText : '';
      });
    } else {
      validationData.contact[fieldName] =
        validationMeta[0] && validationMeta[0].error ? validationMeta[0].helperText : '';
    }
    handleContactChangeCallback(demographicFields.contact, validationData);

    if (fieldName === 'emailAddresses') {
      const primaryEmail = value.find((email) => email.primary)?.emailAddress;

      if (primaryEmail) {
        if (verifiedValues.has(primaryEmail)) {
          onVerificationSuccess('email', true, primaryEmail);
        } else {
          onVerificationSuccess('email', false, primaryEmail);
        }
      }
    } else if (fieldName === 'phoneNumbers') {
      const primaryPhone = value.find((phone) => phone.primary)?.phoneNumber;
      if (primaryPhone) {
        if (verifiedValues.has(primaryPhone)) {
          onVerificationSuccess('phone', true, primaryPhone);
        } else {
          onVerificationSuccess('phone', false, primaryPhone);
        }
      }
    }

    if (fieldName.toUpperCase() === preferredContactMethod.toUpperCase() && value === '') {
      handlePreferredContactMethodCallback('');
    }

    setContactMethodOptions((prevContactMethod) => {
      const updatedContactOptions = [...prevContactMethod];
      return updatedContactOptions.map((contactMethod) => {
        if (contactMethod.id === 'EMAIL' && fieldName === 'emailAddresses' && value === '') {
          contactMethod.disabled = true;
        } else if (contactMethod.id === 'PHONE' && fieldName === 'phoneNumbers' && value === '') {
          contactMethod.disabled = true;
        } else if (contactMethod.id === 'EMAIL' && fieldName === 'emailAddresses' && value) {
          contactMethod.disabled = false;
        } else if (contactMethod.id === 'PHONE' && fieldName === 'phoneNumbers' && value) {
          contactMethod.disabled = false;
        } else if (
          (contact.phoneNumbers === '' && fieldName === 'emailAddresses') ||
          (contact.emailAddresses === '' && fieldName === 'phoneNumbers')
        ) {
          contactMethod.disabled = true;
        } else {
          contactMethod.disabled = false;
        }
        return contactMethod;
      });
    });
  };

  return (
    <Stack direction="column">
      <Grid item xs={6} sm={4} md={4} lg={4} sx={{ pl: 1, marginLeft: 2 }}>
        <Typography variant="h6">{t('contactInformation')}</Typography>
      </Grid>
      <Box>
        {!dataValidation.isDataEmpty(fields) &&
          fields.sort(sortFields).map((item, index) => {
            return (
              <Grid container key={index}>
                {item.code === 'PREFERRED_CONTACT_METHOD' && getPreferredContactMethod(item)}
                {item.code === 'PHONE' && getPhoneNumber(item)}
                {item.code === 'EMAIL' && getEmail(item)}
                {item.code === 'ADDRESS' && getAddress(item)}
                {item.code === 'NOTIFICATIONS' && getNotification(item)}
              </Grid>
            );
          })}
      </Box>
    </Stack>
  );
};
