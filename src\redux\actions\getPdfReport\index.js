/*
 * QUESTIONNAIRE_WIDGET_DETAILS action types
 */

export const GET_PDF_REQUEST = 'GET_PDF_REQUEST';
export const GET_PDF_SUCCESS = 'GET_PDF_SUCCESS';
export const GET_PDF_ERROR = 'GET_PDF_ERROR';
export const GET_PDF_RESET = 'GET_PDF_RESET';

/*
 * action creators
 */

export function getPdfInfo(data) {
  return {
    type: GET_PDF_REQUEST,
    payload: data,
  };
}

export function getPdfSuccess(data) {
  return {
    type: GET_PDF_SUCCESS,
    payload: data,
  };
}

export function getPdfError() {
  return {
    type: GET_PDF_ERROR,
  };
}

export const resetPdfInfo = () => {
  return {
    type: GET_PDF_RESET,
  };
};
