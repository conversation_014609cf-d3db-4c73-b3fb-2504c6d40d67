/*
 * ASSIGN_QUESTIONNAIRE action types
 */

export const SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_REQUEST = 'SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_REQUEST';
export const SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_SUCCESS = 'SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_SUCCESS';
export const SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_ERROR = 'SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_ERROR';
export const SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_RESET = 'SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_RESET';

/*
 * action creators
 */

export function saveQuestionnaireResponseForLater(data) {
  return {
    type: SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_REQUEST,
    payload: data,
  };
}

export function saveQuestionnaireResponseForLaterSuccess(data) {
  return {
    type: SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_SUCCESS,
    payload: data,
  };
}

export function saveQuestionnaireResponseForLaterError() {
  return {
    type: SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_ERROR,
  };
}

export function resetSaveQuestionnaireResponseForLater() {
  return {
    type: SAVE_QUESTIONNAIRE_RESPONSE_FOR_LATER_RESET,
  };
}
