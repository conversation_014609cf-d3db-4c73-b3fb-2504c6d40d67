/**
 * DEFAULT ERROR STATUS
 */
export const DEFAULT_ERROR_STATUS = 500;

/**
 * API expected reponse codes
 */
export const API_RESPONSE_SUCCESS = 200;
export const API_RESPONSE_201_SUCCESS = 201;
export const API_RESPONSE_204_SUCCESS = 204;
// export const API_RESPONSE_INTERNAL_SERVER_ERROR = 500;
export const API_RESPONSE_AUTHENTICATION_ERROR = 401;
export const API_RESPONSE_AUTHENTICATION_409_ERROR = 409;
export const API_RESPONSE_400_ERROR = 400;
/**
 * API Headers
 */
export const API_HEADER_AUTHORIZATION_KEY = 'Authorization';
export const API_HEADER_CAMIBIAN_AUTHORIZATION_KEY = 'Cambian-Authorization';
export const API_HEADER_AUTHORIZATION_ACCESS_TOKEN_KEY = 'access_token';
export const API_HEADER_AUTHORIZATION_VALUE = `Bearer ${API_HEADER_AUTHORIZATION_ACCESS_TOKEN_KEY}`;
export const API_HEADER_AUTHORIZATION_BASIC = `Basic ${API_HEADER_AUTHORIZATION_ACCESS_TOKEN_KEY}`;
export const API_HEADER_CONTENT_TYPE_KEY = 'Content-Type';
export const API_HEADER_CONTENT_TYPE_MULTIPART_FORM_DATA = 'multipart/form-data';
export const API_HEADER_CACHE_CONTROL_KEY = 'Cache-Control';
export const API_HEADER_NO_CACHE_VALUE = 'no-cache';
export const API_ACCEPT_LANGUAGE_KEY = 'Accept-Language';
export const API_HEADER_CONTENT_TYPE = 'Content-type';
export const API_HEADER_CONTENT_TYPE_JSON_VALUE = 'application/json';
export const API_HEADER_RNFB_RESPONSE = 'RNFB-Response';
export const API_HEADER_RNFB_RESPONSE_VALUE_UTF8 = 'utf8';
export const API_HEADER_ACCEPT_VERSION = 'accept-version';
export const API_HEADER_FORM_URL_ENCODED = 'application/x-www-form-urlencoded';
