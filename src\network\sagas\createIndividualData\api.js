import { CREATE_INDIVIDUAL_DATA } from '@/utils/constants/awsApiEndpoints';
import { makeNetworkCall } from '../..';
import { ORGANIZATION_ID } from '@/utils/constants';

export function createIndividualData(action) {
  const { payload = {} } = action || {};
  const { headers = {}, organizationId, individualData } = payload || {};

  const URL = CREATE_INDIVIDUAL_DATA.replace(ORGANIZATION_ID, organizationId);

  const config = {
    method: 'POST',
    url: URL,
    headers: headers,
    data: individualData,
  };

  return makeNetworkCall(config);
}
