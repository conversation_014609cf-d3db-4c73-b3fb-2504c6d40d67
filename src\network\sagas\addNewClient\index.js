/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import {
  ADD_NEW_CLIENT_REQUEST,
  ADD_NEW_CLIENT_SUCCESS,
  ADD_NEW_CLIENT_ERROR,
} from '../../../redux/actions/addNewClient';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiFetchAddNewPatient } from './api';
/**
 *
 * @param {*} action
 */
/**
 
/**
 *
 * @param {*} action
 */

export function* addNewPatient(action) {
  try {
    const response = yield call(makeApiFetchAddNewPatient, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: ADD_NEW_CLIENT_SUCCESS, payload: data });
    } else {
      yield put({ type: ADD_NEW_CLIENT_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: ADD_NEW_CLIENT_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchaddNewPatient() {
  yield takeEvery(ADD_NEW_CLIENT_REQUEST, addNewPatient);
}
