import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Marker, Map as <PERSON>bianMap, GoogleApiWrapper, InfoWindow } from 'google-maps-react';
import Autocomplete, { usePlacesWidget } from 'react-google-autocomplete';
import { Button, Stack, Grid, Typography, InputLabel, FormControl, TextField, Select, MenuItem } from '@mui/material';
import * as BookingUtility from '../../bookingUtility';
import { useTranslation } from 'react-i18next';
import { actions } from '@/containers/commonConstants';
import { LocationSearching } from '@mui/icons-material';
import { useSelector } from 'react-redux';
import useNotification from '@/hooks/useNotification';

const radiusOptions = [
  { label: '10Km', value: 10 },
  { label: '20Km', value: 20 },
  { label: '30Km', value: 30 },
  { label: '50Km', value: 50 },
];

function GoogleMap(props) {
  console.log('TRACE: GoogleMap');
  const {
    handleNavigationCallback,
    handleLocationDistanceUpdate,
    locations,
    locationDistanceMap,
    searchedPlace,
    google,
    radius,
    setRadius,
  } = props;
  const { t } = useTranslation();
  const [, sendNotification] = useNotification();

  const widgetDetailsSuccessData = useSelector((state) => state.widgetDetailsReducer.widgetDetailsSuccessData);
  const { locationRequired } = widgetDetailsSuccessData || {};

  const [locationMarkerList, setLocationMarkerList] = useState([]);
  const [bounds, setBounds] = useState(null);
  const [showingInfoWindow, setShowingInfoWindow] = useState(false);
  const [activeMarker, setActiveMarker] = useState({});
  const [selectedPlace, setSelectedPlace] = useState({});
  const [searchedPlaceAddress, setSearchedPlaceAddress] = useState(searchedPlace?.formatted_address);

  const mapRef = useRef(null);
  const radiusRef = useRef(radius);

  useEffect(() => {
    console.log('TRACE: GoogleMap.useEffect[]');
    setLocationMarkerList(getAllLocationMarkerData());
  }, []);

  useEffect(() => {
    if (mapRef.current && bounds) {
      mapRef?.current?.map?.fitBounds(bounds);
    }
  }, [bounds, searchedPlace]);

  const getAllLocationMarkerData = () => {
    let data = [];
    locations?.forEach((location) => {
      let modalData = {
        location: location,
        address: BookingUtility.getAddress(location?.resource ?? ''),
        name: location?.resource?.name ?? '',
        position: location?.resource?.position ?? '',
        distance: location?.resource?.distance ?? '',
        iconPath: 'https://maps.gstatic.com/mapfiles/ms2/micons/red-dot.png',
      };
      data.push(modalData);
    });
    return data;
  };

  const onMapClicked = () => {
    setActiveMarker(null);
    setShowingInfoWindow(false);
  };

  const handleMapLoad = () => {
    if (!bounds) {
      const bounds = new google.maps.LatLngBounds();
      locations?.forEach((location) => {
        bounds.extend({
          lat: location.resource.position.latitude,
          lng: location.resource.position.longitude,
        });
      });
      if (locations?.length == 1) {
        // Don't zoom in too far on only one marker
        bounds.extend({
          lat: locations[0].resource.position.latitude + 0.1,
          lng: locations[0].resource.position.longitude + 0.1,
        });
        bounds.extend({
          lat: locations[0].resource.position.latitude - 0.1,
          lng: locations[0].resource.position.longitude - 0.1,
        });
      }
      setBounds(bounds);
    }
  };

  const processPlaceSelection = (place) => {
    if (place.formatted_address) {
      let locationDistanceMap = new Map();
      const bounds = new google.maps.LatLngBounds();
      const searchedPlaceLatLng = getSearchedPlaceLatLng(place);
      bounds.extend(searchedPlaceLatLng);

      let boundCount = 1;
      locations?.forEach((location) => {
        const markerPlace = getLocationLatLng(location);

        const distance = (
          google.maps.geometry.spherical.computeDistanceBetween(searchedPlaceLatLng, markerPlace) / 1000
        ).toFixed(2);

        locationDistanceMap.set(location.resource.id, distance);
        if (distance < Number(radiusRef.current)) {
          bounds.extend(markerPlace);
          boundCount = boundCount + 1;
        }
      });

      if (boundCount == 1) {
        locations?.forEach((location) => {
          bounds.extend(getLocationLatLng(location));
          boundCount = boundCount + 1;
        });
      }

      setBounds(bounds);

      if (locationDistanceMap.size) {
        setSearchedPlaceAddress(place?.formatted_address);
        handleLocationDistanceUpdate(locationDistanceMap, place);
      }
    }
  };

  const getUserLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          /* Testing lat and lng,
            const { latitude, longitude } = { latitude: 49.104431, longitude: -122.801094 } // surrey
            const { latitude, longitude } = { latitude: 49.246292, longitude: -123.116226 } // Vancouver
          */
          const userLocation = new google.maps.LatLng(latitude, longitude);
          const place = {
            formatted_address: 'Current Location',
            geometry: {
              location: userLocation,
            },
          };

          processPlaceSelection(place);
        },
        (error) => {
          console.error('Error getting user location:', error);
          sendNotification({ variant: 'error', msg: t('errorGettingUserLocation') });
        },
      );
    } else {
      sendNotification({ variant: 'error', msg: t('pleaseAllowUsLocationPermission') });
    }
  };

  const onMarkerClick = (props, marker) => {
    setActiveMarker(marker);
    setSelectedPlace(props);
    setShowingInfoWindow(true);
  };

  const onInfoWindowClose = () => {
    setActiveMarker(null);
    setShowingInfoWindow(false);
  };

  const onSelectLocation = () => {
    handleNavigationCallback(actions.NEXT, selectedPlace.location);
  };

  const onPlaceSelected = (place) => {
    onMapClicked();
    if (place.formatted_address) {
      setSearchedPlaceAddress(place.formatted_address);

      let locationDistanceMap = new Map();
      const bounds = new google.maps.LatLngBounds();
      let boundCount = 1;
      locations?.forEach((location) => {
        const markerPlace = getLocationLatLng(location);
        const searchedPlaceLatLng = getSearchedPlaceLatLng(place);
        const distance = (
          google.maps.geometry.spherical.computeDistanceBetween(searchedPlaceLatLng, markerPlace) / 1000
        ).toFixed(2);
        locationDistanceMap.set(location.resource.id, distance);
        if (Number(distance) < Number(radiusRef.current)) {
          bounds.extend(getLocationLatLng(location));
          boundCount = boundCount + 1;
        }
      });

      bounds.extend(getSearchedPlaceLatLng(place));

      if (boundCount == 1) {
        locations?.forEach((location) => {
          bounds.extend(getLocationLatLng(location));
          boundCount = boundCount + 1;
        });
      }

      setBounds(bounds);
      if (locationDistanceMap.size) {
        handleLocationDistanceUpdate(locationDistanceMap, place);
      }
    }
  };

  const getSearchedPlaceLatLng = (place) => {
    return new google.maps.LatLng(
      typeof place.geometry.location.lat === 'function' ? place.geometry.location.lat() : place.geometry.location.lat,
      typeof place.geometry.location.lng === 'function' ? place.geometry.location.lng() : place.geometry.location.lng,
    );
  };

  const getLocationLatLng = (location) => {
    return new google.maps.LatLng(location.resource.position.latitude, location.resource.position.longitude);
  };

  const defaultAutocompleteAddress = useMemo(
    () => (searchedPlace?.formatted_address ? searchedPlace.formatted_address : ''),
    [searchedPlace],
  );

  const { ref: materialRef } = usePlacesWidget({
    onPlaceSelected: onPlaceSelected,
    defaultValue: defaultAutocompleteAddress,
    options: {
      fields: ['formatted_address', 'geometry'],
      types: [],
    },
  });

  return (
    <Stack spacing={2}>
      <Typography variant="h6">{t('Book an Appointment at a location near you')}</Typography>
      <Grid container columnGap={{ xs: 0.5, lg: 0.5 }} justifyContent="space-between">
        <Grid item xs={locationRequired ? 7.5 : 12} lg={locationRequired ? 8.5 : 12}>
          <TextField
            fullWidth
            size="small"
            variant="outlined"
            inputRef={materialRef}
            value={searchedPlaceAddress}
            onChange={(e) => {
              setSearchedPlaceAddress(e.target.value);
            }}
            placeholder={t('Search by address, city or postal code')}
          />
          {locationRequired && (
            <Button
              variant="link"
              startIcon={<LocationSearching />}
              sx={{ '&:hover': { backgroundColor: 'transparent' } }}
              onClick={getUserLocation}
            >
              {t('Use my location')}
            </Button>
          )}
        </Grid>
        {locationRequired && (
          <Grid item xs={4} lg={3}>
            <FormControl fullWidth>
              <InputLabel>{t('Radius')}</InputLabel>
              <Select
                label={t('Radius')}
                size="small"
                value={radius}
                onChange={(event) => {
                  setRadius(event.target.value);
                  radiusRef.current = event.target.value;
                }}
              >
                {radiusOptions.map((radius) => (
                  <MenuItem key={radius.value} value={radius.value}>
                    {radius.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        )}
      </Grid>
      {(locationRequired === false || locationDistanceMap?.size) && (
        <CambianMap
          google={google}
          containerStyle={{
            position: 'relative',
            width: '100%',
            height: '100%',
          }}
          style={{
            width: '100%',
            height: '500px',
            position: 'relative',
            margin: 'auto',
          }}
          bounds={bounds}
          onClick={onMapClicked}
          onReady={handleMapLoad}
          ref={mapRef}
        >
          {locationMarkerList?.map((pin, index) => {
            return (
              <Marker
                key={`str_${index + 1}`}
                title={pin.name}
                address={pin.address}
                //distance={getLocationDistance(pin.location)}
                location={pin.location}
                onClick={onMarkerClick}
                icon={{
                  url: `${pin.iconPath}`,
                }}
                position={{
                  lat: pin?.position?.latitude,
                  lng: pin?.position?.longitude,
                }}
              />
            );
          })}

          {searchedPlace && searchedPlace.geometry && (
            <Marker
              title={searchedPlace.formatted_address}
              icon={{
                url: 'https://maps.gstatic.com/mapfiles/ms2/micons/blue-dot.png',
              }}
              position={getSearchedPlaceLatLng(searchedPlace)}
            />
          )}

          <InfoWindow marker={activeMarker} onClose={onInfoWindowClose} visible={showingInfoWindow}>
            <Grid container>
              <Grid item xs={12}>
                <Stack direction="column" justifyContent="flex-start" alignItems="left">
                  <Typography variant="h6" sx={{ color: '#4D76A9', fontSize: 18 }}>
                    {selectedPlace.title}
                  </Typography>
                  <Stack direction="row">
                    <Typography variant="body" sx={{ color: '#333333', fontSize: 16, fontWeight: 'regular' }}>
                      {selectedPlace.address}
                    </Typography>
                  </Stack>
                  {selectedPlace?.location &&
                    BookingUtility.getLocationDistance(locationDistanceMap, selectedPlace.location) && (
                      <Typography variant="body" sx={{ color: '#4D76A9', fontSize: 16, fontWeight: 'regular', mt: 1 }}>
                        {BookingUtility.getLocationDistance(locationDistanceMap, selectedPlace.location)} km
                      </Typography>
                    )}
                </Stack>
                <Button
                  variant="contained"
                  onClick={onSelectLocation}
                  id="change"
                  sx={{ color: '#4D76A9', mt: 1, textTransform: 'none' }}
                >
                  {t('bookingAppointment')}
                </Button>
              </Grid>
            </Grid>
          </InfoWindow>
        </CambianMap>
      )}
    </Stack>
  );
}

export default GoogleApiWrapper({
  apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY,
  libraries: ['places', 'geometry'],
})(GoogleMap);
