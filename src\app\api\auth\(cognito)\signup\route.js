import { handler, signUp } from '@/containers/auth';
import { setCookiesForUnverifiedUser } from '@/containers/auth/utility';
import { NextResponse } from 'next/server';

/**
 * @param {Request} req
 * @returns {Promise<void>}
 */
const postEndpoint = async (req) => {
  const { email, password, firstName, lastName, cambianId } = await req.json();

  const [signUpResult, signUpError] = await signUp({ email, password, firstName, lastName, cambianId });

  if (signUpError) {
    return signUpError.toNextResponse();
  }

  setCookiesForUnverifiedUser({ email, firstName, lastName });
  return NextResponse.json({}, { status: signUpResult.status });
};

const middleware1 = (_req, next) => {
  console.log('middleware 1');
  next();
};

export const POST = handler(middleware1, postEndpoint);
