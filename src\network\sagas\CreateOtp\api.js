/*
 *
 */
import { makeNetworkCall } from '../..';
import { CRETAE_OTP_URL, BASE_URL, CAMBIAN_SERVICE_BASE_URL } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiCreateOtp(action) {
  const { payload = {} } = action || {};
  const { data = {}, headers = {} } = payload || {};
  const mainUrl = CAMBIAN_SERVICE_BASE_URL + CRETAE_OTP_URL;
  const config = {
    method: 'POST',
    url: mainUrl,
    headers: headers,
    data: data,
  };
  return makeNetworkCall(config);
}
