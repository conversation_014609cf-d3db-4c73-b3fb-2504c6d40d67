/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import {
  SEARCH_AND_CREATE_PATIENT_ERROR,
  SEARCH_AND_CREATE_PATIENT_REQUEST,
  SEARCH_AND_CREATE_PATIENT_SUCCESS,
  SEARCH_AND_CREATE_PATIENT_RESET,
} from '../../actions/searchAndCreatePatient';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const searchAndCreatePatientReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case SEARCH_AND_CREATE_PATIENT_REQUEST:
      return {
        ...state,
        searchAndCreatePatientFetching: true,
        searchAndCreatePatientSuccess: false,
        searchAndCreatePatientError: false,
        searchAndCreatePatientErrorData: null,
      };
    case SEARCH_AND_CREATE_PATIENT_SUCCESS: {
      return {
        ...state,
        searchAndCreatePatientFetching: false,
        searchAndCreatePatientSuccess: true,
        searchAndCreatePatientError: false,
        searchAndCreatePatientErrorData: null,
        searchAndCreatePatientSuccessData: payload,
      };
    }
    case SEARCH_AND_CREATE_PATIENT_ERROR:
      return {
        ...state,
        searchAndCreatePatientFetching: false,
        searchAndCreatePatientSuccess: false,
        searchAndCreatePatientError: true,
        searchAndCreatePatientErrorData: payload,
      };
    case SEARCH_AND_CREATE_PATIENT_RESET:
      return {
        ...state,
        searchAndCreatePatientFetching: false,
        searchAndCreatePatientSuccess: false,
        searchAndCreatePatientError: false,
        searchAndCreatePatientErrorData: null,
        searchAndCreatePatientSuccessData: null,
      };
    default:
      return state;
  }
};
