import {
  POST_QUESTIONNAIRE_QUESTIONS_REQUEST,
  POST_QUESTIONNAIRE_QUESTIONS_SUCCESS,
} from '../../actions/SubmitQuestionnaire';
import * as GLOBALS from '../globals';
import { submitQuestionnaireAnswersReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isQuestionnaireSubmitAnswersFetching: true,
  isQuestionnaireSubmitAnswersSuccess: false,
  isQuestionnaireSubmitAnswersError: false,
  questionnaireSubmitAnswersErrorData: null,
};

const successState = {
  isQuestionnaireSubmitAnswersFetching: false,
  isQuestionnaireSubmitAnswersSuccess: true,
  isQuestionnaireSubmitAnswersError: false,
  questionnaireSubmitAnswersErrorData: null,
  questionnaireSubmitAnswersSuccessData: data,
};

describe('Submit Question Reducer', () => {
  it('should return the initial state', () => {
    expect(submitQuestionnaireAnswersReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle POST_QUESTIONNAIRE_QUESTIONS_REQUEST', () => {
    expect(
      submitQuestionnaireAnswersReducer(initialState, {
        type: POST_QUESTIONNAIRE_QUESTIONS_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle POST_QUESTIONNAIRE_QUESTIONS_SUCCESS', () => {
    expect(
      submitQuestionnaireAnswersReducer(initialState, {
        type: POST_QUESTIONNAIRE_QUESTIONS_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });
});
