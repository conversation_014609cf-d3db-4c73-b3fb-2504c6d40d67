import { GET_BOOKING_WIDGET } from '@/utils/constants/awsApiEndpoints';
import { makeNetworkCall } from '../..';
import { ORGANIZATION_ID, WIDGET_ID } from '../../../utils/constants';

export function makeApiFetchWidgetDetails(action) {
  const { payload = {} } = action || {};
  const { organizationId, widgetId, headers, lang } = payload || {};

  let URL = GET_BOOKING_WIDGET.replace(ORGANIZATION_ID, organizationId).replace(WIDGET_ID, widgetId);

  // Add language parameter if specified
  if (lang) {
    URL = `${URL}?lang=${lang}`;
  }

  const config = {
    method: 'GET',
    url: URL,
    headers,
    formData: false,
  };

  return makeNetworkCall(config);
}
