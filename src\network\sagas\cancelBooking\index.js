/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';

import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  CANCEL_BOOKING_ERROR,
  CANCEL_BOOKING_SUCCESS,
  CANCEL_BOOKING_REQUEST,
} from '../../../redux/actions/cancelBooking';

import { cancelBookingApi } from './api';

/**
 *
 * @param {*} action
 */
export function* cancelBooking(action) {
  try {
    const response = yield call(cancelBookingApi, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: CANC<PERSON>_BOOKING_SUCCESS, payload: data });
    } else {
      yield put({ type: CANCEL_BOOKING_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: CANCEL_BOOKING_ERROR });
  }
}

// Our watcher Saga:
export function* watchCancelBooking() {
  yield takeEvery(CANCEL_BOOKING_REQUEST, cancelBooking);
}
