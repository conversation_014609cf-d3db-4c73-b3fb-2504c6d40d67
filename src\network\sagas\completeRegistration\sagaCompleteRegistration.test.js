import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/completeRegistration/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { completeRegistration } from './index';

/**
 * This function test test case for get completeRegistration details saga
 * Fires get completeRegistration success of api gives success
 * Fires get completeRegistration error of api fails
 */

describe('completeRegistration', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeCompleteRegistration = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.completeRegistration(DUMMY_ITEM.data);
    await runSaga(fakeStore, completeRegistration, requestResult).done;

    let successResult = actions.completePatientRegistrationSuccess(DUMMY_ITEM.data);

    expect(api.makeCompleteRegistration.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeCompleteRegistration = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.completeRegistration(DUMMY_ITEM.data);
    await runSaga(fakeStore, completeRegistration, requestResult).done;

    expect(api.makeCompleteRegistration.mock.calls.length).toBe(1);

    let errorResult = actions.completePatientRegistrationError();
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch reset action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeCompleteRegistration = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.completeRegistration(DUMMY_ITEM.data);
    await runSaga(fakeStore, completeRegistration, requestResult).done;

    expect(api.makeCompleteRegistration.mock.calls.length).toBe(1);

    let resetResult = actions.completeRegistrationReset();
    const expectedAction = {
      type: actions.COMPLETE_REGISTRATION_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
