import { GET_INDIVIDUAL_DATA } from '@/utils/constants/awsApiEndpoints';
import { makeNetworkCall } from '../..';
import { INDIVIDUAL } from '@/utils/constants';

export function getIndividualDataApi(action) {
  const { payload = {} } = action || {};
  const { headers = {}, individualId } = payload || {};

  const URL = GET_INDIVIDUAL_DATA + '/' + individualId; //.replace(ORGANIZATION_ID, organizationId) + userId;

  const config = {
    targetAwsEnv: INDIVIDUAL,
    method: 'GET',
    url: URL,
    headers: headers,
  };

  return makeNetworkCall(config);
}
