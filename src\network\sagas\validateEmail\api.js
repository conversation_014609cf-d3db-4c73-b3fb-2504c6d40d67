/*
 *
 */

import { makeNetworkCall } from '../..';
import { VALIDATE_EMAIL, BASE_URL, CAMBIAN_SERVICE_BASE_URL } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiValidateEmail(action) {
  const { payload = {} } = action || {};
  const { headers = {}, validateEmailData = {} } = payload || {};
  const config = {
    method: 'PUT',
    url: CAMBIAN_SERVICE_BASE_URL + VALIDATE_EMAIL,
    headers: headers,
    data: validateEmailData,
  };
  return makeNetworkCall(config);
}
