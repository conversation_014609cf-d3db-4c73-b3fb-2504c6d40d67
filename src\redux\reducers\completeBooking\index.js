/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import {
  COMPLETE_BOOKING_ERROR,
  COMPLETE_BOOKING_REQUEST,
  COMPLETE_BOOKING_SUCCESS,
  COMPLETE_BOOKING_RESET,
} from '../../actions/completeBooking';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const completeBookingReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case COMPLETE_BOOKING_REQUEST:
      return {
        ...state,
        completeBookingFetching: true,
        completeBookingSuccess: false,
        completeBookingError: false,
        completeBookingErrorData: null,
      };
    case COMPLETE_BOOKING_SUCCESS: {
      return {
        ...state,
        completeBookingFetching: false,
        completeBookingSuccess: true,
        completeBookingError: false,
        completeBookingErrorData: null,
        completeBookingSuccessData: payload,
      };
    }
    case COMPLETE_BOOKING_ERROR:
      return {
        ...state,
        completeBookingFetching: false,
        completeBookingSuccess: false,
        completeBookingError: true,
        completeBookingErrorData: payload,
      };
    case COMPLETE_BOOKING_RESET:
      return {
        ...state,
        completeBookingFetching: false,
        completeBookingSuccess: false,
        completeBookingError: false,
        completeBookingErrorData: null,
        completeBookingSuccessData: null,
      };
    default:
      return state;
  }
};
