/*
 *
 */

import { makeNetworkCall } from '../..';
import { DELETE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_URL, BASE_URL } from '../../../utils/constants/apiEndpoints';

import { ORGANIZATION_ID, QUESTIONNAIRE_ID, QUESTIONNAIRE_RESPONSE_ID } from '../../../utils/constants';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiFetchDeleteAutoSaveResponse(action) {
  const { payload = {} } = action || {};
  const {
    headers = {},
    questionnaireResponseData,
    questionnaireResponseId,
    organizationId,
    questionnaireId,
  } = payload || {};
  // let dataObj = clean(patientData);
  // let newObj = Object.values(dataObj);
  // patientData.item = newObj;
  const mainUrl =
    BASE_URL +
    DELETE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_URL.replace(QUESTIONNAIRE_ID, questionnaireId)
      .replace(QUESTIONNAIRE_RESPONSE_ID, questionnaireResponseId)
      .replace(ORGANIZATION_ID, organizationId);
  const config = {
    method: 'DELETE',
    url: mainUrl,
    headers: headers,
    data: questionnaireResponseData,
  };
  return makeNetworkCall(config);
}
