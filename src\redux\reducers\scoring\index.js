/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import {
  SCORING_QUESTIONNAIRE_REQUEST,
  SCORING_QUESTIONNAIRE_SUCCESS,
  SCORING_QUESTIONNAIRE_ERROR,
  SCORING_QUESTIONNAIRE_RESET,
} from '../../actions/scoring';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const scoringQuestionnaireReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;

  switch (type) {
    case SCORING_QUESTIONNAIRE_REQUEST:
      return {
        ...state,
        scoringFetching: true,
        scoringQuestionnaireSuccess: false,
        scoringQuestionnaireError: false,
        scoringQuestionnaireErrorData: null,
      };
    case SCORING_QUESTIONNAIRE_SUCCESS: {
      return {
        ...state,
        scoringFetching: false,
        scoringQuestionnaireSuccess: true,
        scoringQuestionnaireError: false,
        scoringQuestionnaireErrorData: null,
        scoringQuestionnaireSuccessData: payload,
      };
    }
    case SCORING_QUESTIONNAIRE_ERROR:
      return {
        ...state,
        scoringFetching: false,
        scoringQuestionnaireSuccess: false,
        scoringQuestionnaireError: true,
        scoringQuestionnaireErrorData: payload,
      };
    case SCORING_QUESTIONNAIRE_RESET:
      return {
        ...state,
        scoringFetching: false,
        scoringQuestionnaireSuccess: false,
        scoringQuestionnaireError: false,
        scoringQuestionnaireErrorData: null,
        scoringQuestionnaireSuccessData: null,
      };
    default:
      return state;
  }
};
