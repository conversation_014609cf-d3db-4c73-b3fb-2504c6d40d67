import {
  GET_QUESTIONNAIRE_REQUEST,
  GET_QUESTIONNAIRE_SUCCESS,
  GET_QUESTIONNAIRE_ERROR,
} from '../../actions/getQuestionnaireDetails/index';
import * as GLOBALS from '../globals';
import { getQuestionnaireQuestionRedux } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isQuestionnaireQuestionsFetching: true,
  isQuestionnaireQuestionsSuccess: false,
  isQuestionnaireQuestionsError: false,
  questionnaireQuestionsErrorData: null,
};

const successState = {
  isQuestionnaireQuestionsFetching: false,
  isQuestionnaireQuestionsSuccess: true,
  isQuestionnaireQuestionsError: false,
  questionnaireQuestionsErrorData: null,
  questionnaireQuestionsSuccessData: data,
};

const errorState = {
  isQuestionnaireQuestionsFetching: false,
  isQuestionnaireQuestionsSuccess: false,
  isQuestionnaireQuestionsError: true,
  questionnaireQuestionsErrorData: data,
};

describe('Complete Booking Details Reducer', () => {
  it('should return the initial state', () => {
    expect(getQuestionnaireQuestionRedux(undefined, {})).toEqual(initialState);
  });

  it('should handle GET_QUESTIONNAIRE_REQUEST', () => {
    expect(
      getQuestionnaireQuestionRedux(initialState, {
        type: GET_QUESTIONNAIRE_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle GET_QUESTIONNAIRE_SUCCESS', () => {
    expect(
      getQuestionnaireQuestionRedux(initialState, {
        type: GET_QUESTIONNAIRE_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle GET_QUESTIONNAIRE_ERROR', () => {
    expect(
      getQuestionnaireQuestionRedux(initialState, {
        type: GET_QUESTIONNAIRE_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });
});
