/*
 *GET_BOOKING_DETAILS action types
 */

export const GET_BOOKING_REQUEST = 'GET_BOOKING_REQUEST';
export const GET_BOOKING_SUCCESS = 'GET_BOOKING_SUCCESS';
export const GET_BOOKING_ERROR = 'GET_BOOKING_ERROR';
export const GET_BOOKING_RESET = 'GET_BOOKING_RESET';

/*
 * action creators
 */

export function getBookedAppointmentDetails(data) {
  return {
    type: GET_BOOKING_REQUEST,
    payload: data,
  };
}

export function getBookedAppointmentSuccess(data) {
  return {
    type: GET_BOOKING_SUCCESS,
    payload: data,
  };
}

export function getBookedAppointmentError(data) {
  return {
    type: GET_BOOKING_ERROR,
    payload: data,
  };
}

export function getBookedAppointmentReset() {
  return {
    type: GET_BOOKING_RESET,
  };
}
