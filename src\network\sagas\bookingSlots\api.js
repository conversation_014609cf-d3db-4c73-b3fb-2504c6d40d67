import { makeNetworkCall } from '../..';
import { LOCATION_ID, ORGANIZATION_ID } from '../../../utils/constants';
import { GET_TIME_SLOTS } from '@/utils/constants/awsApiEndpoints';

export function makeApiFetchBookingSlots(action) {
  const { payload = {} } = action || {};
  const {
    headers = {},
    slotData,
    locationId,
    organizationId,
    // appointmentType,
    // selectedDate,
    // individualsCount,
  } = payload || {};

  /* const mainUrl =
    selectedDate === undefined
      ? BASE_URL +
        GET_BOOKING_SLOTS.replace(LOCATION_ID, locationId)
          .replace(APPOINTMENT_TYPE, appointmentType)
          .replace(ORGANIZATION_ID, organizationId)
          .replace(INDIVIDUALS_COUNT, individualsCount)
      : CAMBIAN_API_BASE_URL.replace(REGISTRY, CAMBIAN_WIDGET_APIS_REGISTRY) +
        GET_TIME_SLOTS.replace(LOCATION_ID, locationId).replace(ORGANIZATION_ID, organizationId); */

  const URL = GET_TIME_SLOTS.replace(LOCATION_ID, locationId).replace(ORGANIZATION_ID, organizationId);

  const config = {
    method: 'POST',
    url: URL,
    headers: headers,
    data: slotData,
  };

  return makeNetworkCall(config);
}
