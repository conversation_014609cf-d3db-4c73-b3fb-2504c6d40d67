import { GET_BOOKING_REQUEST, GET_BOOKING_SUCCESS, GET_BOOKING_ERROR } from '../../actions/getBookedAppointmentDetails';
import * as GLOBALS from '../globals';
import { getBookedAppointmentReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  getBookedAppointmentFetching: true,
  getBookedAppointmentSuccess: false,
  getBookedAppointmentError: false,
  getBookedAppointmentErrorData: null,
};

const errorState = {
  getBookedAppointmentFetching: false,
  getBookedAppointmentSuccess: false,
  getBookedAppointmentError: true,
  getBookedAppointmentErrorData: data,
};
const successState = {
  getBookedAppointmentFetching: false,
  getBookedAppointmentSuccess: true,
  getBookedAppointmentError: false,
  getBookedAppointmentErrorData: null,
  getBookedAppointmentSuccessData: data,
};

describe('Complete Booked Details Reducer', () => {
  it('should return the initial state', () => {
    expect(getBookedAppointmentReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle GET_BOOKING_REQUEST', () => {
    expect(
      getBookedAppointmentReducer(initialState, {
        type: GET_BOOKING_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle GET_BOOKING_SUCCESS', () => {
    expect(
      getBookedAppointmentReducer(initialState, {
        type: GET_BOOKING_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle GET_BOOKING_ERROR', () => {
    expect(
      getBookedAppointmentReducer(initialState, {
        type: GET_BOOKING_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });
});
