/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import {
  WIDGET_REGISTRATION_DETAILS_REQUEST,
  WIDGET_REGISTRATION_DETAILS_SUCCESS,
  WIDGET_REGISTRATION_DETAILS_ERROR,
} from '../../../redux/actions/widgetRegistrationDetails';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiFetchWidgetRegistrationDetails } from './api';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function* fetchWidgetRegistrationDetails(action) {
  try {
    const response = yield call(makeApiFetchWidgetRegistrationDetails, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: WIDGET_REGISTRATION_DETAILS_SUCCESS, payload: data });
    } else {
      yield put({ type: WIDGET_REGISTRATION_DETAILS_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: WIDGET_REGISTRATION_DETAILS_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchWidgetRegistrationDetails() {
  yield takeEvery(WIDGET_REGISTRATION_DETAILS_REQUEST, fetchWidgetRegistrationDetails);
}
