import {
  CHANGE_STATUS_ERROR,
  CHANGE_STATUS_REQUEST,
  CHANGE_STATUS_RESET,
  CHANGE_STATUS_SUCCESS,
} from '../../actions/changeStatus';
import * as GLOBALS from '../globals';
import { changeStatusReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isChangeStatusFetching: true,
  changeStatusSuccess: false,
  changeStatusError: false,
  changeStatusErrorData: null,
};

const successState = {
  isChangeStatusFetching: false,
  changeStatusSuccess: true,
  changeStatusError: false,
  changeStatusErrorData: null,
  changeStatusSuccessData: data,
};

const errorState = {
  isChangeStatusFetching: false,
  changeStatusSuccess: false,
  changeStatusError: true,
  changeStatusErrorData: data,
};

const resetState = {
  isChangeStatusFetching: false,
  changeStatusSuccess: false,
  changeStatusError: false,
  changeStatusErrorData: null,
};

describe('Reschedule Reducer', () => {
  it('should return the initial state', () => {
    expect(changeStatusReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle CHANGE_STATUS_REQUEST', () => {
    expect(
      changeStatusReducer(initialState, {
        type: CHANGE_STATUS_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle CHANGE_STATUS_SUCCESS', () => {
    expect(
      changeStatusReducer(initialState, {
        type: CHANGE_STATUS_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle CHANGE_STATUS_ERROR', () => {
    expect(
      changeStatusReducer(initialState, {
        type: CHANGE_STATUS_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle CHANGE_STATUS_RESET', () => {
    expect(
      changeStatusReducer(initialState, {
        type: CHANGE_STATUS_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
