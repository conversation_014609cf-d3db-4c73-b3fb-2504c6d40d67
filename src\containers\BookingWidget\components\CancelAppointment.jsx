import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Button, Grid, Typography, Box, Stack, Paper } from '@mui/material';
import { HeaderSummary } from '../HeaderSummary';
import * as CommonUtility from '../../commonUtility';
import useNotification from '@/hooks/useNotification';
import { cancelBooking } from '@/redux/actions/cancelBooking';
import { Loader } from '@/components';
import { useTranslation } from 'react-i18next';
import * as BookingUtility from '../bookingUtility';

export const CancelAppointment = (props) => {
  const {
    headerSummary,
    handleNavigationCallback,
    bookNewAppointmentCallback,
    data,
    redirectBackToWidgetId,
    appointmentId,
    organizationId,
  } = props;

  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [msg, sendNotification] = useNotification();
  const [isFreshRecordFetched, setIsFreshRecordFetched] = useState(false);
  const [heading, setHeading] = useState(t('cancelMainHeading'));
  const [subHeading, setSubHeading] = useState(t('cancelSubHeading'));
  const [isCancelSummaryPage, setIsCancelSummaryPage] = useState(false);

  const {
    cancelBookingFetching,
    cancelBookingSuccess,
    cancelBookingSuccessData,
    cancelBookingError,
    cancelBookingErrorData,
  } = useSelector((state) => state.cancelBookingReducer);

  const getBookedAppointmentSuccessData = useSelector(
    (state) => state.getBookedAppointmentReducer.getBookedAppointmentSuccessData,
  );

  const { entry: bookedAppointmentData } = getBookedAppointmentSuccessData || {};
  const appointmentStatus = BookingUtility.getAppointmentStatus(bookedAppointmentData);

  const handleCancelBookingAppointment = () => {
    dispatch(cancelBooking({ appointmentId, cancelData: data, organizationId }));
  };

  useEffect(() => {
    if (isFreshRecordFetched && cancelBookingSuccess && cancelBookingSuccessData) {
      setHeading(t('cancelSummaryHeading'));
      setSubHeading(t('cancelSummarySubHeading'));
      setIsCancelSummaryPage(true);
    }
  }, [cancelBookingSuccess, cancelBookingSuccessData]);

  useEffect(() => {
    if (isFreshRecordFetched && cancelBookingError && cancelBookingErrorData) {
      sendNotification({ msg: t('apiError'), variant: 'error' });
    }
  }, [cancelBookingError, cancelBookingErrorData]);

  useEffect(() => {
    if (cancelBookingFetching) {
      setIsFreshRecordFetched(true);
    }
  }, [cancelBookingFetching]);

  const getPage = () => {
    if (appointmentStatus === 'cancelled') {
      return cancelPage();
    } else {
      return cancelBookingPage();
    }
  };

  const cancelPage = () => {
    return (
      <Grid sx={{ mt: 5, mx: '2%', px: { xs: 2, md: 4 } }}>
        <Stack
          direction="column"
          alignItems="center"
          justifyContent="center"
          spacing={2}
          sx={{ pt: 0, px: 2, pb: 2, mt: 2 }}
        >
          <Typography variant={'h6'}>{t('appointmentCancelled')}</Typography>
          <Typography variant={'body'}>{t('appointmentCancelledText')}</Typography>
          <Button variant="contained" onClick={bookNewAppointmentCallback}>
            {t('bookNewAppointment')}
          </Button>
        </Stack>
      </Grid>
    );
  };

  const openUrlInSameTab = (URL) => {
    setTimeout(() => {
      let actionUrl = URL.includes('http') ? URL : `https://${URL}`;
      if (URL) {
        window.open(actionUrl, '_self', 'status=yes');
      }
    }, 2000);
  };

  const handleBookAgain = () => {
    if (redirectBackToWidgetId) {
      //redirect to questionnaire
      const [organizationId] = CommonUtility.getOrganizationAndWidgetId();
      const URL = `${window.location.origin}/widget/organizations/${organizationId}/questionnaireWidget/${redirectBackToWidgetId}`;
      openUrlInSameTab(URL);
    } else {
      bookNewAppointmentCallback();
    }
  };

  const cancelBookingPage = () => {
    return (
      <Grid sx={{ mt: 2, mx: '2%', px: { xs: 2, md: 4 } }}>
        <Box sx={{ my: 2 }}>
          <Typography variant="h5">{heading}</Typography>
        </Box>
        <Typography variant="body">{subHeading}</Typography>

        <Paper sx={{ px: 2, my: 2 }}>
          <HeaderSummary headerSummary={headerSummary} />
        </Paper>

        <Stack
          direction="row"
          alignItems="center"
          justifyContent="center"
          spacing={2}
          sx={{ pt: 0, px: 2, pb: 2, mt: 3 }}
        >
          {isCancelSummaryPage ? (
            <Button variant="contained" onClick={() => handleBookAgain()}>
              {t('bookAgain')}
            </Button>
          ) : (
            <>
              <Button variant="contained" onClick={handleCancelBookingAppointment}>
                {t('cancelAppointment')}
              </Button>
              <Button variant="outlined" onClick={() => handleNavigationCallback()}>
                {t('backToAppointmentSummary')}
              </Button>
            </>
          )}
        </Stack>
      </Grid>
    );
  };

  return (
    <>
      <Loader active={cancelBookingFetching} />
      {getPage()}
    </>
  );
};
