import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  FORGOT_PASSWORD_REQUEST,
  FORGOT_PASSWORD_SUCCESS,
  FORGOT_PASSWORD_ERROR,
  FORGOT_PASSWORD_RESET,
} from '../../actions/forgotPassword';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const forgotPasswordReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case FORGOT_PASSWORD_REQUEST:
      return {
        ...state,
        forgotPasswordFetching: true,
        forgotPasswordSuccess: false,
        forgotPasswordError: false,
        forgotPasswordErrorData: null,
      };
    case FORGOT_PASSWORD_SUCCESS: {
      return {
        ...state,
        forgotPasswordFetching: false,
        forgotPasswordSuccess: true,
        forgotPasswordError: false,
        forgotPasswordErrorData: null,
        forgotPasswordSuccessData: payload,
      };
    }
    case FORGOT_PASSWORD_ERROR:
      return {
        ...state,
        forgotPasswordFetching: false,
        forgotPasswordSuccess: false,
        forgotPasswordError: true,
        forgotPasswordErrorData: payload,
      };
    case FORGOT_PASSWORD_RESET:
      return {
        ...state,
        forgotPasswordFetching: false,
        forgotPasswordSuccess: false,
        forgotPasswordError: false,
        forgotPasswordErrorData: null,
        forgotPasswordSuccessData: null,
      };
    default:
      return state;
  }
};
