/*
 * Get locations action types
 */

export const GET_LOCATION_REQUEST = 'GET_LOCATION_REQUEST';
export const GET_LOCATION_SUCCESS = 'GET_LOCATION_SUCCESS';
export const GET_LOCATION_ERROR = 'GET_LOCATION_ERROR';
export const GET_LOCATION_RESET = 'GET_LOCATION_RESET';

/*
 * action creators
 */

export function fetchLocations(data) {
  return {
    type: GET_LOCATION_REQUEST,
    payload: data,
  };
}

export function fetchLocationsSuccess(data) {
  return {
    type: GET_LOCATION_SUCCESS,
    payload: data,
  };
}

export function fetchLocationsError() {
  return {
    type: GET_LOCATION_ERROR,
  };
}

export function fetchLocationsReset() {
  return {
    type: GET_LOCATION_RESET,
  };
}
