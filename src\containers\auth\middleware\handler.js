// Referred https://medium.com/sopra-steria-norge/how-to-write-actual-api-middleware-for-next-js-2a38355f6674

/**
 * Represents the function that should be called to move to the next middleware in the chain.
 * @callback NextFunction
 */

/**
 * Middleware function that processes the request and can invoke the `next` function to pass control to the next middleware.
 * @typedef {function(Request, NextFunction): Promise<NextResponse|void>} Middleware
 */

/**
 * Handles a request by processing it through a series of middlewares.
 *
 * @param {...Middleware} middleware - A series of middleware functions to be executed in order.
 * @returns {function(Request): Promise<NextResponse>}
 *   A function that takes a request object and returns a Promise that resolves to a `NextResponse`.
 */
export const handler =
  (...middleware) =>
  async (request) => {
    let result;

    for (let i = 0; i < middleware.length; i++) {
      let nextInvoked = false;

      /**
       * Move to the next middleware in the chain.
       * @function
       */
      const next = async () => {
        nextInvoked = true;
      };

      result = await middleware[i](request, next);

      if (!nextInvoked) {
        break;
      }
    }

    if (result) {
      return result;
    }

    throw new Error('Your handler or middleware must return a NextResponse!');
  };
