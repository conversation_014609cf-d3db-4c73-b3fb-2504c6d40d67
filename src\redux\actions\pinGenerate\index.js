/*
 *PIN_GENERATE action types
 */

export const PIN_GENERATE_REQUEST = 'PIN_GENERATE_REQUEST';
export const PIN_GENERATE_SUCCESS = 'PIN_GENERATE_SUCCESS';
export const PIN_GENERATE_ERROR = 'PIN_GENERATE_ERROR';
export const PIN_GENERATE_RESET = 'PIN_GENERATE_RESET';

/*
 * action creators
 */

export function pinGenerationRequest(data) {
  return {
    type: PIN_GENERATE_REQUEST,
    payload: data,
  };
}

export function pinGenerationSuccess(data) {
  return {
    type: PIN_GENERATE_SUCCESS,
    payload: data,
  };
}

export function pinGenerationError() {
  return {
    type: PIN_GENERATE_ERROR,
  };
}

export function pinGenerationReset() {
  return {
    type: PIN_GENERATE_RESET,
  };
}
