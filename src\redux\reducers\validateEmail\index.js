import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  VALIDATE_EMAIL_REQUEST,
  VALIDATE_EMAIL_SUCCESS,
  VALIDATE_EMAIL_ERROR,
  VALIDATE_EMAIL_RESET,
} from '../../actions/validateEmail';
export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};
export const validateEmailReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case VALIDATE_EMAIL_REQUEST:
      return {
        ...state,
        isValidateEmailFetching: true,
        validateEmailSuccess: false,
        validateEmailError: false,
        validateEmailErrorData: null,
      };
    case VALIDATE_EMAIL_SUCCESS: {
      return {
        ...state,
        isValidateEmailFetching: false,
        validateEmailSuccess: true,
        validateEmailError: false,
        validateEmailErrorData: null,
        validateEmailSuccessData: payload,
      };
    }
    case VALIDATE_EMAIL_ERROR:
      return {
        ...state,
        isValidateEmailFetching: false,
        validateEmailSuccess: false,
        validateEmailError: true,
        validateEmailErrorData: payload,
      };
    case VALIDATE_EMAIL_RESET:
      return {
        ...state,
        isValidateEmailFetching: false,
        validateEmailSuccess: false,
        validateEmailError: false,
        validateEmailErrorData: null,
      };
    default:
      return state;
  }
};
