import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_ERROR,
  UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_SUCCESS,
  UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_REQUEST,
  UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_RESET,
} from '../../actions/updateSaveLiteQuestionnaireResponse';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const updateSaveLiteQuestionnaireResponseReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_REQUEST:
      return {
        ...state,
        isUpdateSaveLiteQuestionnaireResponseFetching: true,
        isUpdateSaveLiteQuestionnaireResponseSuccess: false,
        isUpdateSaveLiteQuestionnaireResponseError: false,
        updateSaveLiteQuestionnaireResponseErrorData: null,
      };
    case UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_SUCCESS: {
      return {
        ...state,
        isUpdateSaveLiteQuestionnaireResponseFetching: false,
        isUpdateSaveLiteQuestionnaireResponseSuccess: true,
        isUpdateSaveLiteQuestionnaireResponseError: false,
        updateSaveLiteQuestionnaireResponseErrorData: null,
        updateSaveLiteQuestionnaireResponseSuccessData: payload,
      };
    }
    case UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_ERROR:
      return {
        ...state,
        isUpdateSaveLiteQuestionnaireResponseFetching: false,
        isUpdateSaveLiteQuestionnaireResponseSuccess: false,
        isUpdateSaveLiteQuestionnaireResponseError: true,
        updateSaveLiteQuestionnaireResponseErrorData: payload,
      };
    case UPDATE_SAVE_LITE_QUESTIONNAIRE_RESPONSE_RESET:
      return {
        ...state,
        isUpdateSaveLiteQuestionnaireResponseFetching: false,
        isUpdateSaveLiteQuestionnaireResponseSuccess: false,
        isUpdateSaveLiteQuestionnaireResponseError: false,
        updateSaveLiteQuestionnaireResponseErrorData: null,
        updateSaveLiteQuestionnaireResponseSuccessData: null,
      };
    default:
      return state;
  }
};
