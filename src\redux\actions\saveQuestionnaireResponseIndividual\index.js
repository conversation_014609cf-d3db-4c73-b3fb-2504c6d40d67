/*
 * ASSIGN_QUESTIONNAIRE action types
 */

export const SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_REQUEST = 'SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_REQUEST';
export const SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_SUCCESS = 'SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_SUCCESS';
export const SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_ERROR = 'SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_ERROR';
export const SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_RESET = 'SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_RESET';

/*
 * action creators
 */

export function saveQuestionnaireResponseIndividual(data) {
  return {
    type: SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_REQUEST,
    payload: data,
  };
}

export function saveQuestionnaireResponseIndividualSuccess(data) {
  return {
    type: SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_SUCCESS,
    payload: data,
  };
}

export function saveQuestionnaireResponseIndividualError() {
  return {
    type: SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_ERROR,
  };
}

export function resetSaveQuestionnaireResponseIndividual() {
  return {
    type: SAVE_QUESTIONNAIRE_RESPONSE_INDIVIDUAL_RESET,
  };
}
