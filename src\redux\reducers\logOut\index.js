/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import { LOG_OUT_REQUEST, LOG_OUT_SUCCESS, LOG_OUT_ERROR } from '../../actions/logOut';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const userLogOutReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case LOG_OUT_REQUEST:
      return {
        ...state,
        userLogOutFetching: true,
        userLogOutSuccess: false,
        userLogOutError: false,
        userLogOutErrorData: null,
      };
    case LOG_OUT_SUCCESS: {
      return {
        ...state,
        userLogOutFetching: false,
        userLogOutSuccess: true,
        userLogOutError: false,
        userLogOutErrorData: null,
        userLogOutSuccessData: payload,
      };
    }
    case LOG_OUT_ERROR:
      return {
        ...state,
        userLogOutFetching: false,
        userLogOutSuccess: false,
        userLogOutError: true,
        userLogOutErrorData: payload,
      };
    default:
      return state;
  }
};
