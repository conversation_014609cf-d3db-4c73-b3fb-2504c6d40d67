import { FormControl, TextField, Grid, Stack, Box } from '@mui/material';
import dataValidation from '../../../utils/dataValidation/dataValidation';
import { individualFieldValidation, contactFieldValidation } from '../../../utils/helpers/validation';

export const RegisterFields = (props) => {
  const {
    demographicFields,
    individual,
    handleIndividualChangeCallback,
    handleContactChangeCallback,
    fields,
    validationData,
    index,
  } = props;

  const handleIndividualChange = (value, fieldName, isRequired) => {
    individual[fieldName] = value;
    handleIndividualChangeCallback(individual, index, individualFieldValidation(value, fieldName, index, isRequired));
  };

  const handleContactChange = (value, fieldName, isRequired) => {
    demographicFields.contact[fieldName] = value;
    handleContactChangeCallback(demographicFields.contact, contactFieldValidation(value, fieldName, isRequired));
  };

  const getFirstName = (item) => {
    return (
      <Grid item xs={12} sm={10} md={7.5} lg={5} sx={{ mt: 2, pr: 1 }}>
        <FormControl fullWidth variant="outlined">
          <TextField
            defaultValue={individual?.firstName}
            onChange={(e) => handleIndividualChange(e.target.value, 'firstName', item.isMandatory)}
            label={item.display}
            required={item.isMandatory}
            error={validationData?.individuals ? !!validationData?.individuals[index]?.firstName : false}
            helperText={validationData?.individuals ? validationData?.individuals[index]?.firstName : ''}
          />
        </FormControl>
      </Grid>
    );
  };

  const getLastName = (item) => {
    return (
      <Grid item xs={12} sm={10} md={7.5} lg={5} sx={{ mt: 2, pr: 1 }}>
        <FormControl fullWidth variant="outlined">
          <TextField
            defaultValue={individual?.lastName}
            onChange={(e) => handleIndividualChange(e.target.value, 'lastName', item.isMandatory)}
            label={item.display}
            required={item.isMandatory}
            error={validationData?.individuals ? !!validationData?.individuals[index]?.lastName : false}
            helperText={validationData?.individuals ? validationData?.individuals[index]?.lastName : ''}
          />
        </FormControl>
      </Grid>
    );
  };

  const getEmail = (item) => {
    return (
      <Grid item xs={12} sm={10} md={7.5} lg={5} sx={{ mt: 2, pr: 1 }}>
        <FormControl fullWidth variant="outlined">
          <TextField
            defaultValue={demographicFields?.contact?.email}
            onChange={(e) => handleContactChange(e.target.value, 'email', item.isMandatory)}
            label={item.display}
            required={item.isMandatory}
            error={!!validationData?.contact?.email}
            helperText={validationData?.contact?.email}
          />
        </FormControl>
      </Grid>
    );
  };

  return (
    <Stack direction="column">
      <Box>
        {!dataValidation.isDataEmpty(fields) &&
          fields.map((item, index) => {
            return (
              <Grid container key={index}>
                {item.code === 'FIRST_NAME' && getFirstName(item)}
                {item.code === 'LAST_NAME' && getLastName(item)}
                {item.code === 'EMAIL' && getEmail(item)}
              </Grid>
            );
          })}
      </Box>
    </Stack>
  );
};
