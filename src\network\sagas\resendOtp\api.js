/*
 *
 */
import { makeNetworkCall } from '../..';
import { RESEND_OTP, BASE_URL } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiResendOtp(action) {
  const { payload = {} } = action || {};
  const { headers = {}, otpData = {} } = payload || {};
  const config = {
    method: 'POST',
    url: BASE_URL + RESEND_OTP,
    headers: headers,
    data: otpData,
  };
  return makeNetworkCall(config);
}
