/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import {
  VALIDATE_EMAIL_IN_BOOKING_ERROR,
  VALIDATE_EMAIL_IN_BOOKING_REQUEST,
  VALIDATE_EMAIL_IN_BOOKING_SUCCESS,
} from '../../../redux/actions/validateEmailInBooking';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeApiValidateEmailInBooking } from './api';
/**
 *
 * @param {*} action
 */

export function* validateEmailInBooking(action) {
  try {
    const response = yield call(makeApiValidateEmailInBooking, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: VALIDATE_EMAIL_IN_BOOKING_SUCCESS, payload: data });
    } else {
      yield put({ type: VALIDATE_EMAIL_IN_BOOKING_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: VALIDATE_EMAIL_IN_BOOKING_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchValidateEmailInBooking() {
  yield takeEvery(VALIDATE_EMAIL_IN_BOOKING_REQUEST, validateEmailInBooking);
}
