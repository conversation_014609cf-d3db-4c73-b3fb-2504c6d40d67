# DNS for Widget Public hosting
NEXT_PUBLIC_BASE_URL=

# Google Map API key
NEXT_PUBLIC_GOOGLE_MAP_API_KEY=


# * APIs variables
NEXT_PUBLIC_WIDGET_SERVICES_BASE_URL=

# Schedular Booking API Gateway
NEXT_PUBLIC_BOOKING_SERVICES_BASE_URL=

# Artifact repository 
# Private Artifact repository
NEXT_PUBLIC_ARTIFACT_PRIVATE_REPOSITORY_BASE_URL=

# Public Artifact repository
NEXT_PUBLIC_ARTIFACT_PUBLIC_REPOSITORY_BASE_URL=

# CDRs
NEXT_PUBLIC_INDIVIDUAL_CDR_BASE_URL=

NEXT_PUBLIC_ORGANIZATION_CDR_BASE_URL=

# Indexes
NEXT_PUBLIC_CONNECTION_INDEX_BASE_URL=

NEXT_PUBLIC_CLIENT_INDEX_BASE_URL=
NEXT_PUBLIC_ORGANIZATION_MESSAGING_SERVICE_BASE_URL=

NEXT_PUBLIC_ORGANIZATION_INDIVIDUAL_SERVICE_BASE_URL=

# DOC Gen Service
NEXT_PUBLIC_PDF_GENERATION_SERVICE_BASE_URL=

NEXT_PUBLIC_AWS_SERVICES_ENVIRONMENT=

# Allow access to services in Org Env
COGNITO_ORG_MACHINE_USER_POOL_ID=
COGNITO_ORG_MACHINE_APP_CLIENT_ID=
COGNITO_ORG_MACHINE_APP_CLIENT_SECRET=
COGNITO_ORG_MACHINE_USERNAME=
COGNITO_ORG_MACHINE_PASSWORD=

# Allow access to services in Network Env
COGNITO_NETWORK_MACHINE_USER_POOL_ID=
COGNITO_NETWORK_MACHINE_APP_CLIENT_ID=
COGNITO_NETWORK_MACHINE_APP_CLIENT_SECRET=
COGNITO_NETWORK_MACHINE_USERNAME=
COGNITO_NETWORK_MACHINE_PASSWORD=

WIDGET_ALLOW_COGNITO_ACCESS_IAM_ACCESS_KEY=
WIDGET_ALLOW_COGNITO_ACCESS_IAM_SECRET_KEY=

# Allow access to services in Individual Env
COGNITO_INDIVIDUAL_MACHINE_USER_POOL_ID=
COGNITO_INDIVIDUAL_MACHINE_APP_CLIENT_ID=
COGNITO_INDIVIDUAL_MACHINE_APP_CLIENT_SECRET=
COGNITO_INDIVIDUAL_MACHINE_USERNAME=
COGNITO_INDIVIDUAL_MACHINE_PASSWORD=

# Cognito Human auth variables
COGNITO_INDIVIDUAL_HUMAN_REGION=
COGNITO_INDIVIUDAL_HUMAN_USER_POOL_ID=
COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_ID=
COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_SECRET=

NEXTAUTH_URL = $NEXT_PUBLIC_BASE_URL
NEXTAUTH_SECRET = RaB2obLvjFGIAWGLSLHBxDjQjMbpESnOxGeyPEJ6w6I= #same as before
