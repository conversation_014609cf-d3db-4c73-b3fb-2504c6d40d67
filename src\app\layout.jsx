import Wrappers from '@/app/Wrappers';
import './globals.css';
import '@appscoopsolutions/component-questionnaire/dist/esm/index.css';
import { Inter } from 'next/font/google';
import { getServerSession } from 'next-auth';
import Script from 'next/script';
const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'Widget',
  description: "A Cambian Business Services's Product",
};

export default async function RootLayout({ children }) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Wrappers>
          <main>{children}</main>
        </Wrappers>
      </body>
      <Script src="/iframeResizerNew.js" />
      <Script src="/iframeResizer.contentWindow.min.js" />
    </html>
  );
}
