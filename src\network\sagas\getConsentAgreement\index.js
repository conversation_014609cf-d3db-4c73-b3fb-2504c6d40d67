import { call, put, takeEvery } from 'redux-saga/effects';
import {
  GET_CONSENT_AGREEMENT_ERROR,
  GET_CONSENT_AGREEMENT_SUCCESS,
  GET_CONSENT_AGREEMENT_REQUEST,
} from '../../../redux/actions/getConsentAgreement';
import { makeApiFetchConsentAgreement } from './api';
import { API_RESPONSE_SUCCESS } from '@/utils/constants/apiCodes';

export function* fetchConsentAgreement(action) {
  try {
    const response = yield call(makeApiFetchConsentAgreement, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: GET_CONSENT_AGREEMENT_SUCCESS, payload: data });
    } else {
      yield put({ type: GET_CONSENT_AGREEMENT_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: GET_CONSENT_AGREEMENT_ERROR });
  }
}

// Our watcher Saga:
export function* watchFetchConsentAgreement() {
  yield takeEvery(GET_CONSENT_AGREEMENT_REQUEST, fetchConsentAgreement);
}
