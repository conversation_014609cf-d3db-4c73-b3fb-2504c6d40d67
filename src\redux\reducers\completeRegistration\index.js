import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  COMPLETE_REGISTRATION_REQUEST,
  COMPLETE_REGISTRATION_SUCCESS,
  COMPLETE_REGISTRATION_ERROR,
  COMPLETE_REGISTRATION_RESET,
} from '../../actions/completeRegistration';
export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};
export const completeRegistrationReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case COMPLETE_REGISTRATION_REQUEST:
      return {
        ...state,
        completeRegistrationFetching: true,
        completeRegistrationSuccess: false,
        completeRegistrationError: false,
        completeRegistrationErrorData: null,
      };
    case COMPLETE_REGISTRATION_SUCCESS: {
      return {
        ...state,
        completeRegistrationFetching: false,
        completeRegistrationSuccess: true,
        completeRegistrationError: false,
        completeRegistrationErrorData: null,
        completeRegistrationSuccessData: payload,
      };
    }
    case COMPLETE_REGISTRATION_ERROR:
      return {
        ...state,
        completeRegistrationFetching: false,
        completeRegistrationSuccess: false,
        completeRegistrationError: true,
        completeRegistrationErrorData: payload,
      };
    case COMPLETE_REGISTRATION_RESET:
      return {
        ...state,
        completeRegistrationFetching: false,
        completeRegistrationSuccess: false,
        completeRegistrationError: false,
        completeRegistrationErrorData: null,
        completeRegistrationSuccessData: null,
      };
    default:
      return state;
  }
};
