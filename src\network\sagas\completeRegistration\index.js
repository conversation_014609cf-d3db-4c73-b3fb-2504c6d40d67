/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import {
  COMPLETE_REGISTRATION_REQUEST,
  COMPLETE_REGISTRATION_SUCCESS,
  COMPLETE_REGISTRATION_ERROR,
} from '../../../redux/actions/completeRegistration';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { makeCompleteRegistration } from './api';
/**
 *
 * @param {*} action
 */
/**
 
/**
 *
 * @param {*} action
 */

export function* completeRegistration(action) {
  try {
    const response = yield call(makeCompleteRegistration, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: COMPLETE_REGISTRATION_SUCCESS, payload: data });
    } else {
      yield put({ type: COMPLETE_REGISTRATION_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: COMPLETE_REGISTRATION_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchCompleteRegistration() {
  yield takeEvery(COMPLETE_REGISTRATION_REQUEST, completeRegistration);
}
