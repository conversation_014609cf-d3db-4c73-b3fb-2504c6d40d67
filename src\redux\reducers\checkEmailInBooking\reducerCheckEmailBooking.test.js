import {
  CHECK_EMAIL_REQUEST,
  CHECK_EMAIL_SUCCESS,
  CHECK_EMAIL_ERROR,
  CHECK_EMAIL_RESET,
} from '../../actions/checkEmailInBooking';
import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import { checkEmailReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isCheckEmailFetching: true,
  checkEmailSuccess: false,
  checkEmailError: false,
  checkEmailErrorData: null,
};

const successState = {
  isCheckEmailFetching: false,
  checkEmailSuccess: true,
  checkEmailError: false,
  checkEmailErrorData: null,
  checkEmailSuccessData: data,
};

const errorState = {
  isCheckEmailFetching: false,
  checkEmailSuccess: false,
  checkEmailError: true,
  checkEmailErrorData: data,
};

const resetState = {
  isCheckEmailFetching: false,
  checkEmailSuccess: false,
  checkEmailError: false,
  checkEmailErrorData: null,
};

describe('Reschedule Reducer', () => {
  it('should return the initial state', () => {
    expect(checkEmailReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle CHECK_EMAIL_REQUEST', () => {
    expect(
      checkEmailReducer(initialState, {
        type: CHECK_EMAIL_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle CHECK_EMAIL_SUCCESS', () => {
    expect(
      checkEmailReducer(initialState, {
        type: CHECK_EMAIL_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle CHECK_EMAIL_ERROR', () => {
    expect(
      checkEmailReducer(initialState, {
        type: CHECK_EMAIL_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle CHECK_EMAIL_RESET', () => {
    expect(
      checkEmailReducer(initialState, {
        type: CHECK_EMAIL_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
