/* 
*
*
*
COMPONENT TO BE REMOVED, NOT BEING USED ANYMORE
*
*
*/
import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { checkEmail } from '@/redux/actions/checkEmailInBooking';
import * as DemographicUtility from '@/containers/Demographics/demographicUtility';
import * as CommonUtility from '@/containers/commonUtility';
import { Box } from '@mui/material';
import { actions, otpContactType, IDENTIFIED } from '@/containers/commonConstants';
import { pages } from '../../QuestionnaireWidget/questionnaireConstants';
import { EditDemographic, EmailValidation } from '@/containers/Demographics';
import { Loader } from '@/components';
import useNotification from '@/hooks/useNotification';
import { useTranslation } from 'react-i18next';
import { useCommonAPIs } from '@/hooks/useCommonAPIs';
import { useSession } from 'next-auth/react';

export const Demographic = (props) => {
  const {
    questionnaireWidgetSuccessData,
    handleNavigationCallback,
    demographic: demographicProps,
    isClientSummary,
    handleSummaryNavigationCallback,
  } = props;
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { data: session } = useSession();
  const {
    fetchUpdateConnectionAtClientIndex,
    fetchSearchIndividualAtClientIndex,
    getClientId,
    makeConnectionAtClientIndex,
  } = useCommonAPIs();
  const [, sendNotification] = useNotification();
  const [currentPage, setCurrentPage] = useState(pages.DEMOGRAPHIC);
  const [demographic, setDemographic] = useState(demographicProps);
  const [isAddNewPatientCalled, setIsAddNewPatientCalled] = useState(false);
  const [isClientSearched, setIsClientSearched] = useState(false);

  useEffect(() => {
    if (demographicProps) {
      setDemographic(demographicProps);
    }
  }, [demographicProps]);

  const { addNewPatientSuccess, addNewPatientError, addNewPatientErrorData, addNewPatientSuccessData } = useSelector(
    (state) => state.addNewPatientReducer,
  );

  //check email address in Questionnaire
  const { isCheckEmailFetching, checkEmailSuccess, checkEmailError, checkEmailErrorData, checkEmailSuccessData } =
    useSelector((state) => state.checkEmailReducer);

  const oauthSuccessData = useSelector((state) => state.oAuthReducer.oauthSuccessData);
  const access_token = (oauthSuccessData && oauthSuccessData.access_token) || '';

  const { individualUserInfoSuccessData } = useSelector((state) => state.individualUserInfoReducer);

  const { checkExistingClientIndexSuccessData } = useSelector((state) => state.checkExistingClientIndexReducer) || {};

  const { checkExistingConnectionIndexSuccess } =
    useSelector((state) => state.checkExistingConnectionIndexReducer) || {};
  const { createConnectionAtConnectionIndexSuccess } =
    useSelector((state) => state.createConnectionAtConnectionIndexReducer) || {};
  const { createConnectionAtClientIndexSuccess } =
    useSelector((state) => state.createConnectionAtClientIndexReducer) || {};
  const {
    isUpdateConnectionAtClientIndexFetching,
    updateConnectionAtClientIndexSuccess,
    updateConnectionAtClientIndexError,
  } = useSelector((state) => state.updateConnectionAtClientIndexReducer);

  const {
    isSearchIndividualAtClientIndexFetching,
    searchIndividualAtClientIndexSuccessData,
    searchIndividualAtClientIndexError,
    searchIndividualAtClientIndexErrorData,
  } = useSelector((state) => state.searchIndividualAtClientIndexReducer);

  const {
    isConnectionRequired,
    clientGroup,
    flow = null,
    fields,
    provinces,
    widgetType,
    questionnaire,
    otpVerificationEnabled,
    clientInformationPageTitle,
    clientInformationPageSubtitle,
  } = questionnaireWidgetSuccessData || {};

  const headers = CommonUtility.getHeader(access_token, flow);
  const handleSendOtp = (demographic) => {
    setDemographic(demographic);
    if (otpVerificationEnabled) {
      const contactType =
        demographic?.preferredContactMethod === 'Email' ? otpContactType.EMAIL : otpContactType.CELL_PHONE;
      const contactInfo =
        demographic?.preferredContactMethod === 'Email' ? demographic?.contact?.email : demographic?.contact?.phone;
      const checkContactData = DemographicUtility.getCheckEmailData(contactType, contactInfo, questionnaire?.name);
      dispatch(checkEmail({ headers, checkEmailData: checkContactData }));
    } else {
      handleNavigationCallback(actions.NEXT, demographic);
    }
  };

  useEffect(() => {
    if (checkEmailSuccess && checkEmailSuccessData && demographic) {
      if (checkEmailSuccessData.id === 'email_verification_pending') {
        setCurrentPage(pages.EMAIL_VALIDATION);
        sendNotification({ msg: t('otpSent'), variant: 'info' });
      } else {
        handleNavigationCallback(actions.NEXT, demographic);
      }
    }
    if (checkEmailError && checkEmailErrorData && demographic) {
      sendNotification({ msg: t('apiError'), variant: 'error' });
    }
  }, [checkEmailSuccess, checkEmailSuccessData, checkEmailError, checkEmailErrorData]);

  useEffect(() => {
    if (isAddNewPatientCalled) {
      if (addNewPatientSuccess && addNewPatientSuccessData) {
        setIsAddNewPatientCalled(false);
        demographic.id = addNewPatientSuccessData.id;
        demographic.resourceType = addNewPatientSuccessData.resourceType;
        handleNavigationCallback(actions.NEXT, demographic);
      }

      if (addNewPatientError && addNewPatientErrorData) {
        setIsAddNewPatientCalled(false);
        sendNotification({ msg: t('apiError'), variant: 'error' });
      }
    }
  }, [addNewPatientSuccess, addNewPatientSuccessData, addNewPatientError, addNewPatientErrorData]);

  useEffect(() => {
    if (updateConnectionAtClientIndexSuccess) {
      handleNavigationCallback(actions.NEXT, demographic);
    } else if (updateConnectionAtClientIndexError) {
      sendNotification({ variant: 'error', msg: t('apiError') });
    }
  }, [updateConnectionAtClientIndexSuccess, updateConnectionAtClientIndexError]);

  // useEffect(() => {
  //   if (isClientSearched && searchIndividualAtClientIndexSuccessData) {
  //     const { found: clientFound, identifiers = [] } = searchIndividualAtClientIndexSuccessData;
  //     if (!clientFound)
  //       sendNotification({ variant: 'error', msg: 'Record not found, please contact your administrator' });
  //     if (clientFound) {
  //       const updatedDemographic = { ...demographic, id: identifiers[0]?.id };
  //       handleNavigationCallback(actions.NEXT, updatedDemographic);
  //       setIsClientSearched(false);
  //     }
  //   } else if (searchIndividualAtClientIndexError) {
  //     sendNotification({ variant: 'error', msg: t('apiError') });
  //   }
  // }, [searchIndividualAtClientIndexSuccessData, searchIndividualAtClientIndexError]);
  useEffect(() => {
    if (isClientSearched && searchIndividualAtClientIndexSuccessData) {
      const { found: clientFound, identifiers = [] } = searchIndividualAtClientIndexSuccessData?.data;
      if (clientFound) {
        const updatedDemographic = { ...demographic, id: identifiers[0]?.id };
        handleNavigationCallback(actions.NEXT, updatedDemographic);
        setIsClientSearched(false);
      } else {
        if (isConnectionRequired === false) {
          makeConnectionAtClientIndex({ IDENTIFIED, demographic: demographic });
        } else {
          handleNavigationCallback(actions.FINISH, demographic);
        }
        // sendNotification({ variant: 'error', msg: 'Record not found, please contact your administrator' });
      }
    } else if (searchIndividualAtClientIndexError) {
      sendNotification({ variant: 'error', msg: t('apiError') });
    }
  }, [
    searchIndividualAtClientIndexSuccessData,
    searchIndividualAtClientIndexError,
    createConnectionAtClientIndexSuccess,
  ]);

  // * sign-in -> if connection exists on both side, and all the required fields are present, click next
  // * should move to next screen without doing anything additionally
  // * if one or more fields are missing in client index from the required ones, then make a update request
  // * at the CLIENT INDEX and then move to next screen,
  // * no sign-in -> search at client index with demographic details, if connection found proceed, else end;

  // const handleConnectionChecks = (demographic) => {
  //   //TODO: now change this function name as now we are not checking any connection check from frontend intead doing SEARCH_INDIVIDUAL_AT_CLIENT_INDEX
  //   //commenting all the bellow code as the new requirement is to call `${CLIENT_INDEX_BASE_URL}/${ORGANIZATION_ID}/client-ids`
  //   // const clientId = getClientId();
  //   // const allRequiredDetailsAvailable = CommonUtility.checkRequiredDemographicsAtClientIndex(
  //   //   fields,
  //   //   checkExistingClientIndexSuccessData,
  //   // );

  //   // const connectionData = CommonUtility.createUpdateExistingConnectionAtClientIndexData(
  //   //   demographic,
  //   //   checkExistingClientIndexSuccessData,
  //   //   clientId,
  //   // );

  //   // if (createConnectionAtConnectionIndexSuccess && !allRequiredDetailsAvailable) {
  //   //   fetchUpdateConnectionAtClientIndex(connectionData);
  //   //   return;
  //   // }

  //   // if (checkExistingClientIndexSuccessData && checkExistingConnectionIndexSuccess) {
  //   //   if (allRequiredDetailsAvailable) {
  //   //     handleNavigationCallback(actions.NEXT, demographic);
  //   //   } else {
  //   //     fetchUpdateConnectionAtClientIndex(connectionData);
  //   //   }
  //   //   return;
  //   // }

  //   // if (!session?.user) {
  //   //   makeConnectionAtClientIndex({ IDENTIFIED, demographic: demographic });
  //   //   return;
  //   // }

  //   // handleNavigationCallback(actions.NEXT, demographic);

  fetchSearchIndividualAtClientIndex(demographicFields);
  setIsClientSearched(true);
  // };

  const handleDemographicNavigation = (demographic) => {
    setDemographic(demographic);

    if (!otpVerificationEnabled) {
      handleConnectionChecks(demographic);
    } else {
      handleSendOtp(demographic);
    }
  };

  const handleEmailValidation = (action) => {
    if (actions.NEXT === action) {
      handleConnectionChecks(demographic);
    } else if (actions.PREVIOUS === action) {
      setCurrentPage(pages.DEMOGRAPHIC);
    }
  };

  const getPage = () => {
    let page;
    if (currentPage === pages.DEMOGRAPHIC) {
      page = (
        <EditDemographic
          title={clientInformationPageTitle}
          subtitle={clientInformationPageSubtitle}
          handleDemographicCreationCallback={handleDemographicNavigation}
          handleSummaryNavigationCallback={handleSummaryNavigationCallback}
          clientGroup={clientGroup}
          fields={fields}
          demographic={demographic}
          isClientSummary={isClientSummary}
          widgetType={widgetType}
          provinces={provinces}
        />
      );
    } else if (currentPage === pages.EMAIL_VALIDATION) {
      page = (
        <EmailValidation
          handleEmailValidationCallback={handleEmailValidation}
          handleResendCodeCallback={handleSendOtp}
          demographic={demographic}
          isClientSummary={isClientSummary}
        />
      );
    }

    return page;
  };

  return (
    <>
      <Loader
        active={
          isCheckEmailFetching || isUpdateConnectionAtClientIndexFetching || isSearchIndividualAtClientIndexFetching
        }
      />
      <Box sx={{ px: '4%', pt: '8px', pb: '48px' }}>{getPage()}</Box>
    </>
  );
};
