import {
  CONFIRM_APPOINTMENT_REQUEST,
  CONFIRM_APPOINTMENT_SUCCESS,
  CONFIRM_APPOINTMENT_ERROR,
  CONFIRM_APPOINTMENT_RESET,
} from '../../actions/confirmAppointment';

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import { confirmAppointmentReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  confirmAppointmentFetching: true,
  confirmAppointmentSuccess: false,
  confirmAppointmentError: false,
  confirmAppointmentErrorData: null,
};

const successState = {
  confirmAppointmentFetching: false,
  confirmAppointmentSuccess: true,
  confirmAppointmentError: false,
  confirmAppointmentErrorData: null,
  confirmAppointmentSuccessData: data,
};

const errorState = {
  confirmAppointmentFetching: false,
  confirmAppointmentSuccess: false,
  confirmAppointmentError: true,
  confirmAppointmentErrorData: data,
};

const resetState = {
  confirmAppointmentFetching: false,
  confirmAppointmentSuccess: false,
  confirmAppointmentError: true,
  confirmAppointmentErrorData: null,
  confirmAppointmentSuccessData: null,
};

describe('Confirm Reducer', () => {
  it('should return the initial state', () => {
    expect(confirmAppointmentReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle CONFIRM_APPOINTMENT_REQUEST', () => {
    expect(
      confirmAppointmentReducer(initialState, {
        type: CONFIRM_APPOINTMENT_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle CONFIRM_APPOINTMENT_SUCCESS', () => {
    expect(
      confirmAppointmentReducer(initialState, {
        type: CONFIRM_APPOINTMENT_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle CONFIRM_APPOINTMENT_ERROR', () => {
    expect(
      confirmAppointmentReducer(initialState, {
        type: CONFIRM_APPOINTMENT_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle CONFIRM_APPOINTMENT_RESET', () => {
    expect(
      confirmAppointmentReducer(initialState, {
        type: CONFIRM_APPOINTMENT_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
