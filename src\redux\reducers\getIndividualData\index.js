import {
  GET_INDIVIDUAL_DATA_REQUEST,
  GET_INDIVIDUAL_DATA_SUCCESS,
  GET_INDIVIDUAL_DATA_ERROR,
  GET_INDIVIDUAL_DATA_RESET,
} from '@/redux/actions/getIndividualData';
import * as GL<PERSON>BA<PERSON> from '../globals';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const getIndividualDataReducer = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case GET_INDIVIDUAL_DATA_REQUEST:
      return {
        ...state,
        isGetIndividualDataFetching: true,
        getIndividualDataSuccess: false,
        getIndividualDataError: false,
        getIndividualDataErrorData: null,
      };
    case GET_INDIVIDUAL_DATA_SUCCESS:
      return {
        ...state,
        isGetIndividualDataFetching: false,
        getIndividualDataSuccess: true,
        getIndividualDataError: false,
        getIndividualDataErrorData: null,
        getIndividualDataSuccessData: payload,
      };
    case GET_INDIVIDUAL_DATA_ERROR:
      return {
        ...state,
        isGetIndividualDataFetching: false,
        getIndividualDataSuccess: false,
        getIndividualDataSuccessData: null,
        getIndividualDataError: true,
        getIndividualDataErrorData: payload,
      };
    case GET_INDIVIDUAL_DATA_RESET:
      return {
        ...state,
        isGetIndividualDataFetching: undefined,
        getIndividualDataSuccess: undefined,
        getIndividualDataSuccessData: undefined,
        getIndividualDataError: undefined,
        getIndividualDataErrorData: undefined,
      };
    default:
      return state;
  }
};
