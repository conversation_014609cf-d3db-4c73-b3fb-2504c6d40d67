import { NextResponse } from 'next/server';

export class CustomError {
  constructor({ status, message }) {
    /**
     * @type {number}
     */
    this.status = status;

    /**
     * @type {string}
     */
    this.message = message;
  }

  /**
   * @returns {NextResponse}
   */
  toNextResponse() {
    return NextResponse.json({ message: this.message }, { status: this.status });
  }

  /**
   * @returns {Error}
   */
  toNodeError() {
    return new Error(JSON.stringify(this));
  }
}
