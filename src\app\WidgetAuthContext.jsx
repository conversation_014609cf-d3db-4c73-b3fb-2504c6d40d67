'use client';
import { saveWidgetAuth } from '@/redux/actions/widgetAuth';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';

export const WidgetAuthContext = ({ authData }) => {
  console.log('widget auth context: received authData', authData);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(saveWidgetAuth(authData.isSuccess, authData.response));
  }, [authData]);

  return <></>;
};
