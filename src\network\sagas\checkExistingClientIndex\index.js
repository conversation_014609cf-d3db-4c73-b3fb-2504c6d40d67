import { call, put, takeEvery } from 'redux-saga/effects';
import {
  CHECK_EXISTING_CLIENT_INDEX_REQUEST,
  CHECK_EXISTING_CLIENT_INDEX_SUCCESS,
  CHECK_EXISTING_CLIENT_INDEX_ERROR,
} from '../../../redux/actions/checkExistingClientIndex';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import { fetchCheckExistingClientIndex } from './api';

export function* checkExistingClientIndex(action) {
  try {
    const response = yield call(fetchCheckExistingClientIndex, action);
    const { data = {} } = response;

    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: CHECK_EXISTING_CLIENT_INDEX_SUCCESS, payload: data });
    } else {
      yield put({ type: CHECK_EXISTING_CLIENT_INDEX_ERROR, payload: { statusCode: response.status, ...data } });
    }
  } catch (error) {
    yield put({ type: CHECK_EXISTING_CLIENT_INDEX_ERROR });
  }
}

export function* watchCheckExistingClientIndex() {
  yield takeEvery(CHECK_EXISTING_CLIENT_INDEX_REQUEST, checkExistingClientIndex);
}
