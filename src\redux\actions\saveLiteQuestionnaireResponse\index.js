/*
 * POST Questions action types
 */

export const SAVE_LITE_QUESTIONNAIRE_RESPONSE_REQUEST = 'SAVE_LITE_QUESTIONNAIRE_RESPONSE_REQUEST';
export const SAVE_LITE_QUESTIONNAIRE_RESPONSE_SUCCESS = 'SAVE_LITE_QUESTIONNAIRE_RESPONSE_SUCCESS';
export const SAVE_LITE_QUESTIONNAIRE_RESPONSE_ERROR = 'SAVE_LITE_QUESTIONNAIRE_RESPONSE_ERROR';
export const SAVE_LITE_QUESTIONNAIRE_RESPONSE_RESET = 'SAVE_LITE_QUESTIONNAIRE_RESPONSE_RESET';
/*
 * action creators
 */

export function saveLiteQuestionnaireResponse(data) {
  return {
    type: SAVE_LITE_QUESTIONNAIRE_RESPONSE_REQUEST,
    payload: data,
  };
}

export function saveLiteQuestionnaireResponseSuccess(data) {
  return {
    type: SAVE_LITE_QUESTIONNAIRE_RESPONSE_SUCCESS,
    payload: data,
  };
}

export function saveLiteQuestionnaireResponseError() {
  return {
    type: SAVE_LITE_QUESTIONNAIRE_RESPONSE_ERROR,
  };
}

export function resetSaveLiteQuestionnaireResponse() {
  return {
    type: SAVE_LITE_QUESTIONNAIRE_RESPONSE_RESET,
  };
}
