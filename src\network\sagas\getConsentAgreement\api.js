import { makeNetworkCall } from '../..';
import { ORGANIZATION_ID } from '../../../utils/constants';
import { GET_CONSENT_AGREEMENT } from '@/utils/constants/awsApiEndpoints';

export function makeApiFetchConsentAgreement(action) {
  const { payload = {} } = action || {};
  const { headers = {}, organizationId } = payload || {};

  let URL = GET_CONSENT_AGREEMENT.replace(ORGANIZATION_ID, organizationId);

  if (localStorage.getItem('consent_api_identifier')) {
    URL = URL.replace('ffp548rwv6', '4gnjl6hvq5');
  }

  const config = {
    method: 'GET',
    url: URL,
    headers: headers,
  };

  return makeNetworkCall(config);
}
