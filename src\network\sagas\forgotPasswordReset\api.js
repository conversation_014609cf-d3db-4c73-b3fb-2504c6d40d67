/*
 *
 */

import { makeNetworkCall } from '../..';
import { EMAIL } from '../../../utils/constants';
import { FORGOT_PASSWORD, BASE_URL, CAMBIAN_SERVICE_BASE_URL } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiForgotPassword(action) {
  const { payload = {} } = action || {};
  const { headers = {}, email } = payload || {};

  const mainUrl = CAMBIAN_SERVICE_BASE_URL + FORGOT_PASSWORD.replace(EMAIL, email);

  const config = {
    method: 'POST',
    url: mainUrl,
    headers: headers,
  };
  return makeNetworkCall(config);
}
