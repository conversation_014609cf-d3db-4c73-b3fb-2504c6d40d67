import {
  VALIDATE_EMAIL_IN_BOOKING_REQUEST,
  VALIDATE_EMAIL_IN_BOOKING_SUCCESS,
  VALIDATE_EMAIL_IN_BOOKING_ERROR,
  VALIDATE_EMAIL_IN_BOOKING_RESET,
} from '../../actions/validateEmailInBooking';
import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import { validateEmailInBookingReducer } from './index';

const data = {};
const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

const requestState = {
  isValidateEmailInBookingFetching: true,
  validateEmailInBookingSuccess: false,
  validateEmailInBookingError: false,
  validateEmailInBookingErrorData: null,
};

const successState = {
  isValidateEmailInBookingFetching: false,
  validateEmailInBookingSuccess: true,
  validateEmailInBookingError: false,
  validateEmailInBookingErrorData: null,
  validateEmailInBookingSuccessData: data,
};

const errorState = {
  isValidateEmailInBookingFetching: false,
  validateEmailInBookingSuccess: false,
  validateEmailInBookingError: true,
  validateEmailInBookingErrorData: data,
};

const resetState = {
  isValidateEmailInBookingFetching: false,
  validateEmailInBookingSuccess: false,
  validateEmailInBookingError: false,
  validateEmailInBookingErrorData: null,
};

describe('Reschedule Reducer', () => {
  it('should return the initial state', () => {
    expect(validateEmailInBookingReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle VALIDATE_EMAIL_IN_BOOKING_REQUEST', () => {
    expect(
      validateEmailInBookingReducer(initialState, {
        type: VALIDATE_EMAIL_IN_BOOKING_REQUEST,
      }),
    ).toEqual({ ...initialState, ...requestState });
  });

  it('should handle VALIDATE_EMAIL_IN_BOOKING_SUCCESS', () => {
    expect(
      validateEmailInBookingReducer(initialState, {
        type: VALIDATE_EMAIL_IN_BOOKING_SUCCESS,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...successState });
  });

  it('should handle VALIDATE_EMAIL_IN_BOOKING_ERROR', () => {
    expect(
      validateEmailInBookingReducer(initialState, {
        type: VALIDATE_EMAIL_IN_BOOKING_ERROR,
        payload: data,
      }),
    ).toEqual({ ...initialState, ...errorState });
  });

  it('should handle VALIDATE_EMAIL_IN_BOOKING_RESET', () => {
    expect(
      validateEmailInBookingReducer(initialState, {
        type: VALIDATE_EMAIL_IN_BOOKING_RESET,
      }),
    ).toEqual({ ...initialState, ...resetState });
  });
});
