import { NextResponse } from 'next/server';

export class AwsError {
  /**
   * @param {Object} params
   * @param {Error} params.awsError
   */
  constructor(awsError) {
    /**
     * @type {number}
     */
    this.status = awsError['$metadata']?.httpStatusCode || 400;

    /**
     * @type {string}
     *
     * An example of awsError.toString() is "CodeMismatchException: Invalid verification code provided, please try again."
     */
    this.message = awsError.toString().split(':')[1].trim();

    /**
     * @type {string}
     */
    this.name = awsError.name;
  }

  /**
   * @returns {NextResponse}
   */
  toNextResponse() {
    return NextResponse.json({ message: this.message, errorName: this.name }, { status: this.status });
  }

  /**
   * @returns {Error}
   */
  toNodeError() {
    return new Error(JSON.stringify(this));
  }
}
