import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  UPDATE_CONNECTION_AT_CLIENT_INDEX_REQUEST,
  UPDATE_CONNECTION_AT_CLIENT_INDEX_SUCCESS,
  UPDATE_CONNECTION_AT_CLIENT_INDEX_ERROR,
  UPDATE_CONNECTION_AT_CLIENT_INDEX_RESET,
} from '../../actions/updateConnectionAtClientIndex';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const updateConnectionAtClientIndexReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case UPDATE_CONNECTION_AT_CLIENT_INDEX_REQUEST:
      return {
        ...state,
        isUpdateConnectionAtClientIndexFetching: true,
        updateConnectionAtClientIndexSuccess: false,
        updateConnectionAtClientIndexError: false,
        updateConnectionAtClientIndexErrorData: null,
      };
    case UPDATE_CONNECTION_AT_CLIENT_INDEX_SUCCESS: {
      return {
        ...state,
        isUpdateConnectionAtClientIndexFetching: false,
        updateConnectionAtClientIndexSuccess: true,
        updateConnectionAtClientIndexError: false,
        updateConnectionAtClientIndexErrorData: null,
        updateConnectionAtClientIndexSuccessData: payload,
      };
    }
    case UPDATE_CONNECTION_AT_CLIENT_INDEX_ERROR:
      return {
        ...state,
        isUpdateConnectionAtClientIndexFetching: false,
        updateConnectionAtClientIndexSuccess: false,
        updateConnectionAtClientIndexSuccessData: null,
        updateConnectionAtClientIndexError: true,
        updateConnectionAtClientIndexErrorData: payload,
      };
    case UPDATE_CONNECTION_AT_CLIENT_INDEX_RESET:
      return {
        ...state,
        isUpdateConnectionAtClientIndexFetching: undefined,
        updateConnectionAtClientIndexSuccess: undefined,
        updateConnectionAtClientIndexSuccessData: undefined,
        updateConnectionAtClientIndexError: undefined,
        updateConnectionAtClientIndexErrorData: undefined,
      };
    default:
      return state;
  }
};
