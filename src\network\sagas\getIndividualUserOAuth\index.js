/*
 *
 */
import { call, put, takeEvery } from 'redux-saga/effects';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  INDIVIDUAL_USER_OAUTH_ERROR,
  INDIVIDUAL_USER_OAUTH_REQUEST,
  INDIVIDUAL_USER_OAUTH_SUCCESS,
} from '../../../redux/actions/getIndividualUserOAuth';

import { makeApiIndividualUserOAuth } from './api';

/**
 *
 * @param {*} action
 */
/**
 * 

/**
 *
 * @param {*} action
 */

export function* fetchIndividualUserToken(action) {
  try {
    const response = yield call(makeApiIndividualUserOAuth, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: INDIVIDUAL_USER_OAUTH_SUCCESS, payload: data });
    } else {
      yield put({ type: INDIVIDUAL_USER_OAUTH_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: INDIVIDUAL_USER_OAUTH_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchIndividualUserOauth() {
  yield takeEvery(INDIVIDUAL_USER_OAUTH_REQUEST, fetchIndividualUserToken);
}
