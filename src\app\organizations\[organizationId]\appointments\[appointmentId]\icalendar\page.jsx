'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Loader } from '@/components/Loader';
import useNotification from '@/hooks/useNotification';
import { Box, Typography, Link as MuiLink, Paper } from '@mui/material';

/**
 * ICalendarDownload component
 * This component automatically downloads an iCalendar file
 */
export default function ICalendarDownload({ params }) {
  const [, openSnackbar] = useNotification();
  const { organizationId, appointmentId } = params;

  const [status, setStatus] = useState('loading');
  const isApiCalled = useRef(false);

  useEffect(() => {
    const downloadICalendar = async () => {
      if (isApiCalled.current) return;
      isApiCalled.current = true;

      try {
        const apiUrl = `/api/organizations/${organizationId}/appointments/${appointmentId}/icalendar`;

        const response = await fetch(apiUrl);

        if (!response.ok) {
          throw new Error(`API returned ${response.status}`);
        }

        const blob = await response.blob();

        // Create a URL for the blob
        const url = window.URL.createObjectURL(blob);

        // Create a hidden anchor element
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `appointment-${appointmentId}.ics`);
        document.body.appendChild(link);

        // Trigger the download
        link.click();

        // Clean up
        window.URL.revokeObjectURL(url);
        document.body.removeChild(link);

        setStatus('success');
      } catch (error) {
        console.error('Error downloading iCalendar:', error);

        openSnackbar({ variant: 'error', msg: t('failedToDownloadCalenderFile') });
        setStatus('error');
      }
    };

    downloadICalendar();
  }, [organizationId, appointmentId, openSnackbar]);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        width: '100%',
        padding: 2,
        bgcolor: 'background.default',
      }}
    >
      {status === 'loading' && <Loader active={true} />}

      {status === 'success' && (
        <Box
          sx={{
            maxWidth: '75%',
            width: '100%',
            p: 4,
            textAlign: 'center',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <Typography variant="h5" component="h1" gutterBottom>
            Download Complete
          </Typography>
          <Typography variant="body1" gutterBottom>
            Your calendar file has been downloaded. You can now close this window.
          </Typography>
          <Typography variant="body2" sx={{ mt: 0 }}>
            If the download didn&apos;t start automatically, please{' '}
            <MuiLink href={`/api/organizations/${organizationId}/appointments/${appointmentId}/icalendar`} download>
              click here
            </MuiLink>
            .
          </Typography>
        </Box>
      )}

      {status === 'error' && (
        <Box
          sx={{
            maxWidth: '70%',
            width: '100%',
            p: 4,
            textAlign: 'center',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <Typography variant="h5" component="h1" gutterBottom>
            Unable to Download Calendar
          </Typography>
          <Typography variant="body1" gutterBottom>
            We couldn&apos;t download your calendar file at this time. Please try again later or{' '}
            <MuiLink href={`/api/organizations/${organizationId}/appointments/${appointmentId}/icalendar`} download>
              click here
            </MuiLink>{' '}
            to retry
          </Typography>
        </Box>
      )}
    </Box>
  );
}
