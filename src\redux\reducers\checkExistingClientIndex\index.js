import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  CHECK_EXISTING_CLIENT_INDEX_REQUEST,
  CHECK_EXISTING_CLIENT_INDEX_SUCCESS,
  CHECK_EXISTING_CLIENT_INDEX_ERROR,
  CHECK_EXISTING_CLIENT_INDEX_RESET,
} from '../../actions/checkExistingClientIndex';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const checkExistingClientIndexReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case CHECK_EXISTING_CLIENT_INDEX_REQUEST:
      return {
        ...state,
        isCheckExistingClientIndexFetching: true,
        checkExistingClientIndexSuccess: false,
        checkExistingClientIndexError: false,
        checkExistingClientIndexErrorData: null,
      };
    case CHECK_EXISTING_CLIENT_INDEX_SUCCESS: {
      return {
        ...state,
        isCheckExistingClientIndexFetching: false,
        checkExistingClientIndexSuccess: true,
        checkExistingClientIndexError: false,
        checkExistingClientIndexErrorData: null,
        checkExistingClientIndexSuccessData: payload,
      };
    }
    case CHECK_EXISTING_CLIENT_INDEX_ERROR:
      return {
        ...state,
        isCheckExistingClientIndexFetching: false,
        checkExistingClientIndexSuccess: false,
        checkExistingClientIndexSuccessData: null,
        checkExistingClientIndexError: true,
        checkExistingClientIndexErrorData: payload,
      };
    case CHECK_EXISTING_CLIENT_INDEX_RESET:
      return {
        ...state,
        isCheckExistingClientIndexFetching: undefined,
        checkExistingClientIndexSuccess: undefined,
        checkExistingClientIndexSuccessData: undefined,
        checkExistingClientIndexError: undefined,
        checkExistingClientIndexErrorData: undefined,
      };
    default:
      return state;
  }
};
