import { runSaga } from 'redux-saga';
import * as api from './api';
import * as actions from '../../../redux/actions/getRegistrationQuestions/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

import { fetchRegistrationQuestions } from './index';
/**
 * This function test test case for get getRegistrationQuestions details saga
 * Fires get getRegistrationQuestions success of api gives success
 * Fires get getRegistrationQuestions error of api fails
 */

describe('getRegistrationQuestions', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiFetchRegistrationQuestions = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchRegistrationQuestions(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchRegistrationQuestions, requestResult).done;

    let successResult = actions.fetchRegistrationQuestionsSuccess(DUMMY_ITEM.data);

    expect(api.makeApiFetchRegistrationQuestions.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchRegistrationQuestions = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchRegistrationQuestions(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchRegistrationQuestions, requestResult).done;

    expect(api.makeApiFetchRegistrationQuestions.mock.calls.length).toBe(1);

    let errorResult = actions.fetchRegistrationQuestionsError(DUMMY_ITEM.data);
    expect(dispatchedActions).toContainEqual(errorResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchRegistrationQuestions = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.fetchRegistrationQuestions(DUMMY_ITEM.data);
    await runSaga(fakeStore, fetchRegistrationQuestions, requestResult).done;

    expect(api.makeApiFetchRegistrationQuestions.mock.calls.length).toBe(1);

    let resetResult = actions.fetchRegistrationQuestionsReset();
    const expectedAction = {
      type: actions.GET_REGISTRATION_QUESTIONS_RESET,
    };
    expect(expectedAction).toEqual(resetResult);
  });
});
