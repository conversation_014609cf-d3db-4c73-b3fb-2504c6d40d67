/*
 *
 */

import { call, put, takeEvery } from 'redux-saga/effects';

import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';
import {
  SEARCH_AND_CREATE_PATIENT_ERROR,
  SEARCH_AND_CREATE_PATIENT_REQUEST,
  SEARCH_AND_CREATE_PATIENT_SUCCESS,
} from '../../../redux/actions/searchAndCreatePatient';

import { searchAndCreatePatientDataApi } from './api';

/**
 *
 * @param {*} action
 */
export function* searchAndCreatePatient(action) {
  try {
    const response = yield call(searchAndCreatePatientDataApi, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: SEARCH_AND_CREATE_PATIENT_SUCCESS, payload: data });
    } else {
      yield put({ type: SEARCH_AND_CREATE_PATIENT_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: SEARCH_AND_CREATE_PATIENT_ERROR });
  }
}

// Our watcher Saga:
export function* watchSearchAndCreatePatient() {
  yield takeEvery(SEARCH_AND_CREATE_PATIENT_REQUEST, searchAndCreatePatient);
}
