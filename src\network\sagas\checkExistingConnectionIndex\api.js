import { CHECK_EXISTING_CONNECTION_INDEX } from '@/utils/constants/awsApiEndpoints';
import { makeNetworkCall } from '../..';
import { INCLUDE_SHARE_ATTRIBUTES, INDIVIDUAL, INDIVIDUAL_ID, ORGANI<PERSON>ATION_ID } from '@/utils/constants';

export function fetchCheckExistingConnectionIndex(action) {
  const { payload = {} } = action || {};
  const { headers = {}, organizationId, individualId, includeSharedAttributes } = payload || {};

  const URL = CHECK_EXISTING_CONNECTION_INDEX.replace(ORGANIZATION_ID, organizationId)
    .replace(INDIVIDUAL_ID, individualId)
    .replace(INCLUDE_SHARE_ATTRIBUTES, includeSharedAttributes);

  const config = {
    targetAwsEnv: INDIVIDUAL,
    method: 'GET',
    url: URL,
    headers: headers,
    // data: checkEmailData,
  };
  return makeNetworkCall(config);
}
