/*
 *
 */
import { makeNetworkCall } from '../..';
import { BASE_URL, CHECK_EMAIL } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */

export function verifyEmailInBooking(action) {
  const { payload = {} } = action || {};
  const { headers = {}, checkEmailData = {} } = payload || {};
  const END_POINT = CHECK_EMAIL;
  const config = {
    method: 'POST',
    url: BASE_URL + END_POINT,
    headers: headers,
    data: checkEmailData,
  };
  return makeNetworkCall(config);
}
