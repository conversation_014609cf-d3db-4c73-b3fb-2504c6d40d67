import { call, put, takeEvery } from 'redux-saga/effects';
import {
  GET_REPORT_DATA_FOR_PDF_REQUEST,
  GET_REPORT_DATA_FOR_PDF_SUCCESS,
  GET_REPORT_DATA_FOR_PDF_ERROR,
} from '../../../redux/actions/getReportDataForPdf';
import { API_RESPONSE_SUCCESS } from '@/utils/constants/apiCodes';
import { makeApiGetReportDataForPdf } from './api';

/**
 *
 * @param {*} action
 */

export function* fetchGetReportDataForPdf(action) {
  try {
    const response = yield call(makeApiGetReportDataForPdf, action);
    const { data = {} } = response;
    if (response.status === API_RESPONSE_SUCCESS) {
      yield put({ type: GET_REPORT_DATA_FOR_PDF_SUCCESS, payload: data });
    } else {
      yield put({ type: GET_REPORT_DATA_FOR_PDF_ERROR, payload: data });
    }
  } catch (error) {
    yield put({ type: GET_REPORT_DATA_FOR_PDF_ERROR });
  }
}

/**
 * Our watcher Saga:
 */
export function* watchGetReportDataForPdf() {
  yield takeEvery(GET_REPORT_DATA_FOR_PDF_REQUEST, fetchGetReportDataForPdf);
}
