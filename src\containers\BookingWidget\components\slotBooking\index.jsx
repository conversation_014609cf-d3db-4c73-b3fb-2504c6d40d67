import { useEffect, useMemo, useState } from 'react';
import { Paper, Grid, Stack, Button, Typography, Alert, Box } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { fetchBookingSlots } from '@/redux/actions/getBookingSlots';
import { Loader } from '@/components';
import { Slots } from './Slots';
import { Calendar } from './Calendar';
import { HeaderSummary } from '../../HeaderSummary';
import * as CommonUtility from '@/containers/commonUtility';
import { getLocationPhoneNumber, getMaxAllowedDateForService, getNextAvailableSlot } from '../../bookingUtility';
import { convertInDateFormat, formatDateName } from '@/utils/helpers/date-time';
import useNotification from '@/hooks/useNotification';
import { actions } from '@/containers/commonConstants';
import { useTranslation } from 'react-i18next';
import { format, isBefore } from 'date-fns';

const alertMessagesMap = {
  bothDateAvailable:
    '<full_name>, based on your immunization records in our system, your <service> must be booked between <earliest_date> and <latest_date>. If you have any questions, or are unable to find a suitable appointment time, please call this clinic directly to book at <clinic_phone_number>.',
  earliestDateAvailable:
    '<full_name>, based on your immunization records in our system, your <service> must be booked after <earliest_date>. If you have any questions, or are unable to find a suitable appointment time, please call this clinic directly to book at <clinic_phone_number>.',
  latestDateAvailable:
    '<full_name>, based on your immunization records in our system, your <service> must be booked before <latest_date>. If you have any questions, or are unable to find a suitable appointment time, please call this clinic directly to book at <clinic_phone_number>.',
  notAllowedMessage: 'Please call your local health centre to book an appointment',
};

export const SlotBooking = (props) => {
  console.log('TRACE: SlotBooking');
  const {
    headerSummary,
    demographic,
    handleNavigationCallback,
    handleEditNavigationCallback,
    selectedService,
    selectedLocation,
    isRescheduleAppointment,
    smartDates,
    smartDatesStatus,
  } = props;
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const [msg, sendNotification] = useNotification();
  const [selectedTime, setSelectedTime] = useState(null);
  const [appointmentTime, setAppointmentTime] = useState(null);
  const [slot, setSlot] = useState(null);
  const [alertMessage, setAlertMessage] = useState('');

  const locationPhoneNumber = getLocationPhoneNumber(selectedLocation);

  const { widgetDetailsSuccessData } = useSelector((state) => state.widgetDetailsReducer);
  const { services } = widgetDetailsSuccessData || {};
  const maxAllowedDateForSelectedService = getMaxAllowedDateForService(selectedService, services);

  const [minDate, maxDate] = useMemo(() => {
    const dates = Array(2).fill(null);
    if (smartDates?.earliestDate && ['PRESENT', 'FUTURE'].includes(smartDatesStatus?.earliestDateStatus)) {
      dates[0] = smartDates.earliestDate;
    }

    if (smartDates?.latestDate && ['PRESENT', 'FUTURE'].includes(smartDatesStatus?.latestDateStatus)) {
      dates[1] = smartDates.latestDate;
    }

    return dates;
  }, [smartDates]);

  const { isNextAvailableSlotSuccess, nextAvailableSlotSuccessData } = useSelector(
    (state) => state.getNextAvailableSlotReducer,
  );

  const { isBookingSlotsFetching, fetchBookingSlotsError, bookingSlotsErrorData, bookingSlotsSuccessData } =
    useSelector((state) => state.bookingSlotsReducer);

  const { entry } = nextAvailableSlotSuccessData || {};

  const [date, setDate] = useState(
    isNextAvailableSlotSuccess && getNextAvailableSlot(entry, selectedLocation)
      ? new Date(getNextAvailableSlot(entry, selectedLocation))
      : new Date(),
  );

  const prepareMessage = (template) => {
    let preparedMessage = template;
    const { firstName, lastName } = demographic?.individuals?.[0] || {};
    const keyMap = {
      '<full_name>': `<b>${CommonUtility.getFullName(firstName, lastName)}</b>`,
      '<service>': selectedService?.name,
      '<earliest_date>': smartDates.earliestDate ? format(new Date(smartDates.earliestDate), 'yyyy-MM-dd') : '',
      '<latest_date>': smartDates.latestDate ? format(new Date(smartDates.latestDate), 'yyyy-MM-dd') : '',
      '<clinic_phone_number>': locationPhoneNumber,
    };

    Object.entries(keyMap).forEach(([key, value]) => (preparedMessage = preparedMessage.replace(key, value || '')));

    return preparedMessage;
  };

  useEffect(() => {
    if (smartDates?.earliestDate && ['PRESENT', 'FUTURE'].includes(smartDatesStatus?.earliestDateStatus)) {
      setDate(smartDates.earliestDate);
    }
  }, [smartDates?.earliestDate]);

  useEffect(() => {
    if (demographic && smartDates && smartDatesStatus) {
      const { earliestDateStatus, latestDateStatus, bothDateValid } = smartDatesStatus;

      if (smartDates?.message) {
        setAlertMessage(smartDates?.message);
        return;
      }
      let preparedMessage = '';
      if (bothDateValid) {
        preparedMessage = prepareMessage(alertMessagesMap.bothDateAvailable);
      } else if (['PRESENT', 'FUTURE'].includes(earliestDateStatus)) {
        preparedMessage = prepareMessage(alertMessagesMap.earliestDateAvailable);
      } else if (['PRESENT', 'FUTURE'].includes(latestDateStatus)) {
        preparedMessage = prepareMessage(alertMessagesMap.latestDateAvailable);
      } else if (['INVALID', 'PAST'].includes(earliestDateStatus) && latestDateStatus === 'PAST') {
        preparedMessage = alertMessagesMap.notAllowedMessage;
      } else if (isBefore(maxAllowedDateForSelectedService, minDate)) {
        preparedMessage = alertMessagesMap.notAllowedMessage;
      }
      setAlertMessage(preparedMessage);
    }
  }, [smartDatesStatus]);

  const handleSetDate = (value) => {
    setDate(value);
  };

  const handleSlotSelection = ({ data, slotStartTime, slotEndTime }) => {
    setSelectedTime(slotStartTime);
    setAppointmentTime(`${slotStartTime} - ${slotEndTime}`);
    setSlot(data);
  };

  const getAppointmentTypeId = () => {
    return selectedService.id;
  };

  useEffect(() => {
    const appointmentTypeId = getAppointmentTypeId();
    const [organizationId] = CommonUtility.getOrganizationAndWidgetId();

    const locationId = selectedLocation?.resource?.id || null;
    const currentSelectedDate = convertInDateFormat(date);

    dispatch(
      fetchBookingSlots({
        slotData: {
          appointmentTypeId: appointmentTypeId,
          startDate: currentSelectedDate,
        },
        locationId,
        organizationId,
      }),
    );
  }, [date]);

  useEffect(() => {
    setSelectedTime(null);
  }, [isBookingSlotsFetching]);

  useEffect(() => {
    if (fetchBookingSlotsError && bookingSlotsErrorData) {
      sendNotification({ msg: t('apiError'), variant: 'error' });
    }
  }, [fetchBookingSlotsError, bookingSlotsErrorData]);

  const handleSlotNavigationCallback = () => {
    const appointmentData = {
      slotData: slot,
      appointmentDate: formatDateName(date),
      appointmentTime,
    };
    handleNavigationCallback(actions.NEXT, appointmentData);
  };

  const getHeaderSummary = () => {
    if (isRescheduleAppointment) {
      return (
        <HeaderSummary
          headerSummary={headerSummary}
          demographic={demographic}
          handleRescheduleSlotNavigation={(action, editType, demographic) =>
            handleEditNavigationCallback(action, editType, demographic)
          }
          isRescheduleAppointment={isRescheduleAppointment}
        />
      );
    } else {
      return (
        <HeaderSummary
          headerSummary={headerSummary}
          handleNavigationCallback={(action, editType, demographic) =>
            handleEditNavigationCallback(action, editType, demographic)
          }
          demographic={demographic}
        />
      );
    }
  };

  return (
    <Grid sx={{ mt: 2, mx: '4%', mb: 2 }}>
      <Loader active={isBookingSlotsFetching} />
      <Paper sx={{ px: 2, mb: 3 }}>{getHeaderSummary()}</Paper>
      {alertMessage && (
        <Box sx={{ my: 2 }}>
          <Alert severity="error">
            <Typography variant="body1" dangerouslySetInnerHTML={{ __html: alertMessage }} />
          </Alert>
        </Box>
      )}
      <Paper sx={{ p: 2 }}>
        <Grid container>
          <Grid item xs={12}>
            <Typography variant="h5" sx={{ mb: 1 }}>
              {t('selectDateTime')}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={5}>
            <Calendar
              date={date}
              handleSetDate={handleSetDate}
              minDate={minDate}
              maxDate={maxDate}
              maxAllowedDateForSelectedService={maxAllowedDateForSelectedService}
            />
          </Grid>
          {!isBefore(maxAllowedDateForSelectedService, minDate) && (
            <Grid item xs={12} sm={6} md={7}>
              <Slots
                date={date}
                selectedTime={selectedTime}
                selectedLocation={selectedLocation}
                handleSlotSelection={handleSlotSelection}
                slotsData={bookingSlotsSuccessData}
                isBookingSlotsFetching={isBookingSlotsFetching}
              />
            </Grid>
          )}
        </Grid>
      </Paper>
      <Stack direction="column" alignItems="center" spacing={2} sx={{ pt: 0, px: 2, pb: 2, mt: 3 }}>
        <Button variant="contained" onClick={handleSlotNavigationCallback} disabled={!(!!date && !!appointmentTime)}>
          {t('next')}
        </Button>
      </Stack>
    </Grid>
  );
};
