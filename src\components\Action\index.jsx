import React, { useEffect, useState } from 'react';
import {
  actions,
  REDIRECT_TO_URL,
  REDIRECT_TO_WIDGET,
  CALL_BACKGROUND_SERVICE,
  PAGE,
  redirectTargets,
} from '@/containers/commonConstants';
import {
  getOrganizationAndWidgetId,
  getWidgetUrl,
  openInSameIFrame,
  openUrlInNewTab,
  openUrlInSameTab,
  appendIncomingParametersToUrl,
  performBackgroundServiceAction,
  openInNewWindow,
} from '@/containers/commonUtility';
import { HeadingAndDescription } from '..';

export const Action = (props) => {
  console.log('TRACE: Action');
  const {
    handleNavigationCallback,
    matchedAction,
    demographic,
    setIsActionPerformed,
    // below two are required for questionnaire actions
    setIsHeadingDescriptionAction,
    questionnaireResponse,
  } = props;

  const [showDescription, setShowDescription] = useState(false);

  const {
    appendIncomingParameters,
    redirectUrl,
    selectedAction,
    backgroundServiceEndpoint,
    selectedWidget,
    selectedWidgetType,
    selectedTarget,
    actionHeading,
    actionDescription,
  } = matchedAction || {};

  useEffect(() => {
    if (selectedAction) {
      if (selectedAction === PAGE) {
        if (!actionHeading && !actionDescription) {
          setIsActionPerformed(true);
          handleActionNavigation();
        } else {
          setShowDescription(true);
          setIsHeadingDescriptionAction && setIsHeadingDescriptionAction(true);
        }
      } else if (selectedAction === REDIRECT_TO_URL) {
        const url = appendIncomingParameters ? appendIncomingParametersToUrl(redirectUrl) : redirectUrl;
        performRedirection(selectedTarget, url);
      } else if (selectedAction === REDIRECT_TO_WIDGET) {
        let redirectUrl = getWidgetUrl(selectedWidgetType, selectedWidget?.id);
        performRedirection(selectedTarget, redirectUrl);
      } /* else if (selectedAction === REDIRECT_TO_ANOTHER_WIDGET_IN_IFRAME) {
        let redirectUrl = getWidgetUrl(selectedWidgetType, selectedWidget?.id);
        performRedirection(OPEN_IN_SAME_IFRAME, redirectUrl);
      }  */ else if (selectedAction === CALL_BACKGROUND_SERVICE) {
        performBackgroundServiceAction(backgroundServiceEndpoint, appendIncomingParameters);
        setIsActionPerformed(true);
        handleActionNavigation();
      } else {
        setIsActionPerformed(true);
        handleActionNavigation();
      }
    } else {
      setIsActionPerformed(true);
      handleActionNavigation();
    }
  }, [selectedAction]);

  console.log('matched action', matchedAction);

  const getURLParams = () => {
    console.log('Trace Action: Demographics', demographic);
    const params = [];

    if (matchedAction?.demographicFieldsAdd && demographic?.individuals && demographic?.contact) {
      params.push(`id=${demographic.id}`);
      params.push(`resourceType=${demographic.resourceType}`);

      if (demographic.individuals?.length) {
        Object.entries(demographic.individuals[0]).forEach(([key, value]) => {
          params.push(`${key}=${value}`);
        });
      }

      if (demographic.contact) {
        Object.entries(demographic.contact).forEach(([key, value]) => {
          params.push(`${key}=${value}`);
        });
      }

      params.push(`preferredContactMethod=${demographic.preferredContactMethod}`);
      params.push(`demographicPageShow=true`);

      const [, widgetId] = getOrganizationAndWidgetId();
      const questionnaireWidgetId = widgetId.split('?')[0];
      if (questionnaireWidgetId) {
        params.push(`questionnaireWidgetId=${questionnaireWidgetId}`);
      }
    }

    if (questionnaireResponse) {
      const questionnaireResponseId = questionnaireResponse?.id;
      params.push(`qrid=${questionnaireResponseId}`);
    }

    const queryString = params.length ? `?${params.join('&')}` : '';
    return queryString;
  };

  const performRedirection = (target, redirectUrl) => {
    const params = getURLParams();
    if (!target || !redirectUrl) return;
    switch (target) {
      case redirectTargets.SAME_TAB:
        openUrlInSameTab(redirectUrl, params);
        break;
      case redirectTargets.NEW_TAB:
        openUrlInNewTab(redirectUrl, params);
        setIsActionPerformed(true);
        handleActionNavigation();
        break;
      case redirectTargets.SAME_IFRAME:
        openInSameIFrame(redirectUrl, params);
        break;
      case redirectTargets.NEW_WINDOW:
        openInNewWindow(redirectUrl, params);
        setIsActionPerformed(true);
        handleActionNavigation();
        break;
      default:
        setIsActionPerformed(true);
        handleActionNavigation();
        break;
    }
  };

  const handleActionNavigation = () => {
    handleNavigationCallback(actions.NEXT);
  };

  return (
    <>
      {showDescription && (
        <HeadingAndDescription
          // handleNavigationCallback={() => handleActionNavigation()}
          headingAndDescriptionData={{ heading: actionHeading, description: actionDescription }}
          showNextButton={false}
        />
      )}
    </>
  );
};
