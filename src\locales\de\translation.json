{"apiError": "Sorry, there was a problem with your request.", "otpSendError": "Sorry there was some problem in sending OTP", "invalidOtp": "Invalid OTP", "multiplePatientError": "Found multiple patient with same detail", "invalidHealthCardNumber": "Invalid health card number", "clientCreationDisallowedError": "Failed to create client record. Client creation disallowed.", "clientCreationDataNotExistsError": "It seems there was an issue with the data you entered. Please ensure that the information matches exactly as it appears on your government-issued identification.", "failedToDownloadCalenderFile": "Failed to download the calendar file", "someErrorOccurred": "Some error occurred", "errorGettingUserLocation": "Error getting user location", "pleaseAllowUsLocationPermission": "Please allow us location permission", "loading": "Loading...", "none": "None", "individual": "Individual", "bookNow": "Book Now", "bookAgain": "Book Again", "searchALocation": "Enter a Location", "selectALocation": "Select a Location", "bookingAppointment": "Book Appointment", "bookNewAppointment": "Book New Appointment", "cancelAppointment": "<PERSON>cel Appointment", "cancelMainHeading": "Do you want to cancel your appointment?", "cancelSubHeading": "If you proceed, the following appointment will be cancelled.", "cancelSummaryHeading": "Appointment Cancelled", "cancelSummarySubHeading": "The following appointment has been cancelled.", "backToAppointmentSummary": "Back To Appointment Summary", "rescheduleAppointment": "Reschedule Appointment", "cancelledAppointmentMessage": "This appointment has already been cancelled, please book a new appointment.", "rescheduleNow": "Reschedule Now", "rescheduleSubHeading": "If you proceed, the following appointment will be rescheduled.", "rescheduleConfirmation": "Reschedule Confirmation", "rescheduleMessage": "If you have added previous appointment to your calendar, you will need to <strong>remove or change</strong> that appointment since it is no longer valid", "next": "Next", "noSlots": "No appointments available for selected date", "appointmentDetails": "Appointment Details", "service": "Service", "appointment": "Appointment", "location": "Location", "selectService": "Select Service", "selectAppointment": "Select Appointment", "selectDateTime": "Select Date and Time", "dateAndTime": "Date and Time", "addToCalendar": "Add to Calendar", "makeChanges": "Make Changes", "otpSent": "A verification code has been sent", "emailAddress": "Email Address", "phoneNumber": "Phone Number", "select": "Select", "edit": "Edit", "gmtFormat": "G\\MTZ", "reviewDetails": "Review Details", "appointmentCancelled": "Appointment Cancelled", "appointmentCancelledText": "The following appointment has been cancelled.", "dateFormat": "2021-01-15T", "moreDetails": "More Details", "lessDetails": "Less Details", "yourInformationVerifiedSuccessfully": "Your information has been verified successfully", "failedToVerifyInformation": "Failed to verify information", "contactInformation": "Contact Information", "back": "Back", "addIndividual": "Add Individual", "personalInformation": "Personal Information", "organizationUserCambianUser": "Organization User: Cambian User", "clientSearch": "Client Search", "otpError": "We are having trouble verifying that code. Please try again.", "existingAccountError": "This email is already in use for an existing account. Please sign-in or reset the password", "otpValidationSubHeadingEmail": "If you don't see it, check your spam folder", "otpValidationSubHeadingPhone": "Enter the verification code we sent to your mobile phone.", "VerificationCodeSentTo": "Enter the verification code sent to your ", "didNotReceiveOTP": "Didn't receive the verification code?", "resendEmail": "<PERSON><PERSON><PERSON>", "resendText": "Resend Text", "resendOtp": "Resend OTP", "resendCode": "Resend code", "goBack": "Go Back", "toEnterNewEmail": "to enter a new email address", "toEnterNewPhone": "to enter a new phone number", "confirmationNeeded": "Confirmation Needed", "verificationCode": "Verification Code", "changeYourEmail": "Change your email", "changeYourPhone": "Change your phone number", "submit": "Submit", "requiredField": "Required", "emailDuplicate": "Email already exists", "phoneNumberDuplicate": "Phone already exists", "allowNotificationsValid": "Invalid notification preference", "subscribeToNotificationsValid": "Select Yes or No", "dateValid": "Invalid date", "healthCardNumber": "Health Card Number", "personalHealthNumber": "Personal Health Number", "vaccinationHistory": "Vaccination History", "upcomingAppointments": "Upcoming Appointments", "dose": "<PERSON><PERSON>", "serviceSuggestionText": "Based on your age and vaccination history, you are eligible to receive one of the following options below:", "reschedule": "Reschedule", "cancel": "Cancel", "thankYouDescription1": "To see your completed questionnaire, you need to register for a Cambian Navigator account. You can do this by clicking the link in the email we just sent to you and entering the following PIN when requested.", "thankYouDescription2": "Thank you for filling out the questionnaire.", "pin": "PIN", "print": "Print", "ok": "OK", "noServicesForYou": "Sorry, you are not eligible for any services.", "printReport": "Print Report", "downloadReport": "Download Report", "agreeToConsent": "I have read and agree to the", "consentAgreement": "Consent Agreement", "outlook": "Outlook", "firstNameValid": "Letters only", "firstNameValidationMin": "Min 2 characters", "middleNameValid": "Letters only", "middleNameValidationMin": "Min 2 characters", "lastNameValid": "Letters only", "lastNameValidationMin": "Min 2 characters", "emailValidationText": "Invalid email", "phoneNumberValid": "Invalid phone", "phoneNumberLengthValidationText": "Must be 6-14 digits", "addressValidationMin": "Min 2 characters", "countryValidationMin": "Min 2 characters", "cityValidationMin": "Min 2 characters", "provinceValidationMin": "Min 2 characters", "postalCodeValidationMin": "Min 6 characters", "bcPersonalHealthNumberMin": "Must be 10 digits", "bcPersonalHealthNumberValid": "Numbers only", "ytPersonalHealthNumberMin": "Format: 00XXXXXXX", "ytPersonalHealthNumberValid": "Numbers only", "signIn": "Sign in", "login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "registerAndConnect": "Register and Connect", "registration": "Registration", "haveCambianAccount": "Have you used Cambian before ?", "signInToYourAccount": "Sign in to your account", "useYourCambianAccount": "Use your Cambian Account", "forgotPassword": "Forgot your password ?", "dontHaveCambianAccount": "Don't have a Cambian account ?", "registerAccount": "Register for one", "thankYou": "Thank you", "anEmailSent": "An Email has been sent to", "registrationMessageSubText1": "When you register, we send a confirmation message entitled \"Confirm connection request\" to the email account you have provided. If you click the link in that message, your email address will be verified.", "registrationMessageSubText2": "The confirmation email will come from Cambian and you will be asked to choose a password so that you can use the service.", "feelFreeToContact": "Feel free to contact us if you have any questions.", "saveLaterHeading": "Save for later", "saveLaterDescription": "Your response has been saved. You can continue your response by signing into your Cambian Navigator account.", "discardHeading": "Delete", "discardDescription": "Response has been deleted.", "registrationMessageDescription": "Connection request sent! Check your Navigator account for the invite to connect with us.", "preferredContactMethod": "Preferred Contact Method", "email": "Email", "phone": "Phone", "publicHealthJurisdiction": "Public Health Jurisdiction", "firstNamePlaceholder": "First Name", "lastNamePlaceholder": "Last Name", "middleNamePlaceholder": "Middle Name", "emailPlaceholder": "Email", "phonePlaceholder": "Phone Number", "addressPlaceholder": "Address", "addressLine1Placeholder": "Address Line 1", "addressLine2Placeholder": "Address Line 2", "countryPlaceholder": "Country", "cityPlaceholder": "City", "provincePlaceholder": "Province", "postalCodePlaceholder": "Postal Code", "personalHealthNumberPlaceholder": "Personal Health Number", "consentAgreementError": "Failed to retrieve the consent agreement. Please try again later", "individualNotFoundHeading": "Client not found", "individualNotFoundDescription": "Please get in touch with us", "Book an Appointment at a location near you": "Book an Appointment at a location near you", "Search by address, city or postal code": "Search by address, city or postal code", "Use my location": "Use my location", "Radius": "<PERSON><PERSON>", "additionalInformation": "Additional Information", "confirmation": "Confirmation", "confirmAppointment": "Confirm Appointment", "preConfirmationMessage": "A reminder will be sent to you before your appointment", "confirmationMessage": "This appointment is confirmed", "cancelText": "This appointment has already been cancelled, please book a new appointment.", "nextStep": "Next Step", "Manage Notification Preferences": "Manage Notification Preferences", "subscribeToNotifications": "Subscribe to Notifications", "Receive notifications about this appointment, including reminders.": "Receive notifications about this appointment, including reminders.", "Submit": "Submit", "Manage Notification": "Manage Notification", "noClinicsFound": "No clinics found. Try expanding the radius or choosing a different location.", "pleaseVerifyYourInformation": "Please verify your information", "appointmentCancellationMessage": "This appointment has been cancelled", "appointmentCancelModalHeaderText": "<PERSON>cel Appointment", "appointmentCancelModalMessage": "Please confirm your preference", "appointmentCancelModalCancelText": "Keep Appointment", "appointmentCancelModalActionText": "<PERSON>cel Appointment", "dynamicIdError": "Dynamic ID Error", "questionnaireIdIsInvalid": "Questionnaire ID is invalid", "requestNotFound": "Request not found", "Please get in touch with us": "Please get in touch with us", "confirmationPageHeading": "Manage appointment", "appointmentNotFoundHeading": "<PERSON><PERSON>in nicht gefunden", "appointmentNotFoundDescription": "Please get in touch with us", "confirmationPageDescription": "After identifying yourself with your personal information, you will be able to confirm, reschedule, or cancel your appointment.", "Active connection already exists": "Active connection already exists", "Change language": "Change language", "English": "English", "French": "Français", "Widget": "Widget", "previous": "Previous", "done": "Done", "yes": "Yes", "no": "No", "gender": "Gender", "male": "Male", "unknown": "Unknown", "female": "Female", "other": "Other", "dateOfBirth": "Date of Birth", "address": "Address", "Add ID": "Add ID", "Verify your email address": "Verify your email address", "Verify your phone number": "Verify your phone number", "Enter the verification code sent to": "Enter the verification code sent to", "If you do not see it, check your spam folder.": "If you do not see it, check your spam folder.", "Enter your verification code": "Enter your verification code", "Cancel": "Cancel", "Next": "Next", "Did not get the code?": "Did not get the code?", "Resend the code": "Resend the code", "Sign out": "Sign out", "Address Line 1": "Address Line 1", "Address Line 2": "Address Line 2", "City": "City", "Province": "Province", "Country": "Country", "Postal Code": "Postal Code", "Type": "Type", "Issuer": "Issuer", "ID": "ID", "Delete Address": "Delete Address", "Add Address": "Add Address", "addEmail": "Add <PERSON>", "addPhone": "Add Phone", "idMinLength": "Min 2 characters", "Enter Verification Code": "Bestätigungscode eingeben", "A verification code has been sent to ": "Ein Bestätigungscode wurde an ", ". Please enter it to verify your email address.": " gesendet. <PERSON>te geben Si<PERSON> ihn ein, um Ihre E-Mail-Adresse zu bestätigen.", ". Please enter it to verify your phone number.": " gesendet. Bitte geben Si<PERSON> ihn ein, um Ihre Telefonnummer zu bestätigen.", "Verification code": "Bestätigungscode", "We are having trouble verifying that code. Please try again.": "Wir haben Probleme, diesen Code zu überprüfen. Bitte versuchen Sie es erneut.", "Verify": "Bestätigen", "Click here": "<PERSON><PERSON> klicken", "Click to verify": "Zum Bestätigen klicken", "Something went wrong. Please try again.": "Etwas ist schiefgelaufen. Bitte versuchen Sie es erneut.", "Verified": "Bestätigt", "Primary": "<PERSON><PERSON><PERSON><PERSON>", "Set as Primary": "Als primär festlegen", "Delete email": "E-Mail löschen", "Delete phone": "Telefonnummer löschen", "Resend Verification Code": "Bestätigungscode erneut senden", "You must verify your email to continue. ": "You must verify your email to continue. ", "You must verify your phone to continue. ": "You must verify your phone to continue. "}