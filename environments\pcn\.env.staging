# This is for the abpcn-dev account

NEXT_PUBLIC_BASE_URL = https://abpcn-dev.dhklygvimcb6n.amplifyapp.com

# * APIs variables
NEXT_PUBLIC_WIDGET_SERVICES_BASE_URL=https://wzxethyp8k.execute-api.eu-north-1.amazonaws.com/staging

# Schedular Booking API Gateway
NEXT_PUBLIC_BOOKING_SERVICES_BASE_URL=

# Artifact repository 

# Private Artifact repository
NEXT_PUBLIC_ARTIFACT_PRIVATE_REPOSITORY_BASE_URL=https://i0ycrnfvs7.execute-api.ca-central-1.amazonaws.com/staging

# Public Artifact repository
NEXT_PUBLIC_ARTIFACT_PUBLIC_REPOSITORY_BASE_URL=https://5xh0is2uk0.execute-api.ca-central-1.amazonaws.com/staging

# CDRs
NEXT_PUBLIC_INDIVIDUAL_CDR_BASE_URL=https://as235s5m8e.execute-api.ca-central-1.amazonaws.com/staging

NEXT_PUBLIC_ORGANIZATION_CDR_BASE_URL=https://syt9r1f0a1.execute-api.ca-central-1.amazonaws.com/staging

# Indexes
NEXT_PUBLIC_CONNECTION_INDEX_BASE_URL=https://91buhj4y91.execute-api.ca-central-1.amazonaws.com/staging

NEXT_PUBLIC_CLIENT_INDEX_BASE_URL=https://8iw3oz8k0a.execute-api.ca-central-1.amazonaws.com/staging

NEXT_PUBLIC_PDF_GENERATION_SERVICE_BASE_URL=https://vi57uqaxyk.execute-api.ca-central-1.amazonaws.com/staging

NEXT_PUBLIC_ORGANIZATION_MESSAGING_SERVICE_BASE_URL=https://33rpdbkv65.execute-api.ca-central-1.amazonaws.com/staging

NEXT_PUBLIC_ORGANIZATION_INDIVIDUAL_SERVICE_BASE_URL=https://fq53ljixj1.execute-api.ca-central-1.amazonaws.com/staging

NEXT_PUBLIC_PRACTITIONER_INDEX_IDENTIFIER=
NEXT_PUBLIC_PRACTITIONER_INDEX_STAGE=

NEXT_PUBLIC_LOCATION_INDEX_IDENTIFIER=
NEXT_PUBLIC_LOCATION_INDEX_STAGE=

NEXT_PUBLIC_AWS_SERVICES_ENVIRONMENT=dev

# Allow access to services in Org Env
COGNITO_ORG_MACHINE_USER_POOL_ID=ca-central-1_rcST4zA6h
COGNITO_ORG_MACHINE_APP_CLIENT_ID=26kk22rbvtq6ast1ar2ua56d56
COGNITO_ORG_MACHINE_USERNAME=19825ca714a4044677274a0315cf34d8

# Allow access to services in Network Env
COGNITO_NETWORK_MACHINE_USER_POOL_ID=ca-central-1_MRn95Cwg2
COGNITO_NETWORK_MACHINE_APP_CLIENT_ID=21fbvueqabib9h4sokas59am07
COGNITO_NETWORK_MACHINE_USERNAME=58e73daac5e122274c507a5e4134dbfd

# Allow access to services in Individual Env
COGNITO_INDIVIDUAL_MACHINE_USER_POOL_ID=ca-central-1_3zAkcSxl2
COGNITO_INDIVIDUAL_MACHINE_APP_CLIENT_ID=5ridjdglr7mvqhcf4085occbia
COGNITO_INDIVIDUAL_MACHINE_USERNAME=15a9fbbc78219886c38e1f122043f7aa

# Cognito User auth variables
COGNITO_INDIVIDUAL_HUMAN_REGION=ca-central-1
COGNITO_INDIVIUDAL_HUMAN_USER_POOL_ID=ca-central-1_t5XYRZzvT
COGNITO_INDIVIUDAL_HUMAN_APP_CLIENT_ID=1o6f912kklrpao3i289a2qbum

NEXTAUTH_URL=$NEXT_PUBLIC_BASE_URL
