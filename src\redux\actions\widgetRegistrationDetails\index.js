/*
 * WIDGET_REGISTRATION_DETAILS action types
 */

export const WIDGET_REGISTRATION_DETAILS_REQUEST = 'WIDGET_REGISTRATION_DETAILS_REQUEST';
export const WIDGET_REGISTRATION_DETAILS_SUCCESS = 'WIDGET_REGISTRATION_DETAILS_SUCCESS';
export const WIDGET_REGISTRATION_DETAILS_ERROR = 'WIDGET_REGISTRATION_DETAILS_ERROR';
export const WIDGET_REGISTRATION_DETAILS_RESET = 'WIDGET_REGISTRATION_DETAILS_RESET';

/*
 * action creators
 */

export function fetchWidgetRegistrationDetails(data) {
  return {
    type: WIDGET_REGISTRATION_DETAILS_REQUEST,
    payload: data,
  };
}

export function getWidgetRegistrationDetailsSuccess(data) {
  return {
    type: WIDGET_REGISTRATION_DETAILS_SUCCESS,
    payload: data,
  };
}

export function getWidgetRegistrationDetailsError() {
  return {
    type: WIDGET_REGISTRATION_DETAILS_ERROR,
  };
}

export const resetWidgetRegistrationDetails = () => {
  return {
    type: WIDGET_REGISTRATION_DETAILS_RESET,
  };
};
