/*
 *RESCHEDULE_APPOINTMENT action types
 */

export const RESCHEDULE_APPOINTMENT_REQUEST = 'RESCHEDULE_APPOINTMENT_REQUEST';
export const RESCHEDULE_APPOINTMENT_SUCCESS = 'RESCHEDULE_APPOINTMENT_SUCCESS';
export const RESCHEDULE_APPOINTMENT_ERROR = 'RESCHEDULE_APPOINTMENT_ERROR';
export const RESCHEDULE_APPOINTMENT_RESET = 'RESCHEDULE_APPOINTMENT_RESET';

/*
 * action creators
 */

export function rescheduleAppointmentDetails(data) {
  return {
    type: RESCHEDULE_APPOINTMENT_REQUEST,
    payload: data,
  };
}

export function rescheduleAppointmentActionSuccess(data) {
  return {
    type: RESCHEDULE_APPOINTMENT_SUCCESS,
    payload: data,
  };
}

export function rescheduleAppointmentActionError() {
  return {
    type: RESCHEDULE_APPOINTMENT_ERROR,
  };
}

export const resetRescheduleAppointment = () => {
  return {
    type: RESCHEDULE_APPOINTMENT_RESET,
  };
};
