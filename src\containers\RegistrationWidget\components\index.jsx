import { useEffect, useState } from 'react';
import LanguageIcon from '@mui/icons-material/Language';
import { Menu, MenuItem, Box, Button } from '@mui/material';
import { getOrganizationAndWidgetId } from '@/containers/commonUtility';
import { getSignedIndividualUserDemographic } from '@/containers/Demographics/demographicUtility';
import { resetUpdateConnectionAtClientIndex } from '@/redux/actions/updateConnectionAtClientIndex';
import { useSelector, useDispatch } from 'react-redux';
import { fetchWidgetRegistrationDetails } from '@/redux/actions/widgetRegistrationDetails';
import { Loader, HeadingAndDescription, Action, LanguageSelector } from '@/components';
import useNotification from '@/hooks/useNotification';
import { useTranslation } from 'react-i18next';
import { resetAllAPIReducers } from '../registrationUtility';
import { pages } from '../registrationConstants';
import { actions, identifications, REGISTRATION_WIDGET } from '@/containers/commonConstants';
import { useSession } from 'next-auth/react';
import { EditDemographic } from '@/containers/Demographics';
import { SignInSignUp } from '@/containers/auth/SignInSignUp';
import { resetCreateConnectionAtConnectionIndex } from '@/redux/actions/createConnectionAtConnectionIndex';
import {
  resetSearchIndividualAtClientIndex,
  searchIndividualAtClientIndex,
} from '@/redux/actions/searchIndividualAtClientIndex';
import { useCommonAPIs } from '@/hooks/useCommonAPIs';

/* 
Edge Case to Discuss with Bruce: De-Identified Workflow

Workflow Overview:
1. The user is first asked to sign in or sign up.
2. We call the client-ids API, which in the de-identified workflow always creates a new client with null details.
3. Next, we make a create connection request for the Connection Index.
4. If a connection already exists for the signed-in user, the API returns an error: 
   "Active connection exists between ... ".
5. The workflow ends here.

Issue:
- At this point, we have created a client, but the connection request failed.
- As a result, we now have an orphaned or garbage client entry in the Client Index.

Proposed Solution:
1. Delete the newly created client from the client-ids API in the de-identified workflow if the connection request fails.
2. Before calling the client-ids API, first check for any existing connection.
   - If an existing connection is found, display an appropriate message to the user.
   - Otherwise, proceed to call the client-ids API, followed by the connection API.
*/

const getFirstScreen = (introductionEnabled, identification) => {
  if (introductionEnabled) {
    return pages.INTRODUCTION;
  } else if (identification === identifications.IDENTIFIED) {
    return pages.DEMOGRAPHIC;
  } else {
    return pages.SIGN_IN_FLOW;
  }
};

export const RegistrationComponent = () => {
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const { data: session } = useSession();
  const [msg, sendNotification] = useNotification();
  const { getClientId, makeConnectionAtConnectionIndex, checkConnectionAtConnectionIndex } = useCommonAPIs();
  const [organizationId, widgetId] = getOrganizationAndWidgetId();

  const [isSignInDialogOpen, setIsSignInDialogOpen] = useState(false);
  const [demographic, setDemographic] = useState(null);
  const [currentPage, setCurrentPage] = useState();
  const [isActionPerformed, setIsActionPerformed] = useState(false);

  const handleLanguageChange = (lang) => {
    dispatch(
      fetchWidgetRegistrationDetails({
        headers: { 'Content-Type': 'application/json' },
        organizationId,
        widgetId,
        lang,
      }),
    );
  };

  const {
    isWidgetRegistrationDetailsFetching,
    widgetRegistrationDetailsSuccessData,
    fetchWidgetRegistrationDetailsError,
    widgetRegistrationDetailsErrorData,
  } = useSelector((state) => state.widgetRegistrationDetailsReducer);

  const {
    isSearchIndividualAtClientIndexFetching,
    searchIndividualAtClientIndexSuccessData,
    searchIndividualAtClientIndexError,
    searchIndividualAtClientIndexErrorData,
  } = useSelector((state) => state.searchIndividualAtClientIndexReducer);
  const {
    isCreateConnectionAtConnectionIndexFetching,
    createConnectionAtConnectionIndexSuccess,
    createConnectionAtConnectionIndexErrorData,
  } = useSelector((state) => state.createConnectionAtConnectionIndexReducer);
  const {
    checkExistingConnectionIndexSuccess,
    checkExistingConnectionIndexError,
    checkExistingConnectionIndexErrorData,
    isCheckExistingConnectionIndexFetching,
  } = useSelector((state) => state.checkExistingConnectionIndexReducer) || {};
  const { individualUserInfoSuccessData } = useSelector((state) => state.individualUserInfoReducer);

  const {
    identification,
    introduction,
    widgetType,
    selfRegistration,
    isConsentRequired,
    clientInformationPageTitle,
    clientInformationPageSubtitle,
    fields,
    showSignIn,
    finalPage,
    action,
    individualNotFoundPage,
    otpVerificationEnabled,
    byRequestOnly,
    requestNotFoundPage,
    defaultLanguage,
    availableLanguages,
    spinnerText,
  } = widgetRegistrationDetailsSuccessData || {};

  useEffect(() => {
    if (session && session?.user) {
      setDemographic(getSignedIndividualUserDemographic(demographic, session?.user, fields));
    }
  }, [session]);

  useEffect(() => {
    dispatch(
      fetchWidgetRegistrationDetails({
        headers: { 'Content-Type': 'application/json' },
        organizationId,
        widgetId,
      }),
    );
  }, []);

  useEffect(() => {
    if (
      checkExistingConnectionIndexSuccess &&
      identification === identifications.DEIDENTIFIED &&
      currentPage !== pages.INTRODUCTION
    ) {
      setIsSignInDialogOpen(false);
      setCurrentPage(pages.REGISTRATION_MESSAGE);
    }
  }, [checkExistingConnectionIndexSuccess, currentPage]);

  useEffect(() => {
    if (widgetRegistrationDetailsSuccessData) {
      setCurrentPage(getFirstScreen(introduction?.enabled, identification));
      document.title = widgetRegistrationDetailsSuccessData.widgetTitle || t('Widget');

      // Set language based on widget settings
      if (widgetRegistrationDetailsSuccessData.currentLanguage) {
        const languageToUse =
          widgetRegistrationDetailsSuccessData.currentLanguage || widgetRegistrationDetailsSuccessData.defaultLanguage;

        if (languageToUse && i18n.language !== languageToUse) {
          i18n.changeLanguage(languageToUse);
        }
      }
    }
    if (fetchWidgetRegistrationDetailsError && widgetRegistrationDetailsErrorData) {
      setCurrentPage(getFirstScreen(introduction?.enabled, identification));
      sendNotification({ msg: t('apiError'), variant: 'error' });
      resetAllAPIReducers(dispatch);
    }
  }, [widgetRegistrationDetailsSuccessData, fetchWidgetRegistrationDetailsError, widgetRegistrationDetailsErrorData]);

  const handleIntroductionNavigation = () => {
    const isIdentified = identification === identifications.IDENTIFIED;
    if (identification === identifications.IDENTIFIED) {
      setCurrentPage(pages.DEMOGRAPHIC);
      setIsSignInDialogOpen(false);
    }
    if (identification === identifications.DEIDENTIFIED) {
      if (individualUserInfoSuccessData) {
        setIsSignInDialogOpen(false);
        setCurrentPage(pages.REGISTRATION_MESSAGE);
      } else {
        setCurrentPage(pages.SIGN_IN_FLOW);
        setIsSignInDialogOpen(true);
      }
    }
    setCurrentPage(isIdentified ? pages.DEMOGRAPHIC : pages.SIGN_IN_FLOW);
    setIsSignInDialogOpen(isIdentified ? false : true);
  };

  useEffect(() => {
    if (identification === identifications.DEIDENTIFIED && currentPage === pages.SIGN_IN_FLOW) {
      setIsSignInDialogOpen(true);
    }
  }, [currentPage]);

  const handleDeIdentificationNavigation = (action) => {
    setIsSignInDialogOpen(false);
    if (action === actions.NEXT) {
      setCurrentPage(pages.REGISTRATION_MESSAGE);
    } else if (action === actions.PREVIOUS) {
      setCurrentPage(pages.INTRODUCTION);
    }
  };

  const handleDemographicNavigation = (action, demographic) => {
    setDemographic(demographic);
    if (action === actions.NEXT) {
      dispatch(resetUpdateConnectionAtClientIndex());
      setCurrentPage(pages.REGISTRATION_MESSAGE);
    }
  };

  const handleRegisterMessageNavigation = () => {
    if (action?.enabled) {
      setCurrentPage(pages.ACTION);
    }
  };

  const handleActionNavigation = (action) => {
    if (action === actions.NEXT) {
      setCurrentPage(pages.REGISTRATION_MESSAGE);
    }
  };

  const handleSignInDialogState = (state, isSignInCancelled) => {
    setIsSignInDialogOpen(state);

    if (identification === identifications.IDENTIFIED || currentPage !== pages.SIGN_IN_FLOW) return;
    if (identification === identifications.DEIDENTIFIED && isSignInCancelled && introduction.enabled)
      handleDeIdentificationNavigation(actions.PREVIOUS);

    // if (!isSignInCancelled) {
    //   const searchBody = {
    //     client: null,
    //     selfRegistration,
    //     widgetType: 'REGISTRATION_WIDGET',
    //   };
    //   dispatch(
    //     searchIndividualAtClientIndex({
    //       organizationId,
    //       searchBody,
    //     }),
    //   );
    // }
  };

  useEffect(() => {
    if (identification === identifications.DEIDENTIFIED) {
      if (
        individualUserInfoSuccessData &&
        !searchIndividualAtClientIndexSuccessData &&
        !checkExistingConnectionIndexSuccess &&
        !isSearchIndividualAtClientIndexFetching &&
        !checkExistingConnectionIndexError &&
        !isCheckExistingConnectionIndexFetching
      ) {
        checkConnectionAtConnectionIndex();
      }
      if (
        individualUserInfoSuccessData &&
        checkExistingConnectionIndexError &&
        !searchIndividualAtClientIndexSuccessData &&
        !isSearchIndividualAtClientIndexFetching
      ) {
        const searchBody = {
          client: null,
          selfRegistration,
          resyncIdTypes: '',
        };
        dispatch(
          searchIndividualAtClientIndex({
            organizationId,
            searchBody,
          }),
        );
      }
      if (searchIndividualAtClientIndexSuccessData?.data?.found && checkExistingConnectionIndexError) {
        const clientId = getClientId();
        makeConnectionAtConnectionIndex(clientId);
      }
    }
  }, [
    searchIndividualAtClientIndexSuccessData,
    individualUserInfoSuccessData,
    checkExistingConnectionIndexError,
    checkExistingConnectionIndexSuccess,
    isSearchIndividualAtClientIndexFetching,
    isCheckExistingConnectionIndexFetching,
  ]);

  useEffect(() => {
    if (createConnectionAtConnectionIndexSuccess) {
      handleDeIdentificationNavigation(actions.NEXT);
    }
    if (createConnectionAtConnectionIndexErrorData?.message?.includes('Active connection')) {
      dispatch(resetCreateConnectionAtConnectionIndex());
      dispatch(resetSearchIndividualAtClientIndex());
      handleDeIdentificationNavigation(actions.PREVIOUS);
      // sendNotification({ variant: 'error', msg: t('Active connection already exists') });
    }
  }, [createConnectionAtConnectionIndexSuccess, createConnectionAtConnectionIndexErrorData]);

  useEffect(() => {
    if (searchIndividualAtClientIndexError && searchIndividualAtClientIndexErrorData) {
      if (
        searchIndividualAtClientIndexErrorData.statusCode === 400 ||
        searchIndividualAtClientIndexErrorData.statusCode === 500
      ) {
        let errorMessage = t('apiError');

        if (
          searchIndividualAtClientIndexErrorData.errorDetails &&
          searchIndividualAtClientIndexErrorData.errorDetails.length > 0
        ) {
          const firstError = searchIndividualAtClientIndexErrorData.errorDetails[0];
          errorMessage = firstError.message || errorMessage;
        }

        sendNotification({ msg: errorMessage, variant: 'error' });
      }
    }
  }, [searchIndividualAtClientIndexError, searchIndividualAtClientIndexErrorData]);

  const getPage = () => {
    let page;

    if (isWidgetRegistrationDetailsFetching) {
      return <Loader />;
    }

    if (currentPage === pages.INTRODUCTION) {
      page = (
        <HeadingAndDescription
          headingAndDescriptionData={introduction}
          handleNavigationCallback={handleIntroductionNavigation}
        />
      );
    } else if (currentPage === pages.SIGN_IN_FLOW) {
      page = <></>;
    } else if (currentPage === pages.DEMOGRAPHIC) {
      page = (
        <EditDemographic
          title={clientInformationPageTitle}
          subtitle={clientInformationPageSubtitle}
          fields={fields}
          demographic={demographic}
          handleDemographicCreationCallback={handleDemographicNavigation}
          isMultipleIndividualEnabled={false}
          isConsentRequired={isConsentRequired}
          widgetType={widgetType}
          selfRegistration={selfRegistration}
          clientNotFoundPage={individualNotFoundPage}
          otpVerificationEnabled={otpVerificationEnabled}
          byRequestOnly={byRequestOnly}
          requestNotFoundPage={requestNotFoundPage}
        />
      );
    } else if (currentPage === pages.REGISTRATION_MESSAGE) {
      page = (
        <HeadingAndDescription
          headingAndDescriptionData={{ ...finalPage, buttonText: action?.actionButton }}
          handleNavigationCallback={() => handleRegisterMessageNavigation(actions.NEXT)}
          showNextButton={action?.enabled && !isActionPerformed}
        />
      );
    } else if (currentPage === pages.ACTION) {
      page = (
        <Action
          matchedAction={action?.actionConditions?.find((condition) => condition?.default)}
          handleNavigationCallback={handleActionNavigation}
          demographic={demographic}
          setIsActionPerformed={setIsActionPerformed}
        />
      );
    }

    return page;
  };

  return (
    <>
      <Loader
        active={
          isWidgetRegistrationDetailsFetching ||
          isSearchIndividualAtClientIndexFetching ||
          isCreateConnectionAtConnectionIndexFetching
        }
      />

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', px: { xs: 1, md: '4.5%' }, mt: 0.5, gap: 0 }}>
        <LanguageSelector availableLanguages={availableLanguages} onLanguageChange={handleLanguageChange} />

        {showSignIn && (
          <SignInSignUp
            isSignInDialogOpen={isSignInDialogOpen}
            handleSignInDialogState={handleSignInDialogState}
            signInHeading={introduction?.heading}
            signUpHeading={introduction?.heading}
            buttonLabel={t('next')}
            widgetType={REGISTRATION_WIDGET}
            identification={identification}
          />
        )}
      </Box>
      <Box>{widgetRegistrationDetailsSuccessData && <Box sx={{ mx: 'auto', p: 2 }}>{getPage()}</Box>}</Box>
    </>
  );
};
