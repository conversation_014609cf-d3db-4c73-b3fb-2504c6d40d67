import { Box, Button, Stack, Typography } from '@mui/material';
import Image from 'next/image';
import { useTranslation } from 'react-i18next';
import { ConsentAgreement, Loader } from '..';
import { useState } from 'react';
import cambianFullLogo from '@/asset/images/CambianFullLogo.png';
import useNotification from '@/hooks/useNotification';
import { useSelector } from 'react-redux';
import { useEffect } from 'react';
import { DEIDENTIFIED } from '@/containers/commonConstants';
import { useCommonAPIs } from '@/hooks/useCommonAPIs';

export const RegisterScreen = (props) => {
  const { t } = useTranslation();
  const { makeConnectionAtClientIndex, makeConnectionAtConnectionIndex } = useCommonAPIs();
  const { heading, description, userDetails, buttonText, handleNavigationCallback, identification, demographic } =
    props;
  const [, sendNotification] = useNotification();
  const { firstName, lastName } = userDetails;

  const [isConsented, setIsConsented] = useState(false);

  const {
    isCreateConnectionAtClientIndexFetching,
    createConnectionAtClientIndexSuccess,
    createConnectionAtClientIndexSuccessData,
    createConnectionAtClientIndexError,
  } = useSelector((state) => state.createConnectionAtClientIndexReducer) || {};

  const {
    isCreateConnectionAtConnectionIndexFetching,
    createConnectionAtConnectionIndexSuccess,
    createConnectionAtConnectionIndexError,
  } = useSelector((state) => state.createConnectionAtConnectionIndexReducer) || {};

  const { checkExistingConnectionIndexErrorData, checkExistingConnectionIndexSuccessData } =
    useSelector((state) => state.checkExistingConnectionIndexReducer) || {};
  const { checkExistingClientIndexErrorData, checkExistingClientIndexSuccessData } =
    useSelector((state) => state.checkExistingClientIndexReducer) || {};

  useEffect(() => {
    if (createConnectionAtClientIndexSuccess && createConnectionAtClientIndexSuccessData) {
      if (!checkExistingConnectionIndexSuccessData?.aliasId) {
        makeConnectionAtConnectionIndex();
      } else {
        if (checkExistingConnectionIndexSuccessData?.aliasId === createConnectionAtClientIndexSuccessData?.clientId) {
          handleNavigation();
        } else {
          if (identification === DEIDENTIFIED) makeConnectionAtConnectionIndex();
        }
      }
    } else if (createConnectionAtClientIndexError) {
      sendNotification({ variant: 'error', msg: t('someErrorOccurred') });
    }
  }, [createConnectionAtClientIndexSuccess, createConnectionAtClientIndexError]);

  useEffect(() => {
    if (createConnectionAtConnectionIndexSuccess) {
      handleNavigation();
    } else if (createConnectionAtConnectionIndexError) {
      sendNotification({ variant: 'error', msg: t('someErrorOccurred') });
    }
  }, [createConnectionAtConnectionIndexSuccess, createConnectionAtConnectionIndexError]);

  const handleNavigation = () => {
    handleNavigationCallback && handleNavigationCallback();
  };

  const handleConsentChange = () => {
    setIsConsented((prevValue) => !prevValue);
  };

  const handleRegister = () => {
    console.log('handle register clicked');
    if (
      checkExistingConnectionIndexErrorData?.statusCode === 404 &&
      checkExistingClientIndexErrorData?.statusCode === 404
    ) {
      makeConnectionAtClientIndex({ identification, demographic });
    } else if (
      checkExistingConnectionIndexErrorData?.statusCode === 404 &&
      checkExistingClientIndexSuccessData?.clientId
    ) {
      makeConnectionAtConnectionIndex();
    } else if (
      checkExistingConnectionIndexSuccessData?.aliasId &&
      checkExistingClientIndexErrorData?.statusCode === 404
    ) {
      makeConnectionAtClientIndex({ identification, demographic });
    } else if (checkExistingConnectionIndexErrorData?.statusCode === 404) {
      makeConnectionAtClientIndex({ identification, demographic });
    }
  };

  return (
    <>
      <Loader active={isCreateConnectionAtClientIndexFetching || isCreateConnectionAtConnectionIndexFetching} />
      <Box sx={{ minHeight: '30vh' }}>
        <Image src={cambianFullLogo} width="200" height="50" alt={<Typography variant="h6">Cambian</Typography>} />
        <Stack spacing={5} sx={{ mt: 2, mb: 1 }}>
          <Box>
            <Typography variant="h5" sx={{ mb: 3 }}>
              {heading}
            </Typography>
            <Typography variant="body2" sx={{ mt: 2, mb: 1 }}>
              {firstName} {lastName}
            </Typography>
            {description && (
              <Typography variant="body1" sx={{ mb: 3 }} dangerouslySetInnerHTML={{ __html: description }} />
            )}
          </Box>
          {handleNavigationCallback && (
            <Button variant="contained" onClick={handleRegister} disabled={!isConsented}>
              {buttonText?.trim() || t('registerAndConnect')}
            </Button>
          )}
        </Stack>
        {handleNavigationCallback && (
          <ConsentAgreement
            isConsented={isConsented}
            handleConsentChange={handleConsentChange} /* consentAgreementPdf */
          />
        )}
      </Box>
    </>
  );
};
