/*
 * WIDGET_DETAILS action types
 */

export const USER_INFO_REQUEST = 'USER_INFO_REQUEST';
export const USER_INFO_SUCCESS = 'USER_INFO_SUCCESS';
export const USER_INFO_ERROR = 'USER_INFO_ERROR';
export const USER_INFO_RESET = 'USER_INFO_RESET';

/*
 * action creators
 */

export function fetchUserInfo(data) {
  return {
    type: USER_INFO_REQUEST,
    payload: data,
  };
}

export function fetchActionUserInfoSuccess(data) {
  return {
    type: USER_INFO_SUCCESS,
    payload: data,
  };
}

export function fetchActionUserInfoError() {
  return {
    type: USER_INFO_ERROR,
  };
}

export const resetUserInfo = () => {
  return {
    type: USER_INFO_RESET,
  };
};
