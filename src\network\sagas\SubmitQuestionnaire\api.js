/*
 *
 */

import { makeNetworkCall } from '../..';
import { BASE_URL, QUESTIONNAIRE_SUBMIT_ANSWERS } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiFetchQuestionnaireSubmitAnswers(action) {
  const { payload = {} } = action || {};
  const { headers = {}, questionnaireData = {} } = payload || {};
  const mainUrl = BASE_URL + QUESTIONNAIRE_SUBMIT_ANSWERS;
  const config = {
    method: 'POST',
    url: mainUrl,
    headers: headers,
    data: questionnaireData,
  };
  return makeNetworkCall(config);
}
