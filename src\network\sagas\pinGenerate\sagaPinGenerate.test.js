import { runSaga } from 'redux-saga';
import * as api from './api';
import { pinGenerate } from './index';
import * as actions from '../../../redux/actions/pinGenerate/index';
import { API_RESPONSE_SUCCESS } from '../../../utils/constants/apiCodes';

/**
 * This function test test case for get  PIN Generation details saga
 * Fires get  PIN Generation success of api gives success
 * Fires get  PIN Generation error of api fails
 */

describe('Assign Questionnaire', () => {
  it('Should call api and dispatch success action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const initialState = {
      error: null,
      type: '',
      payload: null,
      accessToken: null,
    };

    const dispatchedActions = [];
    api.makeApiFetchPinGenerate = jest.fn(() => Promise.resolve(DUMMY_ITEM));

    const fakeStore = {
      getState: () => initialState,
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.pinGenerationRequest(DUMMY_ITEM.data);
    await runSaga(fakeStore, pinGenerate, requestResult).done;

    let successResult = actions.pinGenerationSuccess(DUMMY_ITEM.data);

    expect(api.makeApiFetchPinGenerate.mock.calls.length).toBe(1);
    expect(dispatchedActions).toContainEqual(successResult);
  });

  it('Should call api and dispatch error action', async () => {
    const DUMMY_ITEM = {
      status: API_RESPONSE_SUCCESS,
      data: {},
    };
    const dispatchedActions = [];
    api.makeApiFetchPinGenerate = jest.fn(() => Promise.reject());

    const fakeStore = {
      getState: () => ({ nextPage: 1 }),
      dispatch: (action) => dispatchedActions.push(action),
    };

    // wait for saga to complete
    let requestResult = actions.pinGenerationRequest(DUMMY_ITEM.data);
    await runSaga(fakeStore, pinGenerate, requestResult).done;

    expect(api.makeApiFetchPinGenerate.mock.calls.length).toBe(1);

    let errorResult = actions.pinGenerationError(DUMMY_ITEM.data);
    expect(dispatchedActions).toContainEqual(errorResult);
  });
});
