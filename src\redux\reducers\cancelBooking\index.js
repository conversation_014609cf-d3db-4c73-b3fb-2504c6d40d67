/*
 *
 */

import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';

import defaultAction from '../../actions';
import {
  CA<PERSON><PERSON>_BOOKING_ERROR,
  CANCEL_BOOKING_REQUEST,
  CANCEL_BOOKING_SUCCESS,
  CANCEL_BOOKING_RESET,
} from '../../actions/cancelBooking';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const cancelBookingReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case CANCEL_BOOKING_REQUEST:
      return {
        ...state,
        cancelBookingFetching: true,
        cancelBookingSuccess: false,
        cancelBookingError: false,
        cancelBookingErrorData: null,
      };
    case CANCEL_BOOKING_SUCCESS: {
      return {
        ...state,
        cancelBookingFetching: false,
        cancelBookingSuccess: true,
        cancelBookingError: false,
        cancelBookingErrorData: null,
        cancelBookingSuccessData: payload,
      };
    }
    case CANCEL_BOOKING_ERROR:
      return {
        ...state,
        cancelBookingFetching: false,
        cancelBookingSuccess: false,
        cancelBookingError: true,
        cancelBookingErrorData: payload,
      };
    case CANCEL_BOOKING_RESET:
      return {
        ...state,
        cancelBookingFetching: false,
        cancelBookingSuccess: false,
        cancelBookingError: true,
        cancelBookingErrorData: null,
        cancelBookingSuccessData: null,
      };
    default:
      return state;
  }
};
