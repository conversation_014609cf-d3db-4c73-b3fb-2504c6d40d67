import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from '../globals';
import defaultAction from '../../actions';
import {
  GET_QUESTIONNAIRE_WIDGET_REQUEST,
  GET_QUESTIONNAIRE_WIDGET_SUCCESS,
  GET_QUESTIONNAIRE_WIDGET_ERROR,
} from '../../actions/getQuestionnaireWidget';

export const initialState = {
  ...GLOBALS,
  error: null,
  type: '',
  payload: null,
  accessToken: null,
};

export const questionnaireWidgetReducer = (state = initialState, action = defaultAction) => {
  const { type, payload } = action;
  switch (type) {
    case GET_QUESTIONNAIRE_WIDGET_REQUEST:
      return {
        ...state,
        isQuestionnaireWidgetFetching: true,
        isQuestionnaireWidgetSuccess: false,
        isQuestionnaireWidgetError: false,
        questionnaireWidgetErrorData: null,
      };
    case GET_QUESTIONNAIRE_WIDGET_SUCCESS: {
      return {
        ...state,
        isQuestionnaireWidgetFetching: false,
        isQuestionnaireWidgetSuccess: true,
        isQuestionnaireWidgetError: false,
        questionnaireWidgetErrorData: null,
        questionnaireWidgetSuccessData: payload,
      };
    }
    case GET_QUESTIONNAIRE_WIDGET_ERROR:
      return {
        ...state,
        isQuestionnaireWidgetFetching: false,
        isQuestionnaireWidgetSuccess: false,
        isQuestionnaireWidgetError: true,
        questionnaireWidgetErrorData: payload,
      };
    default:
      return state;
  }
};
