import { CONFIRM_APPOINTMENT } from '@/utils/constants/awsApiEndpoints';
import { makeNetworkCall } from '../..';
import { APPOINTMENT_ID, ORGANIZATION_ID } from '../../../utils/constants';

export function confirmAppointmentApi(action) {
  const { payload = {} } = action || {};
  const { headers = {}, appointmentId, organizationId } = payload || {};

  const URL = CONFIRM_APPOINTMENT.replace(ORGANIZATION_ID, organizationId).replace(APPOINTMENT_ID, appointmentId);

  const config = {
    method: 'POST',
    url: URL,
    headers: headers,
  };

  return makeNetworkCall(config);
}
