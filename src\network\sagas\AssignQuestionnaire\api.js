/*
 *
 */

import { makeNetworkCall } from '../..';
import { ORGANIZATION_ID } from '../../../utils/constants';
import { BASE_URL, ASSIGN_QUESTIONNAIRE } from '../../../utils/constants/apiEndpoints';

/**
 *
 * @param {*} action
 */
/**
 *
 * @param {*} action
 */

export function makeApiFetchAssignQuestionnaire(action) {
  const { payload = {} } = action || {};
  const { headers = {}, assignData, organizationId } = payload || {};
  const mainUrl = BASE_URL + ASSIGN_QUESTIONNAIRE.replace(ORGANIZATION_ID, organizationId);
  const config = {
    method: 'POST',
    url: mainUrl,
    headers: headers,
    data: assignData,
  };
  return makeNetworkCall(config);
}
