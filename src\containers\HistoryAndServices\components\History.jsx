import { Box, Paper, Stack, Grid, Typography, Divide<PERSON>, Button } from '@mui/material';
import { formatDate } from '../../../utils/helpers/date-time';
import { actions } from '../../commonConstants';
import {
  getPatientDataFromSearchAndCreate,
  getAppointmentsDataFromSearchAndCreatePatient,
  getVaccinationHistory,
  sortVaccineHistory,
  getAppointments,
} from '../utility';
import { useTranslation } from 'react-i18next';

export const History = (props) => {
  const { patientData, handleNavigationCallback } = props;
  const { t } = useTranslation();
  const personalData = getPatientDataFromSearchAndCreate(patientData);
  const appointmentData = getAppointmentsDataFromSearchAndCreatePatient(patientData);
  const vaccinationHistory = getVaccinationHistory(patientData);

  const personalDetailsField = ['Individuals', 'Date of Birth', 'Health Card Number'];
  const personalDetails = [
    {
      Individuals: `${personalData?.individuals[0].firstName} ${personalData?.individuals[0].lastName}`,
      'Date of Birth': formatDate(personalData?.individuals[0].dateOfBirth),
      'Health Card Number': personalData?.individuals[0].healthCardNumber,
    },
  ];

  const nextAppointments = getAppointments(appointmentData);

  return (
    <Box>
      <Paper sx={{ p: 2 }}>
        <Grid container>
          <Grid item xs={11}>
            {personalDetailsField.map((field, index) => (
              <Grid container alignItems="center" key={index} sx={{ mt: 1.5 }}>
                <Grid item xs={5} sm={3} md={2.5}>
                  {field}
                </Grid>
                <Grid item xs={7} sm={9} md={9.5}>
                  {personalDetails[0][field]}
                </Grid>
              </Grid>
            ))}
          </Grid>
          <Grid item xs={1} sx={{ textAlign: 'right' }}>
            <Button
              variant="text"
              sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' } }}
              onClick={() => handleNavigationCallback(actions.PREVIOUS)}
            >
              {t('edit')}
            </Button>
          </Grid>
        </Grid>
        {vaccinationHistory.length ? (
          <>
            <Divider orientation="horizontal" flexItem sx={{ my: 2 }} />
            <Grid container>
              <Grid item sm={12}>
                <Typography variant="subtitle2" sx={{ textDecoration: 'underline' }}>
                  {t('vaccinationHistory')}
                </Typography>
              </Grid>
              {vaccinationHistory.sort(sortVaccineHistory).map((item, index) => (
                <Grid container key={index} sx={{ mt: 1.5 }}>
                  <Grid item xs={3} sm={3} md={2}>
                    {t('dose')} {++index}
                  </Grid>
                  <Grid item xs={3} sm={3} md={2}>
                    {item.doseName}
                  </Grid>
                  <Grid item xs={6} sm={3} md={2} sx={{ pl: 1 }}>
                    {formatDate(item.doseDate)}
                  </Grid>
                </Grid>
              ))}
            </Grid>
          </>
        ) : (
          <></>
        )}
        {nextAppointments.length ? (
          <>
            <Divider orientation="horizontal" flexItem sx={{ my: 2 }} />
            <Grid container>
              <Grid item sm={12}>
                <Typography variant="subtitle2" sx={{ textDecoration: 'underline' }}>
                  {t('upcomingAppointments')}
                </Typography>
              </Grid>
              {nextAppointments.map((item, index) => (
                <Grid container justifyContent="space-between" key={index} sx={{ py: 1 }}>
                  <Grid item container sm={9}>
                    <Grid item sm={6} md={5} lg={4}>
                      <Stack direction="column">
                        <Typography variant="caption" sx={{ color: '#c7c7c7' }}>
                          {item.date}, {item.time}
                        </Typography>
                        <Typography>
                          <strong>{item.service}</strong>
                        </Typography>
                      </Stack>
                    </Grid>
                    <Grid item sm={6} md={4}>
                      <Typography>{item.clinic}</Typography>
                    </Grid>
                  </Grid>
                  <Grid item sm={3}>
                    <Stack direction="row" justifyContent="right" spacing={2} sx={{ mt: -1 }}>
                      <Button
                        variant="text"
                        sx={{
                          px: { xs: 2, sm: 3.5, md: 1 },
                          '&:hover': { backgroundColor: 'transparent', color: '#23527c' },
                        }}
                      >
                        {t('reschedule')}
                      </Button>
                      <Button
                        variant="text"
                        sx={{
                          px: { xs: 2, sm: 3.5, md: 1 },
                          '&:hover': { backgroundColor: 'transparent', color: '#23527c' },
                        }}
                      >
                        {t('cancel')}
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              ))}
            </Grid>
          </>
        ) : (
          <></>
        )}
      </Paper>
    </Box>
  );
};
