import { NextResponse } from 'next/server';
import { setOrgMachineAccessTokenInCookie } from '@/containers/auth/cognito';
/**
 * @param {Request} req
 * @returns {Promise<void>}
 */
const getEndpoint = async (req, { params: { env } }) => {
  const [res, error] = await setOrgMachineAccessTokenInCookie(env);
  if (error) {
    return error.toNextResponse();
  }
  return NextResponse.json(
    { accessToken: res.accessToken },
    {
      status: 200,
    },
  );
};

export const GET = getEndpoint;
