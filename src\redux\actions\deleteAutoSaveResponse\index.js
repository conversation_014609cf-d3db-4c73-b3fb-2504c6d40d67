/*
 * POST Questions action types
 */

export const DELETE_AUTO_SAVE_RESPONSE_REQUEST = 'DELETE_AUTO_SAVE_RESPONSE_REQUEST';
export const DELETE_AUTO_SAVE_RESPONSE_SUCCESS = 'DELETE_AUTO_SAVE_RESPONSE_SUCCESS';
export const DELETE_AUTO_SAVE_RESPONSE_ERROR = 'DELETE_AUTO_SAVE_RESPONSE_ERROR';
export const DELETE_AUTO_SAVE_RESPONSE_RESET = 'DELETE_AUTO_SAVE_RESPONSE_RESET';
/*
 * action creators
 */

export function deleteAutoSaveResponse(data) {
  return {
    type: DELETE_AUTO_SAVE_RESPONSE_REQUEST,
    payload: data,
  };
}

export function deleteAutoSaveResponseSuccess(data) {
  return {
    type: DELETE_AUTO_SAVE_RESPONSE_SUCCESS,
    payload: data,
  };
}

export function deleteAutoSaveResponseError() {
  return {
    type: DELETE_AUTO_SAVE_RESPONSE_ERROR,
  };
}

export function deleteAutoSaveResponseReset() {
  return {
    type: DELETE_AUTO_SAVE_RESPONSE_RESET,
  };
}
